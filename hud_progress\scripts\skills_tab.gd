extends Control

@onready var tab_container: HBoxContainer = $CanvasLayer/All_Tab_Button
@onready var nhiem_vu_button: TextureButton = $CanvasLayer/All_Tab_Button/Mission_Button
@onready var hanh_trang_button: TextureButton = $CanvasLayer/All_Tab_Button/Inventory_Button
@onready var ky_nang_button: TextureButton = $CanvasLayer/All_Tab_Button/Skills_Button
@onready var khac_button: TextureButton = $CanvasLayer/All_Tab_Button/Etc_Button
@onready var close_button: TextureButton = $CanvasLayer/Close_Button

func _ready() -> void:
	# Kiểm tra xem các node có được tìm thấy không
	if not tab_container:
		push_error("HBoxContainer not found at path: CanvasLayer/All_Tab_Button")
		return
	if not nhiem_vu_button:
		push_error("Mission_Button not found at path: CanvasLayer/All_Tab_Button/Mission_Button")
		return
	if not hanh_trang_button:
		push_error("Inventory_Button not found at path: CanvasLayer/All_Tab_Button/Inventory_Button")
		return
	if not ky_nang_button:
		push_error("Skills_Button not found at path: CanvasLayer/All_Tab_Button/Skills_Button")
		return
	if not khac_button:
		push_error("Etc_Button not found at path: CanvasLayer/All_Tab_Button/Etc_Button")
		return
	if not close_button:
		push_error("Close_Button not found at path: CanvasLayer/Close_Button")
		return

	# Kết nối tín hiệu pressed của từng nút
	nhiem_vu_button.connect("pressed", _on_nhiem_vu_button_pressed)
	hanh_trang_button.connect("pressed", _on_hanh_trang_button_pressed)
	ky_nang_button.connect("pressed", _on_ky_nang_button_pressed)
	khac_button.connect("pressed", _on_khac_button_pressed)
	close_button.connect("pressed", _on_close_button_pressed)

func _on_ky_nang_button_pressed() -> void:
	# Đã ở skills_tab.tscn, không cần chuyển scene
	pass

func _on_hanh_trang_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/inventory_tab.tscn")

func _on_nhiem_vu_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/missions_tab.tscn")

func _on_khac_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/etc_tab.tscn")

func _on_close_button_pressed() -> void:
	queue_free()  # Xóa node Missions_Tab khỏi Scene Tree
