[gd_scene load_steps=3 format=3 uid="uid://cfcn61lofywoa"]

[ext_resource type="Texture2D" uid="uid://dpfo7ey164plh" path="res://assets/images/background/screens/loading_screen.png" id="2_gerbl"]

[sub_resource type="GDScript" id="GDScript_i8eqf"]
script/source = "# extends Node2D


# # Called when the node enters the scene tree for the first time.
# func _ready() -> void:
# 	pass # Replace with function body.


# # Called every frame. 'delta' is the elapsed time since the previous frame.
# func _process(delta: float) -> void:
# 	pass

extends Node2D

@onready var loading_scene = preload(\"res://Home/scenes/Loading.tscn\")

func load_scene(current_scene, next_scene):
	# add loading scene to the root
	var loading_scene_instance = loading_scene.instantiate()
	get_tree().get_root().call_deferred(\"add_child\", loading_scene_instance)
	
	# request the resource load
	ResourceLoader.load_threaded_request(next_scene)
	
	current_scene.queue_free()
	# creating a little delay, that lets the loading screen to appear
	await get_tree().create_timer(0.5).timeout
	
	# loading the next_scene
	while true:
		var status = ResourceLoader.load_threaded_get_status(next_scene)
		
		if status == ResourceLoader.THREAD_LOAD_IN_PROGRESS:
			# update the progress bar
			var progress_bar = loading_scene_instance.get_node(\"ProgressBar\")
			progress_bar.value = (progress_bar.value + 1) % 100
		
		elif status == ResourceLoader.THREAD_LOAD_LOADED:
			# get the loaded resource
			var scene = ResourceLoader.load_threaded_get(next_scene).instantiate()
			get_tree().get_root().call_deferred(\"add_child\", scene)
			loading_scene_instance.queue_free()
			return
		
		elif status == ResourceLoader.THREAD_LOAD_FAILED:
			# loading failed
			print(\"error occurred while loading scene\")
			return
			
		await get_tree().process_frame
"

[node name="LoadingPage" type="Node2D"]

[node name="TextureRect" type="TextureRect" parent="."]
offset_left = 4.0
offset_top = 4.0
offset_right = 1151.0
offset_bottom = 639.0
texture = ExtResource("2_gerbl")
expand_mode = 1

[node name="Label" type="Label" parent="."]
offset_left = 528.0
offset_top = 446.0
offset_right = 629.0
offset_bottom = 482.0
theme_override_font_sizes/font_size = 20
text = "Đang Tải..."
script = SubResource("GDScript_i8eqf")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
root_node = NodePath("")

[node name="ProgressBar" type="ProgressBar" parent="."]
offset_left = 230.0
offset_top = 506.0
offset_right = 963.0
offset_bottom = 533.0
