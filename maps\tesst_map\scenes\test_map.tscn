[gd_scene load_steps=15 format=4 uid="uid://lw6o3k6o8ssm"]

[ext_resource type="Texture2D" uid="uid://oax7heho3lva" path="res://assets/images/background/elements/background.png" id="1_w5v1c"]
[ext_resource type="Texture2D" uid="uid://wvt1m58lr618" path="res://assets/images/tiles/Tiles.png" id="2_atp6p"]
[ext_resource type="PackedScene" uid="uid://dk45l678rqkr1" path="res://enemy/scenes/Enemy.tscn" id="4_7org2"]
[ext_resource type="PackedScene" uid="uid://cf8kby3gw31o0" path="res://npcs/scenes/Npc.tscn" id="5_nx30e"]
[ext_resource type="PackedScene" uid="uid://bkn5seficjirh" path="res://player/player.tscn" id="6_4rgpu"]
[ext_resource type="SpriteFrames" uid="uid://f5epyyes21yh" path="res://npcs/skins/lang_bam.tres" id="6_7wglg"]
[ext_resource type="SpriteFrames" uid="uid://cqffh68kyx6by" path="res://npcs/skins/tho_ren.tres" id="7_e74pn"]
[ext_resource type="PackedScene" uid="uid://7ohdh0u4q5rd" path="res://allies/scenes/Ally.tscn" id="8_sejc3"]
[ext_resource type="SpriteFrames" uid="uid://cbujhuaiqxofd" path="res://allies/skins/bow_ally.tres" id="9_m26fe"]
[ext_resource type="SpriteFrames" uid="uid://bs8fu4h73w2kl" path="res://allies/skins/sword_ally.tres" id="10_lvl63"]
[ext_resource type="PackedScene" uid="uid://cytofbyao3jum" path="res://animals/scenes/Animal.tscn" id="11_fpab8"]

[sub_resource type="GDScript" id="GDScript_vf13u"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_k20lf"]
texture = ExtResource("2_atp6p")
0:0/size_in_atlas = Vector2i(5, 5)
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-37.5, -12.5, -37.5, -25, 12.5, -25, 25, -25, 37.5, -25, 37.5, 25, 37.5, 37.5, -37.5, 37.5)
0:5/size_in_atlas = Vector2i(5, 5)
0:5/0 = 0
0:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-37.5, -25, 37.5, -25, 37.5, 37.5, -37.5, 37.5)
17:0/size_in_atlas = Vector2i(8, 3)
17:0/0 = 0
17:3/size_in_atlas = Vector2i(8, 3)
17:3/0 = 0
17:6/size_in_atlas = Vector2i(8, 3)
17:6/0 = 0
17:9/size_in_atlas = Vector2i(8, 3)
17:9/0 = 0
17:12/size_in_atlas = Vector2i(8, 3)
17:12/0 = 0
6:10/size_in_atlas = Vector2i(3, 3)
6:10/0 = 0
15:15/0 = 0
15:16/0 = 0
16:15/size_in_atlas = Vector2i(2, 2)
16:15/0 = 0
18:15/size_in_atlas = Vector2i(2, 2)
18:15/0 = 0
20:15/size_in_atlas = Vector2i(1, 2)
20:15/0 = 0
21:15/size_in_atlas = Vector2i(1, 2)
21:15/0 = 0
22:15/size_in_atlas = Vector2i(1, 2)
22:15/0 = 0
23:19/0 = 0
24:19/0 = 0
24:20/0 = 0
23:20/0 = 0
22:20/0 = 0
22:19/0 = 0
21:19/0 = 0
21:20/0 = 0
19:19/size_in_atlas = Vector2i(2, 3)
19:19/0 = 0
17:19/size_in_atlas = Vector2i(2, 3)
17:19/0 = 0
15:20/0 = 0
15:17/size_in_atlas = Vector2i(1, 3)
15:17/0 = 0
16:17/0 = 0
16:18/size_in_atlas = Vector2i(1, 2)
16:18/0 = 0
16:20/size_in_atlas = Vector2i(1, 2)
16:20/0 = 0
15:21/0 = 0
15:22/0 = 0
15:23/0 = 0
15:24/0 = 0
16:24/0 = 0
16:23/0 = 0
16:22/0 = 0
17:22/0 = 0
17:23/0 = 0
17:24/0 = 0
18:24/0 = 0
18:23/0 = 0
18:22/0 = 0
19:22/0 = 0
19:23/0 = 0
19:24/0 = 0
20:22/size_in_atlas = Vector2i(2, 2)
20:22/0 = 0
18:17/size_in_atlas = Vector2i(2, 2)
18:17/0 = 0
20:17/size_in_atlas = Vector2i(2, 2)
20:17/0 = 0
22:17/0 = 0
17:17/size_in_atlas = Vector2i(1, 2)
17:17/0 = 0
0:21/size_in_atlas = Vector2i(5, 2)
0:21/0 = 0
5:21/size_in_atlas = Vector2i(5, 2)
5:21/0 = 0
10:21/size_in_atlas = Vector2i(5, 2)
10:21/0 = 0
3:17/size_in_atlas = Vector2i(3, 4)
3:17/0 = 0
6:18/size_in_atlas = Vector2i(4, 3)
6:18/0 = 0
0:17/size_in_atlas = Vector2i(3, 4)
0:17/0 = 0
0:14/size_in_atlas = Vector2i(3, 3)
0:14/0 = 0
4:13/size_in_atlas = Vector2i(2, 2)
4:13/0 = 0
4:15/size_in_atlas = Vector2i(5, 2)
4:15/0 = 0
6:13/size_in_atlas = Vector2i(4, 2)
6:13/0 = 0
9:15/size_in_atlas = Vector2i(1, 2)
9:15/0 = 0
10:14/size_in_atlas = Vector2i(1, 3)
10:14/0 = 0
11:14/size_in_atlas = Vector2i(3, 2)
11:14/0 = 0
10:12/size_in_atlas = Vector2i(3, 2)
10:12/0 = 0
10:10/size_in_atlas = Vector2i(5, 2)
10:10/0 = 0
9:10/size_in_atlas = Vector2i(1, 3)
9:10/0 = 0
0:10/size_in_atlas = Vector2i(2, 4)
0:10/0 = 0
2:10/size_in_atlas = Vector2i(2, 4)
2:10/0 = 0
4:10/size_in_atlas = Vector2i(2, 2)
4:10/0 = 0
5:7/size_in_atlas = Vector2i(5, 3)
5:7/0 = 0
5:5/size_in_atlas = Vector2i(3, 2)
5:5/0 = 0
8:4/size_in_atlas = Vector2i(2, 2)
8:4/0 = 0
5:0/size_in_atlas = Vector2i(4, 3)
5:0/0 = 0
5:3/size_in_atlas = Vector2i(2, 2)
5:3/0 = 0
7:3/size_in_atlas = Vector2i(1, 2)
7:3/0 = 0

[sub_resource type="TileSet" id="TileSet_7y3ec"]
tile_size = Vector2i(50, 50)
physics_layer_0/collision_layer = 16
physics_layer_0/collision_mask = 15
sources/0 = SubResource("TileSetAtlasSource_k20lf")

[node name="LangVanLang" type="Node2D"]
position = Vector2(-1109, -377)
script = SubResource("GDScript_vf13u")

[node name="Background" type="ParallaxBackground" parent="."]

[node name="BackgroundLayer" type="ParallaxLayer" parent="Background"]

[node name="BackgroundSprite" type="Sprite2D" parent="Background/BackgroundLayer"]
position = Vector2(10, -1)
scale = Vector2(9.22127, 7.73714)
texture = ExtResource("1_w5v1c")

[node name="GroundLayer" type="TileMapLayer" parent="."]
position = Vector2(1107, 374)
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_7y3ec")

[node name="Player" parent="." instance=ExtResource("6_4rgpu")]
z_index = 5
position = Vector2(-656, 1086)

[node name="Sword" parent="." instance=ExtResource("4_7org2")]
position = Vector2(-9, 947)

[node name="LangBam" parent="." instance=ExtResource("5_nx30e")]
position = Vector2(110, 1132)
sprites = ExtResource("6_7wglg")

[node name="ThoRen" parent="." instance=ExtResource("5_nx30e")]
position = Vector2(30, 1129)
sprites = ExtResource("7_e74pn")

[node name="Spear" parent="." instance=ExtResource("4_7org2")]
position = Vector2(-880, 1051)

[node name="Bow" parent="." instance=ExtResource("4_7org2")]
position = Vector2(197, 1173)

[node name="SpearAlly" parent="." node_paths=PackedStringArray("player_target") instance=ExtResource("8_sejc3")]
position = Vector2(-193, 1152)
peak_duration = null
acceleration = null
follow_distance = null
attack_range = null
attack_cooldown = null
sprites = null
player_target = NodePath("../Player")

[node name="BowAlly" parent="." node_paths=PackedStringArray("player_target") instance=ExtResource("8_sejc3")]
position = Vector2(-5, 1153)
peak_duration = null
acceleration = null
follow_distance = null
attack_range = 200.0
attack_cooldown = null
sprites = ExtResource("9_m26fe")
player_target = NodePath("../Player")

[node name="SwordAlly" parent="." node_paths=PackedStringArray("player_target") instance=ExtResource("8_sejc3")]
position = Vector2(212, 1103)
peak_duration = null
acceleration = null
follow_distance = null
attack_range = null
attack_cooldown = null
sprites = ExtResource("10_lvl63")
player_target = NodePath("../Player")

[node name="Animal" parent="." instance=ExtResource("11_fpab8")]
position = Vector2(479, 1143)

[node name="Animal2" parent="." instance=ExtResource("11_fpab8")]
position = Vector2(375, 1145)
type = "snail"

[node name="Animal3" parent="." instance=ExtResource("11_fpab8")]
position = Vector2(329, 1139)
type = "bee"

[node name="Boss" parent="." instance=ExtResource("4_7org2")]
position = Vector2(652, 1086)
type = "boss"
