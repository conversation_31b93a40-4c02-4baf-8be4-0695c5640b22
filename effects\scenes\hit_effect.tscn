[gd_scene load_steps=2 format=3 uid="uid://c8yvnxvnqnvxs"]

[ext_resource type="Script" path="res://effects/scripts/hit_effect.gd" id="1_yvnxs"]

[node name="HitEffect" type="Node2D"]
script = ExtResource("1_yvnxs")

[node name="CPUParticles2D" type="CPUParticles2D" parent="."]
emitting = false
amount = 15
lifetime = 0.5
one_shot = true
explosiveness = 1.0
emission_shape = 1
emission_sphere_radius = 5.0
direction = Vector2(0, -1)
spread = 60.0
gravity = Vector2(0, 200)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
scale_amount_min = 2.0
scale_amount_max = 4.0
color = Color(1, 0.5, 0.5, 1)
