# Test script for GlobalMapNameUI top-right positioning
# Place this script on a Node in any scene to test the new positioning
extends Node

var test_maps = ["lang_van_lang", "dong_dau", "rung_nuong", "hang_an", "doi_tre", "suoi_thieng"]
var current_test_index = 0

func _ready():
	print("🧪 === MAP NAME UI POSITIONING TEST ===")
	print("Press F1 to test all map names")
	print("Press F2 to test responsive positioning")
	print("Press F3 to show debug info")
	print("Press F4 to cycle through individual maps")
	print("=======================================")

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				test_all_map_names()
			KEY_F2:
				test_responsive_positioning()
			KEY_F3:
				GlobalMapNameUI.debug_info()
			KEY_F4:
				cycle_through_maps()

func test_all_map_names():
	print("\n🧪 Testing all Vietnamese map names...")
	
	for i in range(test_maps.size()):
		var map_id = test_maps[i]
		print("Testing map: %s" % map_id)
		
		# Set and show the map name
		GlobalMapNameUI.set_map_name(map_id)
		GlobalMapNameUI.show_map_name()
		
		# Wait for display + fade duration
		await get_tree().create_timer(4.0).timeout
	
	print("✅ All map names tested!")

func test_responsive_positioning():
	print("\n🧪 Testing responsive positioning...")
	
	# Show a test map name
	GlobalMapNameUI.set_map_name("lang_van_lang")
	GlobalMapNameUI.show_map_name()
	
	# Get current viewport
	var viewport = get_viewport()
	var original_size = viewport.get_visible_rect().size
	
	print("Original screen size: %s" % original_size)
	print("Original position: %s" % GlobalMapNameUI.background_panel.position)
	
	# Test different screen sizes (simulated)
	var test_sizes = [
		Vector2(1920, 1080),  # Full HD
		Vector2(1280, 720),   # HD
		Vector2(800, 600),    # Small
		Vector2(2560, 1440)   # 2K
	]
	
	for size in test_sizes:
		print("\nSimulating screen size: %s" % size)
		
		# Manually calculate what the position should be
		var expected_x = size.x - GlobalMapNameUI.panel_width - GlobalMapNameUI.margin
		var expected_y = GlobalMapNameUI.margin
		var expected_pos = Vector2(expected_x, expected_y)
		
		print("Expected position: %s" % expected_pos)
		
		# Force update position (simulating screen resize)
		GlobalMapNameUI.force_top_right_position()
		
		await get_tree().create_timer(1.0).timeout
	
	print("✅ Responsive positioning test complete!")

func cycle_through_maps():
	print("\n🧪 Cycling through map: %s" % test_maps[current_test_index])
	
	# Set and show current map
	GlobalMapNameUI.set_map_name(test_maps[current_test_index])
	GlobalMapNameUI.show_map_name()
	
	# Move to next map
	current_test_index = (current_test_index + 1) % test_maps.size()

func verify_positioning():
	"""Verify that the positioning is correct"""
	print("\n🔍 Verifying positioning...")
	
	if not GlobalMapNameUI.background_panel:
		print("❌ Background panel not found!")
		return false
	
	var screen_size = get_viewport().get_visible_rect().size
	var panel_pos = GlobalMapNameUI.background_panel.position
	var panel_size = GlobalMapNameUI.background_panel.size
	
	# Check if panel is in top-right area
	var expected_x = screen_size.x - panel_size.x - GlobalMapNameUI.margin
	var expected_y = GlobalMapNameUI.margin
	
	var position_correct = abs(panel_pos.x - expected_x) < 5 and abs(panel_pos.y - expected_y) < 5
	
	if position_correct:
		print("✅ Position is correct: %s" % panel_pos)
		print("   Expected: %s" % Vector2(expected_x, expected_y))
		print("   Screen size: %s" % screen_size)
		return true
	else:
		print("❌ Position is incorrect!")
		print("   Current: %s" % panel_pos)
		print("   Expected: %s" % Vector2(expected_x, expected_y))
		print("   Screen size: %s" % screen_size)
		return false

func verify_vietnamese_names():
	"""Verify that Vietnamese names are displayed correctly"""
	print("\n🔍 Verifying Vietnamese translations...")
	
	var translations = GlobalMapNameUI.map_name_translations
	var all_correct = true
	
	for map_id in test_maps:
		if translations.has(map_id):
			var vietnamese_name = translations[map_id]
			print("✅ %s → %s" % [map_id, vietnamese_name])
		else:
			print("❌ Missing translation for: %s" % map_id)
			all_correct = false
	
	if all_correct:
		print("✅ All Vietnamese translations are available!")
	else:
		print("❌ Some translations are missing!")
	
	return all_correct

func run_full_test():
	"""Run a comprehensive test of all features"""
	print("\n🧪 === RUNNING FULL TEST SUITE ===")
	
	# Test 1: Positioning
	print("\n1. Testing positioning...")
	var pos_ok = verify_positioning()
	
	# Test 2: Vietnamese names
	print("\n2. Testing Vietnamese translations...")
	var names_ok = verify_vietnamese_names()
	
	# Test 3: Display functionality
	print("\n3. Testing display functionality...")
	GlobalMapNameUI.set_map_name("lang_van_lang")
	GlobalMapNameUI.show_map_name()
	await get_tree().create_timer(1.0).timeout
	var display_ok = GlobalMapNameUI.is_showing()
	
	# Results
	print("\n📊 === TEST RESULTS ===")
	print("Positioning: %s" % ("✅ PASS" if pos_ok else "❌ FAIL"))
	print("Vietnamese Names: %s" % ("✅ PASS" if names_ok else "❌ FAIL"))
	print("Display Function: %s" % ("✅ PASS" if display_ok else "❌ FAIL"))
	
	var all_passed = pos_ok and names_ok and display_ok
	print("\nOverall: %s" % ("🎉 ALL TESTS PASSED!" if all_passed else "⚠️ SOME TESTS FAILED"))
	print("=======================")
	
	return all_passed
