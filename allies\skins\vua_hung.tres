[gd_resource type="SpriteFrames" load_steps=10 format=3 uid="uid://d1r8rbljif8hn"]

[ext_resource type="Texture2D" uid="uid://cecjvguebie2n" path="res://assets/images/characters/npcs/vua_hung .png" id="1_4dc7k"]

[sub_resource type="AtlasTexture" id="AtlasTexture_ethys"]
atlas = ExtResource("1_4dc7k")
region = Rect2(0, 0, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_o4ype"]
atlas = ExtResource("1_4dc7k")
region = Rect2(118, 0, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_q14u1"]
atlas = ExtResource("1_4dc7k")
region = Rect2(236, 0, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_musv1"]
atlas = ExtResource("1_4dc7k")
region = Rect2(354, 0, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_tl78l"]
atlas = ExtResource("1_4dc7k")
region = Rect2(0, 118, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_2xulw"]
atlas = ExtResource("1_4dc7k")
region = Rect2(118, 118, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_rsybu"]
atlas = ExtResource("1_4dc7k")
region = Rect2(236, 118, 118, 118)

[sub_resource type="AtlasTexture" id="AtlasTexture_nru4x"]
atlas = ExtResource("1_4dc7k")
region = Rect2(354, 118, 118, 118)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ethys")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o4ype")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q14u1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_musv1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tl78l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2xulw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rsybu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nru4x")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}]
