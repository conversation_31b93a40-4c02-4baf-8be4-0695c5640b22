[gd_scene load_steps=5 format=3 uid="uid://beu5of83qkniu"]

[ext_resource type="Texture2D" uid="uid://0ju2ybul33jl" path="res://assets/images/item/Weapon/ao.png" id="1_k051w"]

[sub_resource type="GDScript" id="GDScript_ljgqj"]
script/source = "# Gắn script này trực tiếp vào node gốc là RigidBody2D
extends RigidBody2D

@export var blink_start_offset: float = 2
@export var blink_interval: float = 0.15
@export var type: String = \"Armor\"
@export var SpawnTime: float =8.0

@onready var arrow: Sprite2D = $ArrowIndicator
@onready var icon: Sprite2D = $Icon
@onready var shape: CollisionShape2D = $CollisionShape2D
@onready var despawn_timer: Timer = $DespawnTimer
@onready var click_area: Area2D = $ClickArea

var initial_jump_velocity: float = -200.0
var initial_horizontal_spread: float = 50.0 # Vận tốc ngang tối đa ban đầu (ngẫu nhiên)
var is_blinking: bool = false
var blink_timer: float = 0.0 # Bộ đếm thời gian cho mỗi chu kỳ nhấp nháy

func _ready() -> void:
	if despawn_timer:
		# Đặt thời gian chờ bằng giá trị lifetime đã export
		despawn_timer.wait_time = SpawnTime
		# Đảm bảo Timer chỉ chạy một lần
		despawn_timer.one_shot = true
		# Kết nối tín hiệu (signal) \"timeout\" của Timer với hàm _on_despawn_timer_timeout
		# Dùng Callable(self, \"function_name\") là cách làm trong Godot 4
		# Thêm kiểm tra để tránh kết nối nhiều lần nếu bạn cũng kết nối qua Editor
		if not despawn_timer.is_connected(\"timeout\", Callable(self, \"_on_despawn_timer_timeout\")):
			despawn_timer.timeout.connect(_on_despawn_timer_timeout)
		# Bắt đầu đếm ngược
		despawn_timer.start()
	else:
		# In lỗi nếu không tìm thấy node Timer
		printerr(\"Node 'DespawnTimer' not found as a child of \", name, \". Item will not despawn automatically.\")
	# Tùy chọn: Có thể thêm một lực xoay nhẹ ban đầu nếu muốn item xoay khi rơi
	# apply_angular_impulse(randf_range(-10.0, 10.0)) # Ví dụ: áp dụng xung lực xoay ngẫu nhiên
	if arrow:
		arrow.visible = false
	else:
		printerr(\"Node 'ArrowIndicator' không tìm thấy!\")

	# --- FIX LỖI CHÍNH: THÊM KHỐI LỆNH KẾT NỐI SIGNAL CHO CLICK ---
	if click_area:
		click_area.input_pickable = true # Đảm bảo có thể click
		# Kết nối signal \"input_event\" của Area2D với hàm \"_on_item_clicked\"
		if not click_area.is_connected(\"input_event\", Callable(self, \"_on_item_clicked\")):
			click_area.input_event.connect(_on_item_clicked)
		print(\"DEBUG: Click Area signal connected in _ready.\") # Print để xác nhận
	else:
		printerr(\"Node 'ClickArea' not found!\")
func _on_despawn_timer_timeout() -> void:
	# Lệnh để xóa node này (và các con của nó) khỏi Scene Tree một cách an toàn
	queue_free()

func _physics_process(delta: float) -> void:
	# --- Logic Nhấp Nháy ---
	# Kiểm tra xem có nên bắt đầu nhấp nháy không
	# Chỉ kiểm tra nếu despawn_timer tồn tại và đang chạy
	if not is_blinking and despawn_timer and despawn_timer.time_left > 0 and despawn_timer.time_left <= blink_start_offset:
		is_blinking = true
		blink_timer = 0.0 # Reset bộ đếm khi bắt đầu nhấp nháy

	# Nếu đang trong trạng thái nhấp nháy
	if is_blinking:
		blink_timer += delta # Cộng dồn thời gian trôi qua
		# Nếu đủ thời gian cho một lần đảo trạng thái
		if blink_timer >= blink_interval:
			if icon: # Kiểm tra xem icon có tồn tại không trước khi truy cập
				icon.visible = not icon.visible # Đảo trạng thái hiển thị (bật/tắt)
			# Reset bộ đếm, trừ đi khoảng thời gian đã qua
			blink_timer -= blink_interval

func _on_item_clicked(viewport: Viewport, event: InputEvent, shape_idx: int) -> void:
	print(\"DEBUG: _on_item_clicked called!\") # Bỏ comment nếu muốn debug

	# Chỉ xử lý nếu là sự kiện click chuột trái hợp lệ
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.is_pressed():
		print(\"DEBUG: Left mouse click detected!\")

		# Chỉ hiện mũi tên KHI click đúng
		if arrow:
			arrow.visible = true
			print(\"DEBUG: Arrow made visible.\")
		else:
			print(\"ERROR: Biến tham chiếu đến mũi tên đang là null!\")

		# Ngăn sự kiện lan truyền
		get_viewport().set_input_as_handled()
"

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1nwlq"]
size = Vector2(13.25, 11.25)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_axvde"]
size = Vector2(15.5, 13.5)

[node name="item" type="RigidBody2D"]
collision_layer = 32
collision_mask = 17
script = SubResource("GDScript_ljgqj")

[node name="Icon" type="Sprite2D" parent="."]
position = Vector2(-1, -1)
scale = Vector2(0.5, 0.5)
texture = ExtResource("1_k051w")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-1, -1)
shape = SubResource("RectangleShape2D_1nwlq")

[node name="DespawnTimer" type="Timer" parent="."]

[node name="ClickArea" type="Area2D" parent="."]

[node name="CollisionShape2D2" type="CollisionShape2D" parent="ClickArea"]
position = Vector2(-1, -1)
shape = SubResource("RectangleShape2D_axvde")
