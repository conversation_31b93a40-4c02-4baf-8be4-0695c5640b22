# Script cho trang bị rơi ra (equipment_drop.gd)
extends RigidBody2D

@export var blink_start_offset: float = 2
@export var blink_interval: float = 0.15
@export var equipment_type: String = "Armor"  # "Armor", "Pant", "Boots", "Helmet", "Gloves"
@export var spawn_time: float = 12.0  # Thời gian tồn tại trước khi biến mất (lâu hơn weapon)

@onready var arrow: Polygon2D = $ArrowIndicator
@onready var icon: Sprite2D = $Icon
@onready var shape: CollisionShape2D = $CollisionShape2D
@onready var despawn_timer: Timer = $DespawnTimer
@onready var click_area: Area2D = $ClickArea
@onready var pickup_area: Area2D = $PickupArea

# Biến để theo dõi trạng thái nhấp nháy
var is_blinking: bool = false
var blink_timer: float = 0.0

# Preload các texture cho trang bị
var equipment_textures = {
	"Armor": preload("res://assets/images/item/Weapon/ao.png"),
	"Pant": preload("res://assets/images/item/Weapon/giay.png"),  # Sử dụng giày cho pants tạm thời
	"Boots": preload("res://assets/images/item/Weapon/giay.png"), # Texture chính thức cho giày
	"Helmet": preload("res://assets/images/item/Weapon/ao.png"),  # Fallback to armor texture
	"Gloves": preload("res://assets/images/item/Weapon/ao.png")   # Fallback to armor texture
}

func _ready() -> void:
	# Thiết lập texture dựa trên loại trang bị
	if equipment_type in equipment_textures:
		icon.texture = equipment_textures[equipment_type]
	else:
		# Fallback texture nếu không tìm thấy
		icon.texture = equipment_textures["Armor"]
		print("WARNING: Unknown equipment type: " + equipment_type + ", using Armor texture")

	# Thiết lập timer để despawn
	if despawn_timer:
		despawn_timer.wait_time = spawn_time
		despawn_timer.one_shot = true
		if not despawn_timer.is_connected("timeout", Callable(self, "_on_despawn_timer_timeout")):
			despawn_timer.timeout.connect(_on_despawn_timer_timeout)
		despawn_timer.start()
	else:
		printerr("Node 'DespawnTimer' not found as a child of ", name, ". Item will not despawn automatically.")

	# Ẩn mũi tên chỉ dẫn ban đầu
	if arrow:
		arrow.visible = false
	else:
		printerr("Node 'ArrowIndicator' không tìm thấy!")

	# Thiết lập kết nối signal cho click
	if click_area:
		click_area.input_pickable = true
		if not click_area.is_connected("input_event", Callable(self, "_on_item_clicked")):
			click_area.input_event.connect(_on_item_clicked)
	else:
		printerr("Node 'ClickArea' not found!")

	# Thiết lập kết nối signal cho vùng tự động nhặt
	if pickup_area:
		if not pickup_area.is_connected("body_entered", Callable(self, "_on_pickup_area_body_entered")):
			pickup_area.body_entered.connect(_on_pickup_area_body_entered)
	else:
		printerr("Node 'PickupArea' not found!")

func _on_despawn_timer_timeout() -> void:
	# Xóa trang bị khi hết thời gian
	queue_free()

func _physics_process(delta: float) -> void:
	# Logic nhấp nháy khi sắp biến mất
	if not is_blinking and despawn_timer and despawn_timer.time_left > 0 and despawn_timer.time_left <= blink_start_offset:
		is_blinking = true
		blink_timer = 0.0

	if is_blinking:
		blink_timer += delta
		if blink_timer >= blink_interval:
			if icon:
				icon.visible = not icon.visible
			blink_timer -= blink_interval

func _on_item_clicked(_viewport: Viewport, event: InputEvent, _shape_idx: int) -> void:
	# Xử lý khi người chơi click vào trang bị
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.is_pressed():
		# Hiển thị mũi tên chỉ dẫn
		if arrow:
			arrow.visible = true

		# Tìm player trong scene
		var player = get_tree().get_first_node_in_group("player")
		if player:
			# Kiểm tra khoảng cách với player
			var dist = global_position.distance_to(player.global_position)
			if dist < 50:  # Nếu player đủ gần
				_collect_equipment()

		# Ngăn sự kiện lan truyền
		get_viewport().set_input_as_handled()

# Xử lý khi người chơi đi vào vùng tự động nhặt
func _on_pickup_area_body_entered(body: Node) -> void:
	# Kiểm tra xem body có phải là player không
	if body.is_in_group("player"):
		# Hiệu ứng nhặt trang bị
		if arrow:
			arrow.visible = true

		# Nhặt trang bị
		_collect_equipment()

func _collect_equipment() -> void:
	# Tìm player để thêm trang bị vào inventory
	var player = get_tree().get_first_node_in_group("player")
	if player and player.has_method("add_equipment_to_inventory"):
		player.add_equipment_to_inventory(equipment_type)
		print("Collected " + equipment_type + " equipment")
	else:
		# Fallback: lưu vào một hệ thống inventory toàn cục
		var equipment_inventory = get_node_or_null("/root/EquipmentInventoryManager")
		if equipment_inventory and equipment_inventory.has_method("add_equipment"):
			equipment_inventory.add_equipment(equipment_type)
			print("Collected " + equipment_type + " equipment (via EquipmentInventoryManager)")
		else:
			print("WARNING: Could not find player or EquipmentInventoryManager to add equipment")

	# Hiệu ứng khi nhặt trang bị
	_play_pickup_effect()

	# Xóa trang bị sau khi nhặt
	queue_free()

# Hiệu ứng khi nhặt trang bị
func _play_pickup_effect() -> void:
	# Tạo hiệu ứng hình ảnh (có thể thêm particle effect ở đây)
	# Ví dụ: tạo một tween để trang bị bay lên và mờ dần
	var tween = create_tween()
	tween.tween_property(self, "position", position + Vector2(0, -35), 0.4)
	tween.parallel().tween_property(self, "modulate", Color(1, 1, 1, 0), 0.4)

	# Phát âm thanh (nếu có)
	# Ví dụ: AudioStreamPlayer.play()
