extends TextureButton

# ----- Properties -----
@export var previous_scene: String = "res://Home/scenes/Map_.tscn" # Scene to return to

# ----- Lifecycle Methods -----
func _ready() -> void:
	mouse_entered.connect(func(): on_hover(true))
	mouse_exited.connect(func(): on_hover(false))
	pressed.connect(on_back_pressed)

# ----- Event Handlers -----
func on_hover(hover_active: bool) -> void:
	modulate = Color(1, 0.8, 0.8) if hover_active else Color(1, 1, 1) # Simple hover effect

func on_back_pressed() -> void:
	get_tree().change_scene_to_file(previous_scene) # Return to previous scene
