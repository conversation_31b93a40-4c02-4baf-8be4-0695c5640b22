[gd_scene load_steps=4 format=3]

[ext_resource type="Texture2D" uid="uid://oax7heho3lva" path="res://assets/images/background/elements/background.png" id="1_w5v1c"]
[ext_resource type="Script" path="res://maps/test_scene.gd" id="1_xtest"]
[ext_resource type="PackedScene" uid="uid://bkn5seficjirh" path="res://player/player.tscn" id="2_4rgpu"]

[node name="TestScene" type="Node2D"]
script = ExtResource("1_xtest")

[node name="Background" type="Sprite2D" parent="."]
position = Vector2(576, 324)
scale = Vector2(5, 5)
texture = ExtResource("1_w5v1c")

[node name="Player" parent="." instance=ExtResource("2_4rgpu")]
position = Vector2(576, 324)

[node name="Label" type="Label" parent="."]
offset_left = 400.0
offset_top = 100.0
offset_right = 752.0
offset_bottom = 200.0
theme_override_font_sizes/font_size = 32
text = "Test Scene Loaded Successfully"
horizontal_alignment = 1
vertical_alignment = 1
