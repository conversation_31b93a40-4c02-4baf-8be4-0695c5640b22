[gd_resource type="SpriteFrames" load_steps=24 format=3 uid="uid://dh7sylci5cd12"]

[ext_resource type="Texture2D" uid="uid://dcjrpjqbpr8ur" path="res://assets/images/characters/players/sheets/none/chuyen_dong_tay_khong_nvc.png" id="1_bfd3y"]

[sub_resource type="AtlasTexture" id="AtlasTexture_qpqxk"]
atlas = ExtResource("1_bfd3y")
region = Rect2(0, 0, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_8vn17"]
atlas = ExtResource("1_bfd3y")
region = Rect2(51, 0, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_soa01"]
atlas = ExtResource("1_bfd3y")
region = Rect2(102, 0, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_6slfg"]
atlas = ExtResource("1_bfd3y")
region = Rect2(153, 0, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_b2ops"]
atlas = ExtResource("1_bfd3y")
region = Rect2(204, 0, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ake5"]
atlas = ExtResource("1_bfd3y")
region = Rect2(255, 0, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_cnatf"]
atlas = ExtResource("1_bfd3y")
region = Rect2(102, 204, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_a2bqx"]
atlas = ExtResource("1_bfd3y")
region = Rect2(153, 204, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_far2k"]
atlas = ExtResource("1_bfd3y")
region = Rect2(0, 204, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_rgein"]
atlas = ExtResource("1_bfd3y")
region = Rect2(51, 204, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_4o7ea"]
atlas = ExtResource("1_bfd3y")
region = Rect2(0, 102, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ihvc"]
atlas = ExtResource("1_bfd3y")
region = Rect2(51, 102, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_yy84i"]
atlas = ExtResource("1_bfd3y")
region = Rect2(102, 102, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_2mk4g"]
atlas = ExtResource("1_bfd3y")
region = Rect2(153, 102, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_w6uqk"]
atlas = ExtResource("1_bfd3y")
region = Rect2(204, 102, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_7urwn"]
atlas = ExtResource("1_bfd3y")
region = Rect2(255, 102, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_de1gl"]
atlas = ExtResource("1_bfd3y")
region = Rect2(0, 51, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_ll10o"]
atlas = ExtResource("1_bfd3y")
region = Rect2(51, 51, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_xv0j7"]
atlas = ExtResource("1_bfd3y")
region = Rect2(102, 51, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_t040a"]
atlas = ExtResource("1_bfd3y")
region = Rect2(153, 51, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_yf3va"]
atlas = ExtResource("1_bfd3y")
region = Rect2(204, 51, 51, 51)

[sub_resource type="AtlasTexture" id="AtlasTexture_cumn2"]
atlas = ExtResource("1_bfd3y")
region = Rect2(255, 51, 51, 51)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_qpqxk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8vn17")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_soa01")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6slfg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b2ops")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ake5")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cnatf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a2bqx")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_far2k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rgein")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4o7ea")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ihvc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yy84i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2mk4g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w6uqk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7urwn")
}],
"loop": true,
"name": &"running",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_de1gl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ll10o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xv0j7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t040a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yf3va")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cumn2")
}],
"loop": true,
"name": &"walking",
"speed": 10.0
}]
