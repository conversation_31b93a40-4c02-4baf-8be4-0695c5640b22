extends Control

@onready var video_player = $VideoPlayer
@onready var skip_button = $TopRightButtons/SkipButton
@onready var speed_button = $TopRightButtons/SpeedButton
var _current_speed = 1.0
var _is_2x = false

func _ready():
	# Connect button signals
	skip_button.pressed.connect(_on_skip_button_pressed)
	speed_button.pressed.connect(_on_speed_button_pressed)
	
	# Connect video finished signal
	video_player.finished.connect(_on_video_finished)
	
	# Start playing the video
	video_player.play()
	
	# Add tooltip to indicate that 2x is visual only
	speed_button.tooltip_text = "Note: Speed control simulated visually"

# Skip button handler - skips the video and goes to the next scene
func _on_skip_button_pressed():
	_go_to_next_scene()

# Speed button handler - for Theora videos in Godot, we can't change playback speed
# but we'll toggle the button for user feedback
func _on_speed_button_pressed():
	_is_2x = !_is_2x
	
	if _is_2x:
		speed_button.text = "1x"
	else:
		speed_button.text = "2x"
	
	# Print a message to console - in a real game you might show a small notification
	print("Speed button pressed - note that actual speed control for Theora videos is not available")

# Video finished handler - automatically proceeds to next scene
func _on_video_finished():
	_go_to_next_scene()

# Common function to proceed to the next scene
func _go_to_next_scene():
	# Stop the video if it's still playing
	if video_player.is_playing():
		video_player.stop()
	
	# Go to the first game scene using the SceneManager
	SceneManager.goto_first_scene() 