# Loading Freeze Fix - Emergency fix for loading screen freeze
extends Node

func _ready():
	print("🚨 Loading Freeze Fix activated")
	call_deferred("apply_emergency_fixes")

func apply_emergency_fixes():
	"""Apply emergency fixes for loading screen freeze"""
	print("\n🔧 Applying emergency fixes for loading screen freeze...")
	
	# Fix 1: Stop all infinite animations
	stop_infinite_animations()
	
	# Fix 2: Clean up problematic UI systems
	cleanup_problematic_ui()
	
	# Fix 3: Force loading completion if stuck
	await get_tree().create_timer(2.0).timeout
	check_and_force_completion()
	
	print("✅ Emergency fixes applied!")

func stop_infinite_animations():
	"""Stop all infinite animations that might cause freeze"""
	print("🛑 Stopping infinite animations...")
	
	# Find all tween nodes and stop infinite loops
	var all_nodes = get_tree().get_nodes_in_group("loading_screen")
	for node in all_nodes:
		if node.has_method("cleanup_loading_screen"):
			node.cleanup_loading_screen()
			print("  ✅ Cleaned up loading screen: %s" % node.name)
	
	# Stop any global tweens
	var tweens = get_tree().get_nodes_in_group("tweens")
	for tween in tweens:
		if tween is Tween:
			tween.kill()
			print("  🛑 Stopped tween: %s" % tween.name)

func cleanup_problematic_ui():
	"""Clean up UI systems that might be causing conflicts"""
	print("🧹 Cleaning up problematic UI systems...")
	
	# Disable GlobalMapNameUI temporarily
	var global_map_ui = get_node_or_null("/root/GlobalMapNameUI")
	if global_map_ui:
		# Disable its processing temporarily
		global_map_ui.set_process(false)
		global_map_ui.set_process_unhandled_input(false)
		print("  ⏸️ Temporarily disabled GlobalMapNameUI")
	
	# Remove any test nodes that might be interfering
	var test_nodes = []
	test_nodes.append_array(get_tree().get_nodes_in_group("ui_test"))
	test_nodes.append_array(get_tree().get_nodes_in_group("system_test"))
	
	for test_node in test_nodes:
		test_node.queue_free()
		print("  🗑️ Removed test node: %s" % test_node.name)

func check_and_force_completion():
	"""Check if loading is stuck and force completion"""
	print("🔍 Checking loading status...")
	
	var loading_nodes = get_tree().root.find_children("*Loading*", "", true, false)
	if loading_nodes.size() > 0:
		print("⚠️ Loading screen still active, forcing completion...")
		
		for loading_node in loading_nodes:
			# Try to complete loading screen
			if loading_node.has_method("show_completion_effect"):
				loading_node.show_completion_effect()
			
			# Force transition after a delay
			await get_tree().create_timer(1.0).timeout
			
			# Force scene change to main menu
			print("🚨 Forcing transition to main menu...")
			get_tree().change_scene_to_file("res://Home/scenes/Startmenu.tscn")
			break
	else:
		print("✅ No loading screen detected, system appears normal")

func restore_ui_systems():
	"""Restore UI systems after emergency fix"""
	print("🔄 Restoring UI systems...")
	
	# Re-enable GlobalMapNameUI
	var global_map_ui = get_node_or_null("/root/GlobalMapNameUI")
	if global_map_ui:
		global_map_ui.set_process(true)
		global_map_ui.set_process_unhandled_input(true)
		print("  ✅ Re-enabled GlobalMapNameUI")
	
	print("✅ UI systems restored!")

# Manual fix functions
func force_main_menu():
	"""Force transition to main menu"""
	print("🚨 Forcing transition to main menu...")
	cleanup_problematic_ui()
	await get_tree().create_timer(0.5).timeout
	get_tree().change_scene_to_file("res://Home/scenes/Startmenu.tscn")

func emergency_restart():
	"""Emergency restart of the game"""
	print("🚨 Emergency restart initiated...")
	cleanup_problematic_ui()
	await get_tree().create_timer(0.5).timeout
	get_tree().reload_current_scene()

# Public API
func fix_loading_freeze():
	"""Main function to fix loading freeze"""
	print("🚨 Starting loading freeze fix...")
	await apply_emergency_fixes()
	await get_tree().create_timer(2.0).timeout
	restore_ui_systems()
	print("✅ Loading freeze fix completed!")
