extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var progress_bar = $LoadingContainer/ProgressBar
@onready var loading_text = $LoadingContainer/LoadingText
@onready var animation_player = $AnimationPlayer


var tips = [
	"Tip Option: Ex: Press TAB to ...",
	"Add more tips here..."
]


func _ready():
	# Setup animations
	_create_animations()
	
	# Play intro animation
	animation_player.play("fade_in")
	
	# Set random tip
	_set_random_tip()
	
	# Make progress bar start at 0
	progress_bar.value = 0

func _process(_delta):
	# Add a subtle pulsing effect to the loading text
	loading_text.modulate.a = 0.7 + 0.3 * sin(Time.get_ticks_msec() * 0.002)
	
func _set_random_tip():
	if tips.size() > 0:
		var tip_index = randi() % tips.size()
		loading_text.text = "Loading... \n\n" + tips[tip_index]
		
func _create_animations():
	# Create animation library
	var library = AnimationLibrary.new()
	
	# Create fade in animation
	var fade_in = Animation.new()
	var track_index = fade_in.add_track(Animation.TYPE_VALUE)
	fade_in.track_set_path(track_index, ":modulate")
	fade_in.track_insert_key(track_index, 0.0, Color(1, 1, 1, 0))
	fade_in.track_insert_key(track_index, 0.5, Color(1, 1, 1, 1))
	fade_in.length = 0.5
	
	# Create fade out animation
	var fade_out_anim = Animation.new()
	track_index = fade_out_anim.add_track(Animation.TYPE_VALUE)
	fade_out_anim.track_set_path(track_index, ":modulate")
	fade_out_anim.track_insert_key(track_index, 0.0, Color(1, 1, 1, 1))
	fade_out_anim.track_insert_key(track_index, 0.5, Color(1, 1, 1, 0))
	fade_out_anim.length = 0.5
	
	# Add animations to library
	library.add_animation("fade_in", fade_in)
	library.add_animation("fade_out", fade_out_anim)
	
	# Add library to animation player
	animation_player.add_animation_library("", library)
	
func fade_out():
	animation_player.play("fade_out")
	await animation_player.animation_finished
	queue_free()
