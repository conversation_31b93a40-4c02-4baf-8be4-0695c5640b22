# Inventory Button Manager - <PERSON><PERSON><PERSON><PERSON> lý nút mở hành trang ở cạnh trái màn hình
extends CanvasLayer

# Nút mở hành trang
var inventory_button: TextureButton

# Biến theo dõi trạng thái giao diện hành trang
var inventory_tab_instance: Control = null
var is_inventory_open: bool = false

func _ready() -> void:
	# Tạo nút mở hành trang
	create_inventory_button()
	
	# Kết nối signal để đóng mở
	if inventory_button:
		inventory_button.pressed.connect(_on_inventory_button_pressed)

func create_inventory_button() -> void:
	# Tạo nút texture button
	inventory_button = TextureButton.new()
	inventory_button.name = "InventoryOpenButton"
	
	# Đặt vị trí ở cạnh bên trái màn hình
	inventory_button.position = Vector2(20, 300)  # Cạnh trái, gần giữa màn hình
	inventory_button.size = Vector2(50, 50)  # Nhỏ hơn một chút
	
	# Tạo texture cho nút 
	create_button_texture()
		# Thêm tooltip
	inventory_button.tooltip_text = "Mở Hành Trang (B)"
	
	# Thêm vào scene
	add_child(inventory_button)
	
	print("Inventory button created at position: ", inventory_button.position)

func create_button_texture() -> void:
	# Tìm kiếm texture cho inventory button
	var texture_paths = [
		"res://assets/images/ui/inventory_button.png",
		"res://assets/images/item/bag.png",
		"res://Legacy-Fantasy - High Forest 2.3/HUD/Base-01.png"
	]
	var texture_found = false
	for path in texture_paths:
		if ResourceLoader.exists(path):
			inventory_button.texture_normal = load(path)
			texture_found = true
			print("Loaded inventory button texture: ", path)
			break
	
	if not texture_found:
		# Tạo style cho nút khi chưa có texture
		var style_normal = StyleBoxFlat.new()
		style_normal.bg_color = Color(0.5, 0.3, 0.1, 0.9)  # Màu nâu đẹp hơn
		style_normal.border_width_left = 2
		style_normal.border_width_top = 2
		style_normal.border_width_right = 2
		style_normal.border_width_bottom = 2
		style_normal.border_color = Color(0.9, 0.7, 0.4)  # Viền vàng
		style_normal.corner_radius_top_left = 10
		style_normal.corner_radius_top_right = 10
		style_normal.corner_radius_bottom_left = 10
		style_normal.corner_radius_bottom_right = 10
		
		var style_hover = StyleBoxFlat.new()
		style_hover.bg_color = Color(0.6, 0.4, 0.2, 1.0)  # Sáng hơn khi hover
		style_hover.border_width_left = 3
		style_hover.border_width_top = 3
		style_hover.border_width_right = 3
		style_hover.border_width_bottom = 3
		style_hover.border_color = Color(1.0, 0.8, 0.5)
		style_hover.corner_radius_top_left = 10
		style_hover.corner_radius_top_right = 10
		style_hover.corner_radius_bottom_left = 10
		style_hover.corner_radius_bottom_right = 10
		
		var style_pressed = StyleBoxFlat.new()
		style_pressed.bg_color = Color(0.4, 0.2, 0.05, 1.0)  # Tối hơn khi nhấn
		style_pressed.border_width_left = 2
		style_pressed.border_width_top = 2
		style_pressed.border_width_right = 2
		style_pressed.border_width_bottom = 2
		style_pressed.border_color = Color(0.7, 0.5, 0.3)
		style_pressed.corner_radius_top_left = 10
		style_pressed.corner_radius_top_right = 10
		style_pressed.corner_radius_bottom_left = 10
		style_pressed.corner_radius_bottom_right = 10
		
		inventory_button.add_theme_stylebox_override("normal", style_normal)
		inventory_button.add_theme_stylebox_override("hover", style_hover)
		inventory_button.add_theme_stylebox_override("pressed", style_pressed)
		
		# Thêm icon text
		var label = Label.new()
		label.text = "🎒"  # Unicode backpack icon
		label.position = Vector2(12, 8)
		label.add_theme_font_size_override("font_size", 28)
		label.add_theme_color_override("font_color", Color.WHITE)
		label.add_theme_color_override("font_shadow_color", Color.BLACK)
		label.add_theme_constant_override("shadow_offset_x", 1)
		label.add_theme_constant_override("shadow_offset_y", 1)
		inventory_button.add_child(label)

func _input(_event: InputEvent) -> void:
	# Hỗ trợ phím tắt B để mở hành trang
	if Input.is_action_just_pressed("open_inventory"):
		_on_inventory_button_pressed()
	
	# Đóng inventory khi nhấn Escape
	if Input.is_action_just_pressed("ui_cancel") and is_inventory_open:
		close_inventory()

func _on_inventory_button_pressed() -> void:
	if is_inventory_open:
		close_inventory()
	else:
		open_inventory()

func open_inventory() -> void:
	if inventory_tab_instance != null:
		return  # Đã mở rồi
	
	# Load scene hành trang with fallback paths
	var inventory_paths = [
		"res://hud_progress/scenes/inventory_tab.tscn",
		"res://ui/scenes/inventory_tab.tscn",
		"res://scenes/inventory_tab.tscn"
	]

	var inventory_scene = null
	for path in inventory_paths:
		if FileAccess.file_exists(path):
			inventory_scene = load(path)
			if inventory_scene:
				print("Loaded inventory scene from: %s" % path)
				break

	if inventory_scene:
		inventory_tab_instance = inventory_scene.instantiate()
		get_tree().root.add_child(inventory_tab_instance)
		is_inventory_open = true

		# Kết nối signal đóng từ nút close trong inventory_tab
		if inventory_tab_instance.has_method("connect_close_signal"):
			inventory_tab_instance.connect_close_signal(close_inventory)

		# Kết nối signal tree_exited
		inventory_tab_instance.tree_exited.connect(_on_inventory_closed)

		print("Inventory opened")

		# Tạo hiệu ứng mở
		inventory_tab_instance.modulate.a = 0.0
		inventory_tab_instance.scale = Vector2(1.0, 1.0)  # Đảm bảo scale bình thường
		var tween = get_tree().create_tween()
		tween.tween_property(inventory_tab_instance, "modulate:a", 1.0, 0.3)
	else:
		print("INFO: Cannot load inventory_tab.tscn from any path - inventory system disabled")
		print("   Checked paths: %s" % str(inventory_paths))

func close_inventory() -> void:
	if inventory_tab_instance == null:
		return
	
	# Tạo hiệu ứng đóng
	var tween = get_tree().create_tween()
	tween.tween_property(inventory_tab_instance, "modulate:a", 0.0, 0.2)
	await tween.finished
	if inventory_tab_instance:
		inventory_tab_instance.queue_free()
		inventory_tab_instance = null
		is_inventory_open = false
		print("Inventory closed")

func _on_inventory_closed() -> void:
	inventory_tab_instance = null
	is_inventory_open = false
	print("Inventory closed via signal")

# Hàm public để các script khác có thể gọi
func toggle_inventory() -> void:
	_on_inventory_button_pressed()

func is_inventory_tab_open() -> bool:
	return is_inventory_open
