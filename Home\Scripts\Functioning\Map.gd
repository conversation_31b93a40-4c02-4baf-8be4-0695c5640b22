extends Node2<PERSON>

@onready var continueButton: But<PERSON>
@onready var newGameButton: But<PERSON>
@onready var settingsButton: But<PERSON>
@onready var exitButton: Button
var originalScale = Vector2(1, 1)
var defaultColor = Color.WHITE
var hoverColor = Color(1, 1, 0) # Yellow color (RGB: 1,1,0)

func _ready():
	continueButton = get_node("VBoxContainer/Continue")
	newGameButton = get_node("VBoxContainer/NewGame")
	settingsButton = get_node("VBoxContainer/Settings")
	exitButton = get_node("VBoxContainer/Exit")
	
	if is_instance_valid(continueButton):
		setup_button(continueButton, "res://ui/scenes/boot_scene.tscn")
	if is_instance_valid(newGameButton):
		setup_button(newGameButton, "res://ui/scenes/boot_scene.tscn")
	if is_instance_valid(settingsButton):
		setup_button(settingsButton, "res://Home/scenes/Setting.tscn")
	if is_instance_valid(exitButton):
		exitButton.pressed.connect(on_exit_pressed)

func setup_button(button: Button, scene_path: String) -> void:
	if not is_instance_valid(button):
		return
		
	# Disconnect any existing connections first
	if button.is_connected("mouse_entered", on_mouse_enter):
		button.mouse_entered.disconnect(on_mouse_enter)
	if button.is_connected("mouse_exited", on_mouse_exit):
		button.mouse_exited.disconnect(on_mouse_exit)
	if button.is_connected("pressed", on_button_pressed):
		button.pressed.disconnect(on_button_pressed)
	
	# Connect new signals
	button.mouse_entered.connect(func(): on_mouse_enter(button))
	button.mouse_exited.connect(func(): on_mouse_exit(button))
	button.pressed.connect(func(): on_button_pressed(button, scene_path))

func on_mouse_enter(button: Button) -> void:
	if is_instance_valid(button):
		button.modulate = hoverColor # Change color to yellow
	
func on_mouse_exit(button: Button) -> void:
	if is_instance_valid(button):
		button.modulate = defaultColor # Return to white color
		button.scale = originalScale # Reset size
	
func on_button_pressed(button: Button, scene_path: String) -> void:
	if not is_instance_valid(button):
		return
		
	button.scale = originalScale * 1.2 # Make bigger
	
	# Add a small delay before scene change
	await get_tree().create_timer(0.1).timeout
	
	# Use SceneManager instead of direct scene change
	SceneManager.goto_scene(scene_path)
	
func on_exit_pressed() -> void:
	# Add a small delay before quitting
	await get_tree().create_timer(0.1).timeout
	get_tree().quit()
