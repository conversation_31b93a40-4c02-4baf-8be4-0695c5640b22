# Script quản lý việc rơi vật phẩm (potion_drop_manager.gd)
extends Node

# Preload scene cho bình máu và bình mana
var potion_drop_scene = preload("res://Items/Potion/scenes/potion_drop.tscn")

# Tỉ lệ rơi vật phẩm (%)
@export var health_potion_drop_rate: float = 15.0  # 15% cơ hội rơi bình máu
@export var mana_potion_drop_rate: float = 10.0    # 10% cơ hội rơi bình mana

# Tỉ lệ rơi vật phẩm dựa trên loại kẻ địch
var enemy_drop_rate_multiplier = {
	"sword": 1.0,   # Tỉ lệ cơ bản
	"spear": 1.2,   # Tăng 20%
	"bow": 1.5,     # Tăng 50%
	"boss": 3.0     # Tăng 200% (chắc chắn rơi)
}

# Hàm tạo vật phẩm rơi ra tại vị trí của kẻ địch
func spawn_potion_drop(enemy_position: Vector2, enemy_type: String) -> void:
	# Tính toán tỉ lệ rơi dựa trên loại kẻ địch
	var multiplier = enemy_drop_rate_multiplier.get(enemy_type, 1.0)
	var adjusted_health_rate = health_potion_drop_rate * multiplier
	var adjusted_mana_rate = mana_potion_drop_rate * multiplier
	
	# Tạo số ngẫu nhiên để quyết định có rơi vật phẩm không
	var random_value = randf() * 100  # Số ngẫu nhiên từ 0 đến 100
	
	# Quyết định loại vật phẩm rơi ra
	if random_value < adjusted_health_rate:
		_create_potion_drop(enemy_position, "health")
	elif random_value < (adjusted_health_rate + adjusted_mana_rate):
		_create_potion_drop(enemy_position, "mana")
	
	# Nếu là boss, luôn rơi cả hai loại
	if enemy_type == "boss":
		_create_potion_drop(enemy_position, "health")
		_create_potion_drop(enemy_position, "mana")

# Hàm tạo vật phẩm cụ thể
func _create_potion_drop(position: Vector2, potion_type: String) -> void:
	# Tạo instance của scene potion_drop
	var potion_instance = potion_drop_scene.instantiate()
	
	# Thiết lập loại potion
	potion_instance.potion_type = potion_type
	
	# Thiết lập vị trí
	potion_instance.global_position = position
	
	# Thêm vào scene
	var current_scene = get_tree().current_scene
	if current_scene:
		current_scene.add_child(potion_instance)
	else:
		get_tree().root.add_child(potion_instance)
	
	# Thêm lực ngẫu nhiên để vật phẩm "bắn" ra
	potion_instance.apply_impulse(Vector2(randf_range(-50, 50), randf_range(-100, -50)))
	
	print("Spawned " + potion_type + " potion at " + str(position))
