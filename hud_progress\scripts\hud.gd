extends Control
class_name HUD

# ----- Properties -----
var _character: Player
var _mana_bar: TextureProgressBar
var _health_bar: TextureProgressBar
var _last_mana_value: float = 0.0
var _last_health_value: float = 0.0

# ----- Lifecycle Methods -----
func _ready() -> void:
	if self.name != "HUD":
		push_error("<PERSON><PERSON><PERSON> is not running on the HUD node!")
		return
	
	# Truy cập ManaProgressBar
	_mana_bar = get_node("ManaProgressBar")
	if not _mana_bar:
		push_error("ManaProgressBar not found in HUD. Check the scene tree or path.")
	
	# Truy cập HealthProgressBar
	_health_bar = get_node("HealthProgressBar")
	if not _health_bar:
		push_error("HealthProgressBar not found in HUD. Check the scene tree or path.")
	
	# Cấu hình vị trí và kích thước
	if _mana_bar:
		_mana_bar.position = Vector2(168, 49)
		_mana_bar.size = Vector2(202, 40)
	
	if _health_bar:
		_health_bar.position = Vector2(168, 11)
		_health_bar.size = Vector2(278.5, 45)
	
	# In cây node để kiểm tra
	print_tree()

func _physics_process(_delta: float) -> void:
	# Skip if no character reference
	if _character == null:
		return
	
	# Update mana bar with smooth tween if value changes
	if _mana_bar and _last_mana_value != _character.mana:
		var mana_tween := create_tween()
		mana_tween.tween_property(_mana_bar, "value", _character.mana, 0.2)
		_last_mana_value = _character.mana
	
	# Update health bar with smooth tween if value changes
	if _health_bar and _last_health_value != _character.health:
		var health_tween := create_tween()
		health_tween.tween_property(_health_bar, "value", _character.health, 0.2)
		_last_health_value = _character.health

# ----- Initialization -----
func initialize(character: Player) -> void:
	_character = character
	if _character == null:
		push_error("Null character passed to HUD")
		return
	
	if _mana_bar:
		_mana_bar.max_value = _character.mana
		_mana_bar.value = _character.mana
		_last_mana_value = _character.mana
	
	if _health_bar:
		_health_bar.max_value = _character.health
		_health_bar.value = _character.health
		_last_health_value = _character.health
