extends TextureButton

# ----- Signals -----
signal hovered(title: String, description: String)
signal unhovered

# ----- Exported Variables -----
@export_file("*.tscn") var target_scene: String = "res://maps/dong_dau/scenes/dong_dau.tscn"
@export var button_title: String = "Phùng Nguyên"
@export var button_description: String = "Khu vực phát hiện nhiều hiện vật thời Đông Đầu."
@export var is_locked: bool = false  # Mặc định là không khóa

# ----- Constants -----
# Không sử dụng hiệu ứng scale

# ----- Lifecycle -----
func _ready():
	if not is_instance_valid(self):
		push_error("TextureButton instance is invalid")
		return

	# Không thay đổi kích thước
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	pressed.connect(_on_pressed)


# ----- Event Handlers -----
func _on_mouse_entered():
	if not is_instance_valid(self):
		return

	if is_locked:
		# <PERSON><PERSON><PERSON> bị khóa, emit signal với thông báo khó<PERSON>
		hovered.emit(button_title, "Khu vực này hiện đang bị khóa. Hãy hoàn thành nhiệm vụ trước để mở khóa.")
		return

	# Không thay đổi kích thước khi hover
	hovered.emit(button_title, button_description)

func _on_mouse_exited():
	if not is_instance_valid(self):
		return

	# Không thay đổi kích thước khi unhover
	unhovered.emit()

func _on_pressed():
	if is_locked:
		# Có thể thêm hiệu ứng âm thanh hoặc visual để chỉ ra rằng button bị khóa
		print("This area is locked!")
		return

	if not FileAccess.file_exists(target_scene):
		push_error("Cannot load target scene: " + target_scene)
		return

	# Không thay đổi kích thước khi nhấn, chỉ chuyển scene
	await get_tree().create_timer(0.1).timeout
	SceneManager.goto_scene(target_scene)

# ----- Public Methods -----
func unlock() -> void:
	is_locked = false
	print("Area unlocked!")

func lock() -> void:
	is_locked = true
	print("Area locked!")
