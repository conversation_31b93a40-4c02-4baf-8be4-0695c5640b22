# Teleport System Spatial Analysis

## Current Teleport Gate Inventory

### <PERSON> (Central Hub)
**Map Bounds**: Unknown (need to check scene)
**Current Gates**:
- `TeleportGate_DongDau` at position Vector2(-645, -6) relative to TeleportGates node at (900, -2063)
  - Actual position: Vector2(255, -2069) 
  - Gate ID: `lang_van_lang_to_dong_dau`
  - Target: dong_dau.tscn at Vector2(3500, -1900)
  - **Spatial Analysis**: LEFT side of Lang Van Lang

- `TeleportGate_SuoiThieng` at position Vector2(2839, 8) relative to TeleportGates node at (900, -2063)
  - Actual position: Vector2(3739, -2055)
  - Gate ID: `lang_van_lang_to_suoi_thieng` 
  - Target: suoi_thieng.tscn at Vector2(-2000, 200)
  - **Spatial Analysis**: RIGHT side of Lang Van Lang

### Dong Dau
**Map Bounds**: left=-2535, right=5400, top=-1600, bottom=-125
**Player Default Position**: Vector2(-1421, -429)
**Current Gates**:
- `rung_nuong` gate at Vector2(5328, -320)
  - Gate ID: `dong_dau_to_rung_nuong`
  - Target: rung_nuong.tscn at Vector2(-200, -1300)
  - **Spatial Analysis**: FAR RIGHT side of Dong Dau

- `doi_tre` gate at Vector2(-2500, -435)
  - Gate ID: `dong_dau_to_doi_tre`
  - Target: doi_tre.tscn at Vector2(-2292, -538)
  - **Spatial Analysis**: FAR LEFT side of Dong Dau

- `lang_van_lang` gate at Vector2(-2122, -296)
  - Gate ID: `dong_dau_to_lang_van_lang`
  - Target: lang_van_lang.tscn at Vector2(300, -1900)
  - **Spatial Analysis**: LEFT side of Dong Dau

### Doi Tre
**Map Bounds**: left=-3000, right=1500, top=-800, bottom=-120
**Player Default Position**: Vector2(-2232, -581)
**Current Gates**:
- `TeleportGate_DoiTre` at Vector2(-2798, -229)
  - Gate ID: `doi_tre_to_dong_dau`
  - Target: dong_dau.tscn at Vector2(-1400, -400)
  - **Spatial Analysis**: FAR LEFT side of Doi Tre

### Rung Nuong
**Current Gates** (from analysis):
- `dong_dau` gate at Vector2(-584, -1350)
  - Gate ID: `rung_nuong_to_dong_dau`
  - Target: dong_dau.tscn at Vector2(5200, -350)
  - **Spatial Analysis**: LEFT side of Rung Nuong

- `hang_an` gate at Vector2(2003, -1343)
  - Gate ID: `rung_nuong_to_hang_an`
  - Target: hang_an.tscn at Vector2(300, -1900)
  - **Spatial Analysis**: RIGHT side of Rung Nuong

- `lang_van_lang` gate at Vector2(1500, -1300)
  - Gate ID: `rung_nuong_to_lang_van_lang`
  - Target: lang_van_lang.tscn at Vector2(2000, -1900)
  - **Spatial Analysis**: CENTER-RIGHT of Rung Nuong

### Hang An
**Current Gates**:
- Gate to `rung_nuong` 
  - Gate ID: `hang_an_to_rung_nuong`
  - Target: rung_nuong.tscn at Vector2(2000, -1200)

- Gate to `lang_van_lang`
  - Gate ID: `hang_an_to_lang_van_lang`
  - Target: lang_van_lang.tscn at Vector2(3700, -1900)

## Current Spatial Inconsistencies

### Major Issues Identified:

1. **Inconsistent Naming**: Gates use map names instead of spatial directions
2. **Broken Spatial Logic**: 
   - Lang Van Lang LEFT gate (255, -2069) → Dong Dau spawn at (3500, -1900) - should spawn on RIGHT
   - Dong Dau LEFT gate (-2122, -296) → Lang Van Lang spawn at (300, -1900) - should spawn on LEFT
   - Doi Tre LEFT gate (-2798, -229) → Dong Dau spawn at (-1400, -400) - correct LEFT→LEFT

3. **Missing Reciprocal Gates**: Some connections are one-way only

4. **Position Mapping Conflicts**: Multiple systems (teleport_position_mapping.gd, lang_van_lang_teleport_system.gd) with different values

## Proposed Spatial Layout

Based on current positions, the logical spatial relationship should be:

```
[Hang An] ←→ [Rung Nuong] ←→ [Dong Dau] ←→ [Doi Tre]
     ↑              ↑              ↑
     └─────── [Lang Van Lang] ──────┘
```

### Directional Gate Naming Convention:
- `left_entrance` / `left_exit`
- `right_entrance` / `right_exit`  
- `north_entrance` / `north_exit`
- `south_entrance` / `south_exit`
- `center_entrance` / `center_exit` (for hub connections)

## Next Steps:
1. Design complete spatial framework
2. Rename all gates with directional names
3. Update spawn positions for spatial consistency
4. Focus on TeleportGate_DoiTre.tscn as requested
5. Test spatial flow
