[gd_scene load_steps=20 format=3 uid="uid://j1pm87wmv6vm"]

[ext_resource type="Texture2D" uid="uid://g6l21fbj20hm" path="res://hud_progress/images/BackPack_Etc.png" id="1_xda7o"]
[ext_resource type="Script" uid="uid://cgn2mnwt1pn25" path="res://hud_progress/scripts/etc_tab.gd" id="1_ya400"]
[ext_resource type="Texture2D" uid="uid://bu7yt8thgnu8q" path="res://hud_progress/images/close 1.png" id="2_mgpnb"]
[ext_resource type="Texture2D" uid="uid://darqm32h0cpj7" path="res://hud_progress/images/Group 1501.png" id="3_pt1st"]
[ext_resource type="Texture2D" uid="uid://ko5svgdg634e" path="res://hud_progress/images/Group 1493.png" id="4_6xgvn"]
[ext_resource type="Texture2D" uid="uid://dl7ucdnnpjdrg" path="res://hud_progress/images/Group 1494.png" id="5_rdbkn"]
[ext_resource type="Texture2D" uid="uid://chhvfvg7fvw03" path="res://hud_progress/images/Group 1505.png" id="6_3rr25"]
[ext_resource type="Texture2D" uid="uid://byep2fst4irrb" path="res://hud_progress/images/Group 1495.png" id="7_eig7h"]
[ext_resource type="Texture2D" uid="uid://bed2f8xpe4wrq" path="res://hud_progress/images/Group 1504.png" id="8_2vh5n"]
[ext_resource type="Texture2D" uid="uid://cb4pip81phr0y" path="res://hud_progress/images/Group 1503.png" id="9_s15ag"]
[ext_resource type="Texture2D" uid="uid://cx0nn3qcow14s" path="res://hud_progress/images/Group 1496.png" id="10_13tim"]
[ext_resource type="Texture2D" uid="uid://b35b5klmy304a" path="res://hud_progress/images/Music_Sound_Label.png" id="11_jjpi0"]
[ext_resource type="Texture2D" uid="uid://b7ddh4msmtutq" path="res://hud_progress/images/Sound_Label.png" id="12_3kqnk"]
[ext_resource type="Texture2D" uid="uid://c7qkt70kwcl8q" path="res://hud_progress/images/Version_Label.png" id="13_vr8a8"]
[ext_resource type="Texture2D" uid="uid://bsqm02j0ywkts" path="res://hud_progress/images/Map_Button.png" id="14_eyxke"]
[ext_resource type="Texture2D" uid="uid://l1fae18c08k3" path="res://hud_progress/images/mp_border.png" id="16_fkykq"]
[ext_resource type="Texture2D" uid="uid://dlrs7wwi50o5e" path="res://hud_progress/images/mp_color.png" id="17_07w1l"]
[ext_resource type="Texture2D" uid="uid://cmek6fyluh4ul" path="res://hud_progress/images/HP_border.png" id="18_bx16k"]
[ext_resource type="Texture2D" uid="uid://dmbk6gi0o1nn1" path="res://hud_progress/images/hp_color.png" id="19_tb302"]

[node name="Etc_Tab" type="CanvasLayer"]
script = ExtResource("1_ya400")

[node name="Panel" type="Panel" parent="."]
offset_right = 40.0
offset_bottom = 40.0

[node name="BackPackEtc" type="Sprite2D" parent="Panel"]
position = Vector2(240, 360)
texture = ExtResource("1_xda7o")

[node name="Close_Button" type="TextureButton" parent="Panel"]
layout_mode = 0
offset_left = 456.0
offset_top = 17.0
offset_right = 501.0
offset_bottom = 62.0
texture_normal = ExtResource("2_mgpnb")

[node name="All_Tab_Button" type="HBoxContainer" parent="Panel"]
layout_mode = 0
offset_left = 16.0
offset_top = 127.0
offset_right = 467.0
offset_bottom = 190.0

[node name="Mission_Button" type="TextureButton" parent="Panel/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("3_pt1st")
texture_pressed = ExtResource("4_6xgvn")
texture_disabled = ExtResource("3_pt1st")

[node name="Inventory_Button" type="TextureButton" parent="Panel/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("5_rdbkn")
texture_pressed = ExtResource("6_3rr25")
texture_disabled = ExtResource("5_rdbkn")

[node name="Skills_Button" type="TextureButton" parent="Panel/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("7_eig7h")
texture_pressed = ExtResource("8_2vh5n")
texture_disabled = ExtResource("7_eig7h")

[node name="Etc_Button" type="TextureButton" parent="Panel/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("9_s15ag")
texture_disabled = ExtResource("10_13tim")

[node name="Music_Sound" type="TextureRect" parent="."]
offset_left = 2.0
offset_top = 195.0
offset_right = 478.0
offset_bottom = 256.0
texture = ExtResource("11_jjpi0")

[node name="Sound" type="TextureRect" parent="."]
offset_left = 2.0
offset_top = 260.0
offset_right = 478.0
offset_bottom = 321.0
texture = ExtResource("12_3kqnk")

[node name="Version" type="TextureRect" parent="."]
offset_left = 2.0
offset_top = 325.0
offset_right = 478.0
offset_bottom = 386.0
texture = ExtResource("13_vr8a8")

[node name="MapButton" type="Sprite2D" parent="."]
position = Vector2(240, 570)
texture = ExtResource("14_eyxke")

[node name="ManaProgressBar" type="TextureProgressBar" parent="."]
offset_left = 171.0
offset_top = 47.0
offset_right = 373.0
offset_bottom = 70.0
value = 100.0
texture_under = ExtResource("16_fkykq")
texture_progress = ExtResource("17_07w1l")
texture_progress_offset = Vector2(1, 1)

[node name="HealthProgressBar" type="TextureProgressBar" parent="."]
offset_left = 170.0
offset_top = 10.0
offset_right = 727.0
offset_bottom = 55.0
scale = Vector2(0.5, 0.5)
value = 50.0
texture_under = ExtResource("18_bx16k")
texture_progress = ExtResource("19_tb302")
texture_progress_offset = Vector2(3, 2)
