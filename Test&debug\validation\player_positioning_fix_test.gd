# player_positioning_fix_test.gd - Test for player positioning after teleportation
extends Node

var test_results = []

func _ready():
	print("🧪 Player Positioning Fix Test - Testing player spawn positioning after teleportation")
	await get_tree().process_frame  # Wait for autoloads to load
	run_positioning_tests()

func run_positioning_tests():
	print("\n=== Testing Player Positioning Fix ===")
	
	# Test 1: SceneManager spawn position management
	test_scene_manager_spawn_system()
	
	# Test 2: TeleportContextManager integration
	test_teleport_context_integration()
	
	# Test 3: Map controller positioning logic
	test_map_controller_positioning()
	
	# Test 4: End-to-end positioning flow
	test_positioning_flow()
	
	# Print final results
	print_test_results()

func test_scene_manager_spawn_system():
	print("\n--- Test 1: SceneManager Spawn System ---")
	
	if not SceneManager:
		add_test_result("SceneManager Availability", "FAIL", "SceneManager not found")
		return
	
	add_test_result("SceneManager Availability", "PASS", "SceneManager found")
	
	# Test spawn position setting
	var test_position = Vector2(1000, -500)
	SceneManager.set_next_spawn_position(test_position)
	
	if SceneManager.has_next_spawn_position():
		add_test_result("Spawn Position Setting", "PASS", "Position set successfully")
	else:
		add_test_result("Spawn Position Setting", "FAIL", "Position not set")
		return
	
	# Test get_next_spawn_position (should not clear)
	var retrieved_pos = SceneManager.get_next_spawn_position()
	if retrieved_pos == test_position:
		add_test_result("Get Spawn Position", "PASS", "Position retrieved correctly")
	else:
		add_test_result("Get Spawn Position", "FAIL", "Position mismatch: %s vs %s" % [retrieved_pos, test_position])
	
	# Verify position is still available after get
	if SceneManager.has_next_spawn_position():
		add_test_result("Position Persistence", "PASS", "Position persists after get")
	else:
		add_test_result("Position Persistence", "FAIL", "Position cleared after get")
	
	# Test get_and_clear_spawn_position
	var cleared_pos = SceneManager.get_and_clear_spawn_position()
	if cleared_pos == test_position:
		add_test_result("Get and Clear Position", "PASS", "Position retrieved and cleared")
	else:
		add_test_result("Get and Clear Position", "FAIL", "Position mismatch on clear")
	
	# Verify position is cleared
	if not SceneManager.has_next_spawn_position():
		add_test_result("Position Clearing", "PASS", "Position properly cleared")
	else:
		add_test_result("Position Clearing", "FAIL", "Position not cleared")

func test_teleport_context_integration():
	print("\n--- Test 2: TeleportContextManager Integration ---")
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager:
		add_test_result("TeleportContextManager", "FAIL", "TeleportContextManager not found")
		return
	
	add_test_result("TeleportContextManager", "PASS", "TeleportContextManager found")
	
	# Test context creation and spawn position setting
	context_manager.set_teleport_context(
		"test_gate",
		"lang_van_lang",
		"res://maps/dong_dau/scenes/dong_dau.tscn"
	)
	
	# Check if SceneManager received the spawn position
	if SceneManager.has_next_spawn_position():
		var spawn_pos = SceneManager.get_next_spawn_position()
		add_test_result("Context-SceneManager Integration", "PASS", "Spawn position set: %s" % spawn_pos)
	else:
		add_test_result("Context-SceneManager Integration", "FAIL", "No spawn position set by context manager")
	
	# Clean up
	context_manager.clear_teleport_context()
	SceneManager.clear_next_spawn_position()

func test_map_controller_positioning():
	print("\n--- Test 3: Map Controller Positioning Logic ---")
	
	# Test if map controllers have the required methods
	var map_controller_paths = [
		"res://maps/lang_van_lang/scripts/lang_van_lang_map_controller.gd",
		"res://maps/dong_dau/scripts/dong_dau_map_controller.gd",
		"res://maps/hang_an/scripts/hang_an_map_controller.gd",
		"res://maps/doi_tre/scripts/doi_tre_map_controller.gd",
		"res://maps/rung_nuong/scripts/rung_nuong_map_controller.gd",
		"res://maps/suoi_thieng/scripts/suoi_thieng_map_controller.gd"
	]
	
	var controllers_tested = 0
	var controllers_passed = 0
	
	for controller_path in map_controller_paths:
		var script = load(controller_path)
		if script:
			controllers_tested += 1
			
			# Create a temporary instance to check methods
			var temp_node = Node2D.new()
			temp_node.set_script(script)
			
			if temp_node.has_method("_auto_fix_teleport_position") and temp_node.has_method("_set_default_spawn_position"):
				controllers_passed += 1
			
			temp_node.queue_free()
	
	if controllers_passed == controllers_tested and controllers_tested > 0:
		add_test_result("Map Controller Methods", "PASS", "All %d controllers have required methods" % controllers_tested)
	else:
		add_test_result("Map Controller Methods", "FAIL", "Only %d/%d controllers have required methods" % [controllers_passed, controllers_tested])

func test_positioning_flow():
	print("\n--- Test 4: End-to-End Positioning Flow ---")
	
	# Simulate the complete teleportation positioning flow
	var test_position = Vector2(-1421, -429)  # Dong Dau position
	
	# Step 1: TeleportContextManager sets position
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if context_manager:
		context_manager.set_teleport_context(
			"lang_van_lang_to_dong_dau",
			"lang_van_lang",
			"res://maps/dong_dau/scenes/dong_dau.tscn"
		)
		
		# Step 2: Check if SceneManager has the position
		if SceneManager.has_next_spawn_position():
			var scene_pos = SceneManager.get_next_spawn_position()
			add_test_result("Flow Step 1-2", "PASS", "Context -> SceneManager: %s" % scene_pos)
			
			# Step 3: Simulate map controller retrieving and clearing position
			var final_pos = SceneManager.get_and_clear_spawn_position()
			if final_pos != Vector2.ZERO:
				add_test_result("Flow Step 3", "PASS", "Map controller retrieval: %s" % final_pos)
				
				# Step 4: Verify position is cleared
				if not SceneManager.has_next_spawn_position():
					add_test_result("Flow Step 4", "PASS", "Position properly cleared after use")
				else:
					add_test_result("Flow Step 4", "FAIL", "Position not cleared after use")
			else:
				add_test_result("Flow Step 3", "FAIL", "Map controller got zero position")
		else:
			add_test_result("Flow Step 1-2", "FAIL", "SceneManager has no spawn position")
		
		# Clean up
		context_manager.clear_teleport_context()
	else:
		add_test_result("Flow Test", "FAIL", "TeleportContextManager not available")

func add_test_result(test_name: String, result: String, details: String):
	test_results.append({
		"name": test_name,
		"result": result,
		"details": details
	})
	
	var status_icon = "✅" if result == "PASS" else "❌"
	print("%s %s: %s" % [status_icon, test_name, details])

func print_test_results():
	print("\n=== Player Positioning Test Results ===")
	
	var passed = 0
	var failed = 0
	
	for result in test_results:
		if result.result == "PASS":
			passed += 1
		else:
			failed += 1
	
	print("Total Tests: %d" % test_results.size())
	print("Passed: %d" % passed)
	print("Failed: %d" % failed)
	
	if failed == 0:
		print("🎉 All tests passed! Player positioning fix is working correctly.")
		print("\n✅ Expected Behavior:")
		print("- Players will spawn at correct teleport gate locations")
		print("- No more spawning in wrong positions or disappearing")
		print("- Consecutive teleportations work properly")
	else:
		print("⚠️ Some tests failed. Please review the implementation.")
		print("\n🔧 If tests fail, check:")
		print("- TeleportContextManager is properly loaded as autoload")
		print("- SceneManager spawn position methods work correctly")
		print("- Map controllers use get_and_clear_spawn_position()")
	
	print("\n🎮 To test in-game:")
	print("1. Load Lang Van Lang map")
	print("2. Use a teleport gate to go to Dong Dau")
	print("3. Player should appear near the destination gate, not in the middle of the map")
	print("4. Return to Lang Van Lang and repeat - should work consistently")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Re-running tests...")
		test_results.clear()
		run_positioning_tests()
