# Missing Assets Fix - Tạo fallback resources cho các assets bị thiếu
extends Node

# Missing assets that need fallbacks
var missing_assets = {
	"res://assets/images/ui/inventory_button.png": "UI Button",
	"res://assets/images/item/bag.png": "Inventory Bag",
	"res://assets/images/ui/teleport_gate_icon.png": "Teleport Gate Icon",
	"res://assets/sounds/teleport.ogg": "Teleport Sound",
	"res://assets/sounds/ui_click.ogg": "UI Click Sound"
}

# Available assets that can be used as alternatives
var alternative_assets = {
	"ui_button": "res://Legacy-Fantasy - High Forest 2.3/HUD/Base-01.png",
	"background": "res://assets/images/background/elements/background.png",
	"frame": "res://assets/images/background/screens/Frame.png"
}

func _ready():
	print("🔧 Missing Assets Fix System initialized")
	call_deferred("fix_missing_assets")

func fix_missing_assets():
	"""Fix all missing assets by creating fallbacks or using alternatives"""
	print("\n🔍 Checking for missing assets...")
	
	var fixed_count = 0
	var total_missing = 0
	
	for asset_path in missing_assets.keys():
		var asset_name = missing_assets[asset_path]
		
		if not FileAccess.file_exists(asset_path):
			total_missing += 1
			print("❌ Missing: %s (%s)" % [asset_name, asset_path])
			
			if _create_fallback_asset(asset_path, asset_name):
				fixed_count += 1
				print("✅ Created fallback for: %s" % asset_name)
			else:
				print("⚠️ Could not create fallback for: %s" % asset_name)
		else:
			print("✅ Found: %s" % asset_name)
	
	print("\n📊 Asset Fix Summary:")
	print("Total missing assets: %d" % total_missing)
	print("Fallbacks created: %d" % fixed_count)
	print("Success rate: %.1f%%" % ((fixed_count * 100.0 / total_missing) if total_missing > 0 else 100.0))

func _create_fallback_asset(asset_path: String, asset_name: String) -> bool:
	"""Create a fallback asset for missing resource"""
	var file_extension = asset_path.get_extension().to_lower()
	
	match file_extension:
		"png", "jpg", "jpeg":
			return _create_fallback_image(asset_path, asset_name)
		"ogg", "wav", "mp3":
			return _create_fallback_audio(asset_path, asset_name)
		_:
			print("⚠️ Unknown asset type: %s" % file_extension)
			return false

func _create_fallback_image(asset_path: String, asset_name: String) -> bool:
	"""Create a fallback image"""
	# Ensure directory exists
	var dir_path = asset_path.get_base_dir()
	if not DirAccess.dir_exists_absolute(dir_path):
		var dir = DirAccess.open("res://")
		if dir:
			dir.make_dir_recursive(dir_path)
	
	# Create a simple colored image based on asset type
	var image = Image.create(64, 64, false, Image.FORMAT_RGBA8)
	var color = _get_fallback_color(asset_name)
	image.fill(color)
	
	# Add simple pattern or text indicator
	_add_fallback_pattern(image, asset_name)
	
	# Save as PNG
	var error = image.save_png(asset_path)
	if error == OK:
		print("💾 Saved fallback image: %s" % asset_path)
		return true
	else:
		print("❌ Failed to save fallback image: %s (Error: %d)" % [asset_path, error])
		return false

func _create_fallback_audio(asset_path: String, asset_name: String) -> bool:
	"""Create a fallback audio file (placeholder)"""
	# For audio files, we'll create a simple sine wave or use silence
	# This is more complex in Godot, so we'll create a minimal audio resource
	
	# Ensure directory exists
	var dir_path = asset_path.get_base_dir()
	if not DirAccess.dir_exists_absolute(dir_path):
		var dir = DirAccess.open("res://")
		if dir:
			dir.make_dir_recursive(dir_path)
	
	# Create a simple AudioStreamGenerator as fallback
	var audio_stream = AudioStreamGenerator.new()
	audio_stream.mix_rate = 22050
	audio_stream.buffer_length = 0.1  # 100ms of silence
	
	# Save as resource file
	var resource_path = asset_path.replace(".ogg", ".tres").replace(".wav", ".tres").replace(".mp3", ".tres")
	var error = ResourceSaver.save(audio_stream, resource_path)
	
	if error == OK:
		print("💾 Saved fallback audio resource: %s" % resource_path)
		return true
	else:
		print("❌ Failed to save fallback audio: %s (Error: %d)" % [resource_path, error])
		return false

func _get_fallback_color(asset_name: String) -> Color:
	"""Get appropriate fallback color based on asset type"""
	var name_lower = asset_name.to_lower()
	
	if "button" in name_lower or "ui" in name_lower:
		return Color(0.3, 0.3, 0.5, 1.0)  # Blue-gray for UI elements
	elif "bag" in name_lower or "inventory" in name_lower:
		return Color(0.6, 0.4, 0.2, 1.0)  # Brown for inventory items
	elif "teleport" in name_lower or "gate" in name_lower:
		return Color(0.5, 0.2, 0.8, 1.0)  # Purple for teleport elements
	else:
		return Color(0.5, 0.5, 0.5, 1.0)  # Gray for unknown

func _add_fallback_pattern(image: Image, asset_name: String):
	"""Add a simple pattern to distinguish fallback images"""
	var width = image.get_width()
	var height = image.get_height()
	
	# Add border
	var border_color = Color(1, 1, 1, 1)  # White border
	
	# Top and bottom borders
	for x in range(width):
		image.set_pixel(x, 0, border_color)
		image.set_pixel(x, height - 1, border_color)
	
	# Left and right borders
	for y in range(height):
		image.set_pixel(0, y, border_color)
		image.set_pixel(width - 1, y, border_color)
	
	# Add diagonal lines for pattern
	for i in range(min(width, height)):
		if i % 4 == 0:  # Every 4th pixel
			image.set_pixel(i, i, border_color)
			if i < width and (height - 1 - i) >= 0:
				image.set_pixel(i, height - 1 - i, border_color)

func create_alternative_mappings():
	"""Create mappings to use existing assets as alternatives"""
	var mappings = {}
	
	# Map missing UI assets to existing ones
	if FileAccess.file_exists(alternative_assets["ui_button"]):
		mappings["res://assets/images/ui/inventory_button.png"] = alternative_assets["ui_button"]
		mappings["res://assets/images/ui/teleport_gate_icon.png"] = alternative_assets["ui_button"]
	
	# Map missing backgrounds to existing ones
	if FileAccess.file_exists(alternative_assets["background"]):
		mappings["res://assets/images/ui/missing_background.png"] = alternative_assets["background"]
	
	return mappings

func apply_alternative_mappings():
	"""Apply alternative asset mappings where possible"""
	var mappings = create_alternative_mappings()
	var applied_count = 0
	
	print("\n🔄 Applying alternative asset mappings...")
	
	for missing_path in mappings.keys():
		var alternative_path = mappings[missing_path]
		
		if not FileAccess.file_exists(missing_path) and FileAccess.file_exists(alternative_path):
			# Copy the alternative asset to the missing location
			var dir_path = missing_path.get_base_dir()
			if not DirAccess.dir_exists_absolute(dir_path):
				var dir = DirAccess.open("res://")
				if dir:
					dir.make_dir_recursive(dir_path)
			
			# Copy file
			var source_file = FileAccess.open(alternative_path, FileAccess.READ)
			var dest_file = FileAccess.open(missing_path, FileAccess.WRITE)
			
			if source_file and dest_file:
				dest_file.store_buffer(source_file.get_buffer(source_file.get_length()))
				source_file.close()
				dest_file.close()
				applied_count += 1
				print("✅ Mapped %s -> %s" % [missing_path.get_file(), alternative_path.get_file()])
			else:
				print("❌ Failed to copy %s to %s" % [alternative_path, missing_path])
	
	print("📊 Applied %d alternative mappings" % applied_count)

func validate_critical_assets() -> Dictionary:
	"""Validate that all critical assets are available"""
	var validation_result = {
		"total_assets": 0,
		"available_assets": 0,
		"missing_assets": [],
		"fallback_assets": [],
		"status": "UNKNOWN"
	}
	
	validation_result.total_assets = missing_assets.size()
	
	for asset_path in missing_assets.keys():
		var asset_name = missing_assets[asset_path]
		
		if FileAccess.file_exists(asset_path):
			validation_result.available_assets += 1
		else:
			validation_result.missing_assets.append(asset_name)
			
			# Check if fallback exists
			var fallback_path = asset_path.replace(".ogg", ".tres").replace(".wav", ".tres")
			if FileAccess.file_exists(fallback_path):
				validation_result.fallback_assets.append(asset_name)
	
	# Determine status
	var availability_rate = (validation_result.available_assets * 100.0 / validation_result.total_assets)
	if availability_rate >= 90:
		validation_result.status = "EXCELLENT"
	elif availability_rate >= 70:
		validation_result.status = "GOOD"
	elif availability_rate >= 50:
		validation_result.status = "FAIR"
	else:
		validation_result.status = "POOR"
	
	return validation_result

func print_validation_report():
	"""Print a comprehensive validation report"""
	var result = validate_critical_assets()
	
	print("\n" + "="*50)
	print("📊 ASSET VALIDATION REPORT")
	print("="*50)
	print("Total Critical Assets: %d" % result.total_assets)
	print("Available Assets: %d" % result.available_assets)
	print("Missing Assets: %d" % result.missing_assets.size())
	print("Fallback Assets: %d" % result.fallback_assets.size())
	print("Status: %s" % result.status)
	print("="*50)
	
	if result.missing_assets.size() > 0:
		print("\n❌ Missing Assets:")
		for asset in result.missing_assets:
			print("  - %s" % asset)
	
	if result.fallback_assets.size() > 0:
		print("\n🔄 Fallback Assets:")
		for asset in result.fallback_assets:
			print("  - %s" % asset)

# Public API
func run_complete_asset_fix():
	"""Run complete asset fixing process"""
	print("🚀 Starting complete asset fix process...")
	
	# Step 1: Apply alternative mappings
	apply_alternative_mappings()
	
	# Step 2: Create fallbacks for remaining missing assets
	fix_missing_assets()
	
	# Step 3: Validate results
	print_validation_report()
	
	print("✅ Complete asset fix process finished!")
