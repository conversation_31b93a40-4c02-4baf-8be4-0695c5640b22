# Spatial Consistency Teleport System Documentation

## 🌟 Overview

The Spatial Consistency Teleport System creates a logical, connected world experience where players feel like they're moving through physically connected spaces rather than jumping between disconnected scenes.

## 🗺️ World Layout

The game world follows this spatial relationship:

```
     [<PERSON><PERSON>]
          |
    [Hang <PERSON>] ←→ [<PERSON><PERSON>] ←→ [<PERSON> Dau] ←→ [Doi Tre]
          ↑           ↑              ↑
          └─────── [<PERSON>] ──┘
                   (Central Hub)
```

## 🎯 Key Principles

### 1. Directional Naming Convention
- **OLD**: `lang_van_lang_to_dong_dau`
- **NEW**: `lang_van_lang_left_exit`

### 2. Spatial Logic
- Exiting LEFT → Spawn at RIGHT entrance of destination
- Exiting RIGHT → Spawn at LEFT entrance of destination
- Exiting NORTH → Spawn at SOUTH entrance of destination
- Exiting SOUTH → Spawn at NORTH entrance of destination

### 3. Visual Indicators
- Gate names include directional arrows: "ĐÔNG →", "TÂY ←"
- Interaction prompts show direction: "Nhấn [ENTER] để đi về phía ĐÔNG"

## 📁 Updated Files

### Core System Files
- `spatial_consistency_framework.gd` - Main framework
- `systems/spatial_teleport_system.gd` - Position mappings
- `spatial_teleport_test.gd` - Testing system

### Updated Teleport Gates
- `maps/doi_tre/scenes/TeleportGate_DoiTre.tscn` ✅
  - **OLD**: `doi_tre_to_dong_dau`
  - **NEW**: `doi_tre_right_exit`
  - **Spawn**: Vector2(-2400, -400) (LEFT side of Dong Dau)

- `maps/dong_dau/scenes/TeleportGate_DongDau_DoiTre.tscn` ✅
  - **OLD**: `dong_dau_to_doi_tre`
  - **NEW**: `dong_dau_left_exit`
  - **Spawn**: Vector2(1400, -400) (RIGHT side of Doi Tre)

- `maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DongDau.tscn` ✅
  - **OLD**: `lang_van_lang_to_dong_dau`
  - **NEW**: `lang_van_lang_left_exit`
  - **Spawn**: Vector2(-2000, -400) (LEFT side of Dong Dau)

- `maps/dong_dau/scenes/TeleportGate_DongDau_LangVanLang.tscn` ✅
  - **OLD**: `dong_dau_to_lang_van_lang`
  - **NEW**: `dong_dau_right_exit`
  - **Spawn**: Vector2(300, -2000) (LEFT side of Lang Van Lang)

### Updated Configuration Scripts
- `maps/lang_van_lang/scripts/lang_van_lang_teleport_system.gd` ✅
  - Updated to use `spatial_gates_config`
  - Added `SpatialTeleportSystem` integration

## 🚀 How to Use

### For Developers

1. **Adding New Gates**:
```gdscript
# Use the spatial system
var spatial_system = SpatialTeleportSystem.new()
var spawn_pos = spatial_system.get_spawn_position("map_direction_exit")
```

2. **Gate Naming Pattern**:
```
{map_name}_{direction}_exit
Examples:
- lang_van_lang_left_exit
- dong_dau_right_exit
- doi_tre_north_exit
```

3. **Position Calculation**:
```gdscript
# Automatic spatial positioning
var gate_pos = spatial_system.get_gate_position(gate_id)
var spawn_pos = spatial_system.get_spawn_position(gate_id)
```

### For Content Creators

1. **Gate Placement Rules**:
   - LEFT gates: Near left edge of map
   - RIGHT gates: Near right edge of map
   - NORTH gates: Near top edge of map
   - SOUTH gates: Near bottom edge of map

2. **Spawn Position Rules**:
   - LEFT exit → RIGHT entrance (opposite side)
   - Maintains spatial illusion of connected world

## 🧪 Testing

Run the spatial test system:
```gdscript
# Add to scene
var test_system = preload("res://spatial_teleport_test.gd").new()
add_child(test_system)

# Manual testing controls:
# ENTER - Re-run tests
# ESCAPE - Print mappings
```

## 🎯 Benefits

1. **Immersive Experience**: Players feel like moving through connected spaces
2. **Logical Navigation**: Directional consistency reduces confusion
3. **Scalable System**: Easy to add new maps and connections
4. **Visual Clarity**: Clear directional indicators and arrows

## 🔧 Maintenance

### Adding New Maps
1. Define spatial relationship in `spatial_consistency_framework.gd`
2. Add position mappings in `spatial_teleport_system.gd`
3. Create gates with directional naming
4. Test with `spatial_teleport_test.gd`

### Troubleshooting
- **Gates not working**: Check gate_id matches spatial mappings
- **Wrong spawn position**: Verify position_mappings in spatial system
- **Missing reciprocal connection**: Ensure both directions are defined

## 📊 Current Status

✅ **Completed**:
- Core spatial framework
- Doi Tre ↔ Dong Dau connection
- Lang Van Lang ↔ Dong Dau connection
- Testing system
- Documentation

🔄 **In Progress**:
- Remaining gate updates
- Full system integration

🎯 **Next Steps**:
- Update all remaining teleport gates
- Integrate with existing map controllers
- Add visual improvements (arrows, better UI)

## 🎮 Player Experience

**Before**: "I'm teleporting to random locations"
**After**: "I'm walking east through a tunnel to the next area"

The spatial consistency system transforms disconnected teleportation into intuitive spatial navigation, creating a more immersive and logical game world.
