extends Node
class_name ItemManager

const item: Dictionary = {
	"Armor": preload("res://Items/Weapon/Resource/aogiap.tres"),
	"Pant": preload("res://Items/Weapon/Resource/quangiap.tres"),
	"arrow": preload("res://Items/Weapon/Resource/cung.tres"),
	"Sword": preload("res://Items/Weapon/Resource/kiem.tres"),
	"Spear": preload("res://Items/Weapon/Resource/thuong.tres"),
}

func get_current_item(name: String):
	return item[name]
