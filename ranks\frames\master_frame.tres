[gd_resource type="SpriteFrames" load_steps=14 format=3 uid="uid://3nwp2afkrjem"]

[sub_resource type="AtlasTexture" id="AtlasTexture_wsn1e"]
region = Rect2(0, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_1s726"]
region = Rect2(64, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_i70dy"]
region = Rect2(128, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2fjbu"]
region = Rect2(192, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xp5lf"]
region = Rect2(256, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ipkip"]
region = Rect2(320, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_uxdn6"]
region = Rect2(384, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tw7jy"]
region = Rect2(448, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_f3oh6"]
region = Rect2(512, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_dfjfa"]
region = Rect2(576, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_podig"]
region = Rect2(640, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_u0wyl"]
region = Rect2(704, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_rhrad"]
region = Rect2(768, 128, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_wsn1e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1s726")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i70dy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2fjbu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xp5lf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ipkip")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uxdn6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tw7jy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f3oh6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dfjfa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_podig")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u0wyl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rhrad")
}],
"loop": true,
"name": &"burst",
"speed": 10.0
}]
