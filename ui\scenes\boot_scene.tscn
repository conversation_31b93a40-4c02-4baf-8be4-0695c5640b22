[gd_scene load_steps=4 format=3 uid="uid://bvd6jq1e0euwu"]

[ext_resource type="Script" path="res://ui/scripts/boot_scene.gd" id="1_c83yh"]
[ext_resource type="VideoStream" path="res://assets/video/boot_scene.ogv" id="2_vc0rk"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4f7h2"]
bg_color = Color(0.14902, 0.14902, 0.14902, 0.74902)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="BootScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_c83yh")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)

[node name="VideoPlayer" type="VideoStreamPlayer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
stream = ExtResource("2_vc0rk")
expand = true

[node name="TopRightButtons" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -164.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 60.0
grow_horizontal = 0
theme_override_constants/separation = 8
alignment = 2

[node name="SpeedButton" type="Button" parent="TopRightButtons"]
custom_minimum_size = Vector2(60, 40)
layout_mode = 2
theme_override_styles/normal = SubResource("StyleBoxFlat_4f7h2")
text = "2x"

[node name="SkipButton" type="Button" parent="TopRightButtons"]
custom_minimum_size = Vector2(60, 40)
layout_mode = 2
theme_override_styles/normal = SubResource("StyleBoxFlat_4f7h2")
text = "Skip"
