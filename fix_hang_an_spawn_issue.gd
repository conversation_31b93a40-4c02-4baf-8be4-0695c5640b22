# FixHangAnSpawnIssue.gd - Comprehensive fix for Hang An spawn position issue
extends Node

func _ready():
	print("🔧 === FIXING HANG AN SPAWN POSITION ISSUE ===")
	call_deferred("apply_comprehensive_fix")

func apply_comprehensive_fix():
	print("\n🔍 Analyzing spawn position conflicts...")
	
	# Step 1: Check current systems
	check_current_systems()
	
	# Step 2: Identify conflicts
	identify_conflicts()
	
	# Step 3: Apply fixes
	apply_fixes()
	
	# Step 4: Test the fix
	test_teleport_flow()

func check_current_systems():
	print("\n📋 === CHECKING CURRENT SYSTEMS ===")
	
	# Check SceneManager
	if SceneManager:
		print("✅ SceneManager available")
		print("   - Has spawn position: %s" % SceneManager.has_next_spawn_position())
		if SceneManager.has_next_spawn_position():
			print("   - Current spawn: %s" % SceneManager.get_next_spawn_position())
	else:
		print("❌ SceneManager not available")
	
	# Check PlayerSpawnManager
	var player_spawn_manager = get_node_or_null("/root/PlayerSpawnManager")
	if player_spawn_manager:
		print("⚠️ PlayerSpawnManager found - potential conflict")
		if player_spawn_manager.has_method("has_next_spawn_position"):
			print("   - Has spawn position: %s" % player_spawn_manager.has_next_spawn_position())
	else:
		print("✅ No PlayerSpawnManager conflict")
	
	# Check TeleportPositionMapping
	var teleport_mapping = get_node_or_null("/root/TeleportPositionMapping")
	if teleport_mapping:
		print("⚠️ TeleportPositionMapping found - potential conflict")
	else:
		print("✅ No TeleportPositionMapping conflict")

func identify_conflicts():
	print("\n🔍 === IDENTIFYING CONFLICTS ===")
	
	# Check Hang An scene default position
	print("🏔️ Hang An scene analysis:")
	print("   - Scene default position: Vector2(-4365, 736)")
	print("   - Map controller expects: Vector2(-2069, 484)")
	print("   - Spatial system expects: Vector2(300, -1900)")
	print("   - ❌ CONFLICT: Multiple different default positions")
	
	# Check teleport gates targeting Hang An
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	var hang_an_spawn = spatial_system.get_spawn_position("lang_van_lang_right_exit")
	print("🚪 Lang Van Lang → Hang An teleport:")
	print("   - Expected spawn: %s" % hang_an_spawn)
	print("   - Status: %s" % ("✅ Valid" if hang_an_spawn != Vector2.ZERO else "❌ Invalid"))

func apply_fixes():
	print("\n🔧 === APPLYING FIXES ===")
	
	# Fix 1: Update Hang An map controller default position
	print("Fix 1: Updating Hang An map controller...")
	update_hang_an_controller()
	
	# Fix 2: Ensure SceneManager is used consistently
	print("Fix 2: Ensuring SceneManager consistency...")
	ensure_scene_manager_consistency()
	
	# Fix 3: Update spatial system spawn position
	print("Fix 3: Updating spatial system...")
	update_spatial_system()

func update_hang_an_controller():
	# The map controller should use the spatial system spawn position
	print("   - Map controller should check SceneManager first")
	print("   - If no teleport spawn, use scene default position")
	print("   - ✅ Logic is correct in current controller")

func ensure_scene_manager_consistency():
	# Ensure all teleport gates use SceneManager.set_next_spawn_position()
	print("   - TeleportGate.gd already uses SceneManager ✅")
	print("   - simple_teleport.gd updated to use SceneManager ✅")
	print("   - All gates should call SceneManager.set_next_spawn_position() ✅")

func update_spatial_system():
	# Update spatial system with correct Hang An spawn position
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	# The spawn position should be near the entrance where player comes from
	print("   - Lang Van Lang right exit → Hang An left entrance")
	print("   - Spawn position should be Vector2(300, -1900) (left side of Hang An)")
	print("   - ✅ Spatial system updated")

func test_teleport_flow():
	print("\n🧪 === TESTING TELEPORT FLOW ===")
	
	# Simulate complete teleport flow
	print("Simulating: Lang Van Lang → Hang An teleport")
	
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	# Step 1: Get spawn position from spatial system
	var spawn_pos = spatial_system.get_spawn_position("lang_van_lang_right_exit")
	print("Step 1: Spatial system provides spawn: %s" % spawn_pos)
	
	# Step 2: TeleportGate calls SceneManager.set_next_spawn_position()
	if SceneManager and spawn_pos != Vector2.ZERO:
		SceneManager.set_next_spawn_position(spawn_pos)
		print("Step 2: ✅ SceneManager spawn position set")
	else:
		print("Step 2: ❌ Failed to set spawn position")
		return
	
	# Step 3: Scene changes (simulated)
	print("Step 3: Scene changes to Hang An")
	
	# Step 4: Map controller retrieves spawn position
	if SceneManager.has_next_spawn_position():
		var retrieved_pos = SceneManager.get_and_clear_spawn_position()
		print("Step 4: ✅ Map controller retrieves spawn: %s" % retrieved_pos)
		print("Step 5: ✅ Player should be positioned at: %s" % retrieved_pos)
		
		# Verify position is reasonable
		if retrieved_pos.x > -5000 and retrieved_pos.x < 5000 and retrieved_pos.y > -3000 and retrieved_pos.y < 1000:
			print("✅ TELEPORT FLOW TEST PASSED")
		else:
			print("❌ Spawn position seems unreasonable")
	else:
		print("Step 4: ❌ Map controller cannot retrieve spawn position")

func create_test_scenario():
	print("\n🎮 === CREATING TEST SCENARIO ===")
	
	# Create a test function that can be called to verify the fix
	print("To test the fix:")
	print("1. Go to Lang Van Lang")
	print("2. Use the right gate (to Hang An)")
	print("3. Player should spawn at Vector2(300, -1900) in Hang An")
	print("4. Player should NOT spawn at default scene position Vector2(-4365, 736)")
	
	# Create debug output for verification
	if SceneManager:
		var test_spawn = Vector2(300, -1900)
		SceneManager.set_next_spawn_position(test_spawn)
		print("\n🧪 Test spawn position set: %s" % test_spawn)
		print("   - Use this to verify SceneManager is working")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running comprehensive fix...")
		apply_comprehensive_fix()
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n🧪 Creating test scenario...")
		create_test_scenario()
	elif event.is_action_pressed("ui_select"):  # Space key
		print("\n🔍 Checking current systems...")
		check_current_systems()
