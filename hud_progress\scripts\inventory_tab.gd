extends Control

@onready var tab_container: HBoxContainer = $CanvasLayer/All_Tab_Button
@onready var nhiem_vu_button: TextureButton = $CanvasLayer/All_Tab_Button/Mission_Button
@onready var hanh_trang_button: TextureButton = $CanvasLayer/All_Tab_Button/Inventory_Button
@onready var ky_nang_button: TextureButton = $CanvasLayer/All_Tab_Button/Skills_Button
@onready var khac_button: TextureButton = $CanvasLayer/All_Tab_Button/Etc_Button
@onready var close_button: TextureButton = $CanvasLayer/Close_Button

func _ready() -> void:
	# Initialize with retry logic to handle loading order issues
	call_deferred("_initialize_ui_components")

func _initialize_ui_components() -> void:
	"""Initialize UI components with fallback search strategies"""
	var missing_components = []

	# Check and find components with fallback strategies
	if not tab_container:
		tab_container = _find_node_with_fallbacks(["CanvasLayer/All_Tab_Button", "All_Tab_Button", "TabContainer"])
		if not tab_container:
			missing_components.append("Tab Container")

	if not nhiem_vu_button:
		nhiem_vu_button = _find_node_with_fallbacks(["CanvasLayer/All_Tab_Button/Mission_Button", "Mission_Button", "MissionButton"])
		if not nhiem_vu_button:
			missing_components.append("Mission Button")

	if not hanh_trang_button:
		hanh_trang_button = _find_node_with_fallbacks(["CanvasLayer/All_Tab_Button/Inventory_Button", "Inventory_Button", "InventoryButton"])
		if not hanh_trang_button:
			missing_components.append("Inventory Button")

	if not ky_nang_button:
		ky_nang_button = _find_node_with_fallbacks(["CanvasLayer/All_Tab_Button/Skills_Button", "Skills_Button", "SkillsButton"])
		if not ky_nang_button:
			missing_components.append("Skills Button")

	if not khac_button:
		khac_button = _find_node_with_fallbacks(["CanvasLayer/All_Tab_Button/Etc_Button", "Etc_Button", "EtcButton"])
		if not khac_button:
			missing_components.append("Etc Button")

	if not close_button:
		close_button = _find_node_with_fallbacks(["CanvasLayer/Close_Button", "Close_Button", "CloseButton"])
		if not close_button:
			missing_components.append("Close Button")

	# Report missing components but don't crash
	if missing_components.size() > 0:
		print("⚠️ Inventory Tab: Missing UI components: " + str(missing_components))
		print("   Inventory tab will have limited functionality")
	else:
		print("✅ Inventory Tab: All UI components found successfully")
		_setup_button_connections()

func _find_node_with_fallbacks(paths: Array) -> Node:
	"""Find node using multiple path strategies"""
	for path in paths:
		var node = get_node_or_null(path)
		if node:
			return node

		# Try searching in scene tree
		node = get_tree().current_scene.find_child(path.get_file(), true, false)
		if node:
			return node

	return null

func _setup_button_connections() -> void:
	"""Setup button connections if buttons exist"""
	if nhiem_vu_button:
		if not nhiem_vu_button.pressed.is_connected(_on_mission_button_pressed):
			nhiem_vu_button.pressed.connect(_on_mission_button_pressed)

	if hanh_trang_button:
		if not hanh_trang_button.pressed.is_connected(_on_inventory_button_pressed):
			hanh_trang_button.pressed.connect(_on_inventory_button_pressed)

	if ky_nang_button:
		if not ky_nang_button.pressed.is_connected(_on_skills_button_pressed):
			ky_nang_button.pressed.connect(_on_skills_button_pressed)

	if khac_button:
		if not khac_button.pressed.is_connected(_on_etc_button_pressed):
			khac_button.pressed.connect(_on_etc_button_pressed)

	if close_button:
		if not close_button.pressed.is_connected(_on_close_button_pressed):
			close_button.pressed.connect(_on_close_button_pressed)

# Button handler functions
func _on_mission_button_pressed() -> void:
	print("Mission button pressed")
	# TODO: Switch to mission tab

func _on_inventory_button_pressed() -> void:
	print("Inventory button pressed")
	# TODO: Switch to inventory tab

func _on_skills_button_pressed() -> void:
	print("Skills button pressed")
	# TODO: Switch to skills tab

func _on_etc_button_pressed() -> void:
	print("Etc button pressed")
	# TODO: Switch to etc tab

# _on_close_button_pressed already exists below

	# Kết nối tín hiệu pressed của từng nút
	nhiem_vu_button.connect("pressed", _on_nhiem_vu_button_pressed)
	hanh_trang_button.connect("pressed", _on_hanh_trang_button_pressed)
	ky_nang_button.connect("pressed", _on_ky_nang_button_pressed)
	khac_button.connect("pressed", _on_khac_button_pressed)
	close_button.connect("pressed", _on_close_button_pressed)

func _on_hanh_trang_button_pressed() -> void:

	pass

func _on_nhiem_vu_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/missions_tab.tscn")

func _on_ky_nang_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/skills_tab.tscn")

func _on_khac_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/etc_tab.tscn")

func _on_close_button_pressed() -> void:
	queue_free()  # Xóa node Iventory_Tab khỏi Scene Tree
