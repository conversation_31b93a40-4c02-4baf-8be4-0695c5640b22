[gd_scene load_steps=6 format=3 uid="uid://beu5of83qkniu"]

[ext_resource type="Texture2D" uid="uid://cov5k51auae2s" path="res://assets/images/item/potion_health.png" id="1_k051w"]
[ext_resource type="Script" path="res://Items/Potion/scripts/potion_drop.gd" id="2_ljgqj"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1nwlq"]
size = Vector2(13.25, 11.25)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_axvde"]
size = Vector2(15.5, 13.5)

[sub_resource type="CircleShape2D" id="CircleShape2D_pickup"]
radius = 30.0

[node name="potion_drop" type="RigidBody2D"]
collision_layer = 32
collision_mask = 17
script = ExtResource("2_ljgqj")

[node name="Icon" type="Sprite2D" parent="."]
position = Vector2(-1, -1)
scale = Vector2(0.5, 0.5)
texture = ExtResource("1_k051w")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-1, -1)
shape = SubResource("RectangleShape2D_1nwlq")

[node name="DespawnTimer" type="Timer" parent="."]

[node name="ClickArea" type="Area2D" parent="."]
collision_layer = 32
collision_mask = 129

[node name="CollisionShape2D" type="CollisionShape2D" parent="ClickArea"]
position = Vector2(-1, -1)
shape = SubResource("RectangleShape2D_axvde")

[node name="ArrowIndicator" type="Polygon2D" parent="."]
visible = false
position = Vector2(-1, -20)
color = Color(1, 0.921569, 0, 1)
polygon = PackedVector2Array(0, -10, 5, 0, -5, 0)

[node name="PickupArea" type="Area2D" parent="."]
collision_layer = 32

[node name="CollisionShape2D" type="CollisionShape2D" parent="PickupArea"]
shape = SubResource("CircleShape2D_pickup")
