extends TextureButton

signal hovered(title: String, description: String)
signal unhovered

@export_file("*.tscn") var target_scene: String = "res://maps/suoi_thieng/scenes/suoi_thieng.tscn"
@export var button_title: String = "Ng<PERSON><PERSON>"
@export var button_description: String = "<PERSON>hu vực nổi tiếng với trống đồng <PERSON>."
@export var is_locked: bool = false

func _ready():
    mouse_entered.connect(_on_mouse_entered)
    mouse_exited.connect(_on_mouse_exited)
    pressed.connect(_on_pressed)

func _on_mouse_entered():
    if is_locked:
        hovered.emit(button_title, "<PERSON>hu vực này hiện đang bị khóa.")
    else:
        hovered.emit(button_title, button_description)

func _on_mouse_exited():
    unhovered.emit()

func _on_pressed():
    if is_locked:
        print("This area is locked!")
        return
    if not FileAccess.file_exists(target_scene):
        push_error("Cannot load target scene: " + target_scene)
        return
    await get_tree().create_timer(0.1).timeout
    SceneManager.goto_scene(target_scene) 