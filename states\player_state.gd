extends Node
class_name PlayerState

func get_state_name():
	return ""

# Called when entering the state.
func enter(owner: Player) -> void:
	pass

# Called before exiting the state.
func exit(owner: Player) -> void:
	pass

# Optional: handle input events for this state.
func handle_input(owner: Player, event: InputEvent) -> void:
	pass

# Update logic for this state; called from _physics_process or _process.
func update(owner: Player, delta: float) -> void:
	pass
