extends Node2D

# Signal emitted when an enemy is spawned
signal enemy_spawned

# Thêm signal dị<PERSON> chuyể<PERSON>
signal player_teleported

# Visual properties
@export var active_color: Color = Color(0.8, 0.2, 0.2, 0.5)
@export var inactive_color: Color = Color(0.5, 0.5, 0.5, 0.3)
@export var gate_name: String = "Enemy Gate"
# Thêm biến export cho dịch chuyển
@export_file("*.tscn") var target_scene: String = ""
@export var target_position: Vector2 = Vector2.ZERO
@export var interaction_key: String = "teleport_interact"  # Mặc định phím Enter

# Reference to the gate visual
@onready var gate_visual = $GateVisual
@onready var spawn_particles = $SpawnParticles
@onready var label = $Label
# Thêm node Area2D và prompt UI nếu có
@onready var area = $SpawnArea if has_node("SpawnArea") else null
@onready var prompt_label = $Prompt if has_node("Prompt") else null

# State
var is_active = false
var _player_inside = false
var _current_player = null

func _ready():
	# Set initial state
	set_active(false)
	label.text = gate_name

	# Add to appropriate group based on name or position
	if "Left" in name or "West" in gate_name:
		add_to_group("left_enemy_gate")
	elif "Right" in name or "East" in gate_name:
		add_to_group("right_enemy_gate")

	# Kết nối Area2D nếu có
	if area:
		area.body_entered.connect(_on_body_entered)
		area.body_exited.connect(_on_body_exited)
	# Thiết lập phím Enter nếu chưa có
	if not InputMap.has_action(interaction_key):
		InputMap.add_action(interaction_key)
		var event = InputEventKey.new()
		event.keycode = KEY_ENTER
		InputMap.action_add_event(interaction_key, event)

# Set the gate's active state
func set_active(active: bool):
	is_active = active
	gate_visual.color = active_color if active else inactive_color

# Trigger the spawn effect
func trigger_spawn_effect():
	if not is_active:
		set_active(true)

	print("Gate " + gate_name + " triggered spawn effect!")

	# Play particle effect
	spawn_particles.restart()
	spawn_particles.emitting = true

	# Optional: Add animation or sound effect here

	# Emit signal
	emit_signal("enemy_spawned")

	# Reset after a delay
	await get_tree().create_timer(2.0).timeout
	if not is_active:
		set_active(false)

# ======= Dịch chuyển player =======
func _on_body_entered(body):
	if body is Player:
		_player_inside = true
		_current_player = body
		if prompt_label:
			prompt_label.text = "Nhấn [ENTER]để dịch chuyển"
			prompt_label.visible = true

func _on_body_exited(body):
	if body is Player:
		_player_inside = false
		_current_player = null
		if prompt_label:
			prompt_label.visible = false

func _process(delta):
	if _player_inside and Input.is_action_just_pressed(interaction_key):
		_teleport_player()

func _teleport_player():
	if not _current_player or target_scene == "":
		return
	# Ẩn prompt
	if prompt_label:
		prompt_label.visible = false
	# (Tùy chọn) Lưu trạng thái player, hiệu ứng...
	# Chuyển scene
	var autoload_settings = ProjectSettings.get_setting("autoload")
	if autoload_settings != null and autoload_settings is Dictionary and autoload_settings.has("SceneManager") and SceneManager:
		SceneManager.goto_scene(target_scene)
	else:
		get_tree().change_scene_to_file(target_scene)
	# (Tùy chọn) Đặt vị trí spawn nếu cần
	# Gửi signal
	emit_signal("player_teleported")
