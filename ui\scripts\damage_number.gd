extends Node

# Lớp DamageNumber để hiển thị số sát thương
class DamageNumber extends Node2D:
	# <PERSON><PERSON><PERSON> thuộc tính hiển thị
	var value: float = 0
	var is_critical: bool = false
	var color: Color = Color.WHITE
	var font_size: int = 24
	var float_speed: float = 70.0
	var fade_duration: float = 0.7
	var scale_duration: float = 0.3
	var max_scale: float = 1.5

	# Biến nội bộ
	var _label: Label
	var _lifetime: float = 0
	var _total_lifetime: float = 1.0
	var _velocity: Vector2 = Vector2.ZERO
	var _initial_position: Vector2

	func _ready() -> void:
		# Tạo label để hiển thị số sát thương
		_label = Label.new()
		_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		add_child(_label)

		# Thiết lập thuộc tính ban đầu
		_label.text = str(int(value))
		_label.add_theme_font_size_override("font_size", font_size)
		_label.add_theme_color_override("font_color", color)

		# Lưu vị trí ban đầu
		_initial_position = position

		# Thiết lập vận tốc ban đầu (hướng lên và ngẫu nhiên sang trái/phải)
		var random_x = randf_range(-30.0, 30.0)
		_velocity = Vector2(random_x, -float_speed)

		# Hiệu ứng phóng to ban đầu
		scale = Vector2.ZERO
		var tween = create_tween()
		tween.tween_property(self, "scale", Vector2(max_scale, max_scale), scale_duration)
		tween.tween_property(self, "scale", Vector2.ONE, scale_duration)

		# Nếu là đòn chí mạng, làm cho số lớn hơn và màu khác
		if is_critical:
			_label.add_theme_font_size_override("font_size", font_size * 1.5)
			_label.add_theme_color_override("font_color", Color.YELLOW)
			_label.text = str(int(value)) + "!"

		# Đảm bảo số sát thương hiển thị trên cùng
		z_index = 100

	func _process(delta: float) -> void:
		# Cập nhật vị trí
		position += _velocity * delta

		# Giảm dần vận tốc theo thời gian
		_velocity.y += 50.0 * delta  # Trọng lực nhẹ

		# Cập nhật thời gian sống
		_lifetime += delta

		# Hiệu ứng mờ dần khi gần hết thời gian
		if _lifetime > _total_lifetime - fade_duration:
			var alpha = 1.0 - (_lifetime - (_total_lifetime - fade_duration)) / fade_duration
			_label.modulate.a = alpha

		# Xóa khi hết thời gian
		if _lifetime >= _total_lifetime:
			queue_free()

# Hàm tạo số sát thương mới
func create_damage_number(position: Vector2, value: float, is_critical: bool = false, color: Color = Color.WHITE) -> void:
	print("Creating damage number: ", value, " at position: ", position)

	var damage_number = DamageNumber.new()
	damage_number.position = position
	damage_number.value = value
	damage_number.is_critical = is_critical
	damage_number.color = color

	# Thiết lập thời gian sống dài hơn cho đòn chí mạng
	if is_critical:
		damage_number._total_lifetime = 1.5

	# Thêm vào cảnh hiện tại
	if get_tree() and get_tree().current_scene:
		get_tree().current_scene.add_child(damage_number)
	else:
		print("ERROR: Could not add damage number to scene - current_scene is null")
