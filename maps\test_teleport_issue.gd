# Test script để kiểm tra vấn đề teleport rung_nuong -> dong_dau
extends Node

func _ready():
	print("🧪 === TELEPORT ISSUE DIAGNOSIS ===")
	call_deferred("run_diagnosis")

func run_diagnosis():
	await get_tree().process_frame
	
	print("\n📋 Checking Teleport Configuration...")
	check_teleport_config()
	
	print("\n📋 Checking Scene Structure...")
	check_scene_structure()
	
	print("\n📋 Checking Player Management...")
	check_player_management()
	
	print("\n📋 Checking SceneManager...")
	check_scene_manager()

func check_teleport_config():
	"""Kiểm tra cấu hình teleport gate"""
	
	# Check RungNuong teleport gate
	var rung_nuong_gate_path = "res://maps/rung_nuong/scenes/TeleportGate_RungNuong.tscn"
	if ResourceLoader.exists(rung_nuong_gate_path):
		print("✅ RungNuong teleport gate exists")
		
		var gate_scene = load(rung_nuong_gate_path)
		if gate_scene:
			print("✅ RungNuong gate loads successfully")
			# Note: Can't check properties without instantiating
		else:
			print("❌ RungNuong gate FAILED to load")
	else:
		print("❌ RungNuong teleport gate NOT FOUND")
	
	# Check target scene
	var target_scene = "res://maps/dong_dau/scenes/dong_dau.tscn"
	if ResourceLoader.exists(target_scene):
		print("✅ Target scene (dong_dau) exists")
	else:
		print("❌ Target scene (dong_dau) NOT FOUND")

func check_scene_structure():
	"""Kiểm tra cấu trúc scene"""
	
	# Check dong_dau scene structure
	print("📁 Checking dong_dau scene structure...")
	var dong_dau_path = "res://maps/dong_dau/scenes/dong_dau.tscn"
	if ResourceLoader.exists(dong_dau_path):
		print("✅ dong_dau scene exists")
		
		var scene = load(dong_dau_path)
		if scene:
			print("✅ dong_dau scene loads successfully")
			
			# Try to instantiate to check structure
			var instance = scene.instantiate()
			if instance:
				print("✅ dong_dau scene instantiates successfully")
				
				# Check for player
				var player = instance.find_child("Player", true, false)
				if player:
					print("✅ Player found in dong_dau scene")
					print("   Player position: %s" % player.position)
				else:
					print("❌ Player NOT FOUND in dong_dau scene")
				
				# Check for map controller
				var controller = instance.find_child("DongDauMapController", true, false)
				if controller:
					print("✅ DongDauMapController found")
				else:
					print("❌ DongDauMapController NOT FOUND")
				
				instance.queue_free()
			else:
				print("❌ dong_dau scene FAILED to instantiate")
		else:
			print("❌ dong_dau scene FAILED to load")
	else:
		print("❌ dong_dau scene NOT FOUND")

func check_player_management():
	"""Kiểm tra hệ thống quản lý player"""
	
	print("👤 Checking player management systems...")
	
	# Check current scene for player
	var current_player = get_tree().get_first_node_in_group("player")
	if current_player:
		print("✅ Player found in current scene: %s" % current_player.name)
		print("   Position: %s" % current_player.global_position)
	else:
		print("❌ No player found in current scene")
	
	# Check player scene file
	var player_scene_path = "res://player/player.tscn"
	if ResourceLoader.exists(player_scene_path):
		print("✅ Player scene file exists")
	else:
		print("❌ Player scene file NOT FOUND")

func check_scene_manager():
	"""Kiểm tra SceneManager"""
	
	print("🔄 Checking SceneManager...")
	
	if SceneManager:
		print("✅ SceneManager autoload exists")
		
		# Check methods
		var required_methods = ["goto_scene", "set_next_spawn_position"]
		for method in required_methods:
			if SceneManager.has_method(method):
				print("✅ SceneManager.%s() exists" % method)
			else:
				print("❌ SceneManager.%s() MISSING" % method)
	else:
		print("❌ SceneManager autoload NOT FOUND")

# Suggested fixes
func suggest_fixes():
	print("\n🔧 SUGGESTED FIXES:")
	print("1. Đảm bảo target_position trong RungNuong gate gần với vị trí player mặc định trong dong_dau")
	print("2. Thêm logic auto-positioning trong DongDauMapController")
	print("3. Kiểm tra SceneManager có handle player spawn đúng không")
	print("4. Test teleport với debug logging để theo dõi player position")

func test_teleport_coordinates():
	print("\n📍 COORDINATE ANALYSIS:")
	print("RungNuong Gate Target: Vector2(-1200, -429)")
	print("DongDau Player Default: Vector2(-1421, -429)")
	print("Difference: %s" % (Vector2(-1200, -429) - Vector2(-1421, -429)))
	print("✅ Coordinates are close - should work!")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		suggest_fixes()
		test_teleport_coordinates()
