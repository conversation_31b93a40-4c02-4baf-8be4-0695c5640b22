# consecutive_teleportation_test.gd - Test for consecutive teleportation functionality
extends Node

var test_results = []

func _ready():
	print("🧪 Consecutive Teleportation Test - Testing multiple teleportations and gate positioning")
	await get_tree().process_frame  # Wait for autoloads to load
	run_consecutive_teleportation_tests()

func run_consecutive_teleportation_tests():
	print("\n=== Testing Consecutive Teleportation Fix ===")
	
	# Test 1: TeleportPositionMapping accuracy
	test_position_mapping_accuracy()
	
	# Test 2: Gate state management
	test_gate_state_management()
	
	# Test 3: Context manager clearing
	test_context_manager_clearing()
	
	# Test 4: Specific route testing (dong_dau -> doi_tre)
	test_specific_route_positioning()
	
	# Test 5: Consecutive teleportation simulation
	test_consecutive_teleportation_simulation()
	
	# Print final results
	print_test_results()

func test_position_mapping_accuracy():
	print("\n--- Test 1: Position Mapping Accuracy ---")
	
	if not TeleportPositionMapping:
		add_test_result("TeleportPositionMapping", "FAIL", "TeleportPositionMapping not found")
		return
	
	add_test_result("TeleportPositionMapping", "PASS", "TeleportPositionMapping found")
	
	# Test key routes that were problematic
	var test_routes = [
		["dong_dau", "doi_tre", Vector2(-2818, -219)],  # Should spawn near TeleportGate_DoiTre
		["doi_tre", "dong_dau", Vector2(-2500, -435)],  # Should spawn near doi_tre gate in dong_dau
		["lang_van_lang", "dong_dau", Vector2(-2444, -299)],  # Should spawn near lang_van_lang gate in dong_dau
		["dong_dau", "lang_van_lang", Vector2(300, -1900)],  # Should spawn near dong_dau gate in lang_van_lang
	]
	
	for route in test_routes:
		var from_map = route[0]
		var to_map = route[1]
		var expected_pos = route[2]
		
		var actual_pos = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
		
		if actual_pos == expected_pos:
			add_test_result("Route %s->%s" % [from_map, to_map], "PASS", "Position: %s" % actual_pos)
		else:
			add_test_result("Route %s->%s" % [from_map, to_map], "FAIL", "Expected: %s, Got: %s" % [expected_pos, actual_pos])

func test_gate_state_management():
	print("\n--- Test 2: Gate State Management ---")
	
	# Load the teleport gate script
	var teleport_gate_script = load("res://maps/scripts/teleport_gate.gd")
	if not teleport_gate_script:
		add_test_result("TeleportGate Script", "FAIL", "Could not load teleport gate script")
		return
	
	add_test_result("TeleportGate Script", "PASS", "Script loaded successfully")
	
	# Create a mock gate to test state management
	var mock_gate = Area2D.new()
	mock_gate.set_script(teleport_gate_script)
	add_child(mock_gate)
	
	# Test initial state
	if not mock_gate._is_activated:
		add_test_result("Initial State", "PASS", "_is_activated is false")
	else:
		add_test_result("Initial State", "FAIL", "_is_activated should be false initially")
	
	# Test reset functionality
	mock_gate._is_activated = true
	if mock_gate.has_method("_reset_activation_for_success"):
		mock_gate._reset_activation_for_success()
		if not mock_gate._is_activated:
			add_test_result("Reset Functionality", "PASS", "_reset_activation_for_success works")
		else:
			add_test_result("Reset Functionality", "FAIL", "_reset_activation_for_success failed")
	else:
		add_test_result("Reset Functionality", "FAIL", "_reset_activation_for_success method missing")
	
	mock_gate.queue_free()

func test_context_manager_clearing():
	print("\n--- Test 3: Context Manager Clearing ---")
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager:
		add_test_result("TeleportContextManager", "FAIL", "TeleportContextManager not found")
		return
	
	add_test_result("TeleportContextManager", "PASS", "TeleportContextManager found")
	
	# Test context creation and clearing
	context_manager.set_teleport_context(
		"test_gate",
		"dong_dau",
		"res://maps/doi_tre/scenes/doi_tre.tscn"
	)
	
	if context_manager.has_active_teleport_context():
		add_test_result("Context Creation", "PASS", "Context created successfully")
		
		# Test clearing
		context_manager.clear_teleport_context()
		if not context_manager.has_active_teleport_context():
			add_test_result("Context Clearing", "PASS", "Context cleared successfully")
		else:
			add_test_result("Context Clearing", "FAIL", "Context not cleared properly")
	else:
		add_test_result("Context Creation", "FAIL", "Context not created")

func test_specific_route_positioning():
	print("\n--- Test 4: Specific Route Testing (dong_dau -> doi_tre) ---")
	
	# This tests the specific route mentioned in the user's issue
	if TeleportPositionMapping:
		var spawn_pos = TeleportPositionMapping.get_accurate_spawn_position("dong_dau", "doi_tre")
		
		# The expected position should be near the TeleportGate_DoiTre in doi_tre.tscn
		var expected_pos = Vector2(-2818, -219)
		
		if spawn_pos == expected_pos:
			add_test_result("Dong Dau -> Doi Tre", "PASS", "Correct position: %s" % spawn_pos)
		else:
			add_test_result("Dong Dau -> Doi Tre", "FAIL", "Expected: %s, Got: %s" % [expected_pos, spawn_pos])
		
		# Test reverse route
		var reverse_spawn_pos = TeleportPositionMapping.get_accurate_spawn_position("doi_tre", "dong_dau")
		var reverse_expected_pos = Vector2(-2500, -435)
		
		if reverse_spawn_pos == reverse_expected_pos:
			add_test_result("Doi Tre -> Dong Dau", "PASS", "Correct position: %s" % reverse_spawn_pos)
		else:
			add_test_result("Doi Tre -> Dong Dau", "FAIL", "Expected: %s, Got: %s" % [reverse_expected_pos, reverse_spawn_pos])
	else:
		add_test_result("Route Testing", "FAIL", "TeleportPositionMapping not available")

func test_consecutive_teleportation_simulation():
	print("\n--- Test 5: Consecutive Teleportation Simulation ---")
	
	# Simulate multiple teleportations to test the complete flow
	var teleportation_sequence = [
		["lang_van_lang", "dong_dau"],
		["dong_dau", "doi_tre"],
		["doi_tre", "dong_dau"],
		["dong_dau", "lang_van_lang"]
	]
	
	var context_manager = get_node_or_null("/root/TeleportContextManager")
	if not context_manager or not SceneManager or not TeleportPositionMapping:
		add_test_result("Simulation Setup", "FAIL", "Required systems not available")
		return
	
	add_test_result("Simulation Setup", "PASS", "All required systems available")
	
	var successful_teleports = 0
	
	for i in range(teleportation_sequence.size()):
		var route = teleportation_sequence[i]
		var from_map = route[0]
		var to_map = route[1]
		
		# Clear any previous context
		context_manager.clear_teleport_context()
		SceneManager.clear_next_spawn_position()
		
		# Simulate teleportation setup
		var target_scene = "res://maps/%s/scenes/%s.tscn" % [to_map, to_map]
		context_manager.set_teleport_context("test_gate_%d" % i, from_map, target_scene)
		
		# Check if spawn position was set correctly
		if SceneManager.has_next_spawn_position():
			var spawn_pos = SceneManager.get_next_spawn_position()
			var expected_pos = TeleportPositionMapping.get_accurate_spawn_position(from_map, to_map)
			
			if spawn_pos == expected_pos:
				successful_teleports += 1
				print("  ✅ Teleport %d (%s->%s): %s" % [i+1, from_map, to_map, spawn_pos])
			else:
				print("  ❌ Teleport %d (%s->%s): Expected %s, Got %s" % [i+1, from_map, to_map, expected_pos, spawn_pos])
		else:
			print("  ❌ Teleport %d (%s->%s): No spawn position set" % [i+1, from_map, to_map])
	
	if successful_teleports == teleportation_sequence.size():
		add_test_result("Consecutive Teleportations", "PASS", "All %d teleportations successful" % successful_teleports)
	else:
		add_test_result("Consecutive Teleportations", "FAIL", "Only %d/%d teleportations successful" % [successful_teleports, teleportation_sequence.size()])
	
	# Clean up
	context_manager.clear_teleport_context()
	SceneManager.clear_next_spawn_position()

func add_test_result(test_name: String, result: String, details: String):
	test_results.append({
		"name": test_name,
		"result": result,
		"details": details
	})
	
	var status_icon = "✅" if result == "PASS" else "❌"
	print("%s %s: %s" % [status_icon, test_name, details])

func print_test_results():
	print("\n=== Consecutive Teleportation Test Results ===")
	
	var passed = 0
	var failed = 0
	
	for result in test_results:
		if result.result == "PASS":
			passed += 1
		else:
			failed += 1
	
	print("Total Tests: %d" % test_results.size())
	print("Passed: %d" % passed)
	print("Failed: %d" % failed)
	
	if failed == 0:
		print("🎉 All tests passed! Consecutive teleportation should work correctly.")
		print("\n✅ Expected Behavior:")
		print("- Players spawn near the correct destination gate (not default map position)")
		print("- Consecutive teleportations work without gates becoming unresponsive")
		print("- Each teleportation uses accurate gate-specific positioning")
	else:
		print("⚠️ Some tests failed. Issues may persist with consecutive teleportation.")
		print("\n🔧 If tests fail, check:")
		print("- TeleportPositionMapping has correct gate positions")
		print("- TeleportContextManager properly clears context between teleportations")
		print("- SceneManager spawn position management works correctly")
		print("- Teleport gates reset their activation state properly")
	
	print("\n🎮 To test in-game:")
	print("1. Load Lang Van Lang map")
	print("2. Teleport to Dong Dau - should appear near lang_van_lang gate")
	print("3. Teleport to Doi Tre - should appear near TeleportGate_DoiTre")
	print("4. Teleport back to Dong Dau - should appear near doi_tre gate")
	print("5. All gates should remain functional for further teleportations")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Re-running tests...")
		test_results.clear()
		run_consecutive_teleportation_tests()
