# Debug script to check spawn position distances
extends Node

func _ready():
	print("🔍 Debugging spawn position distances...")
	
	# Old default position in dong_dau
	var old_default = Vector2(-1421, -429)
	
	# New spawn position from rung_nuong
	var new_spawn = Vector2(5200, -350)
	
	# Calculate distance
	var distance = old_default.distance_to(new_spawn)
	
	print("📍 Old default position: %s" % old_default)
	print("📍 New spawn position: %s" % new_spawn)
	print("📏 Distance between them: %.1f units" % distance)
	print("🚨 Validation threshold: 5000 units")
	
	if distance > 5000:
		print("❌ PROBLEM: New spawn position is too far from old default!")
		print("   This will trigger the validation reset in dong_dau_map_controller.gd")
		print("   The player will be moved back to default position: %s" % old_default)
	else:
		print("✅ Distance is within acceptable range")
	
	# Check map bounds
	var dong_dau_bounds = {
		"left": -2535,
		"right": 5400,
		"top": -1600,
		"bottom": -125
	}
	
	print("\n🗺️ Map bounds validation:")
	print("   Left: %d, Right: %d" % [dong_dau_bounds.left, dong_dau_bounds.right])
	print("   Top: %d, Bottom: %d" % [dong_dau_bounds.top, dong_dau_bounds.bottom])
	
	var within_bounds = (
		new_spawn.x >= dong_dau_bounds.left and 
		new_spawn.x <= dong_dau_bounds.right and
		new_spawn.y >= dong_dau_bounds.top and 
		new_spawn.y <= dong_dau_bounds.bottom
	)
	
	print("   New spawn within bounds: %s" % within_bounds)
	
	if within_bounds:
		print("✅ Spawn position is within map bounds")
	else:
		print("❌ Spawn position is outside map bounds!")
