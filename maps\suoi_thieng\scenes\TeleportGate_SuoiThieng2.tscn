[gd_scene load_steps=4 format=4 uid="uid://bvn5seficjirh2"]

[ext_resource type="Script" path="res://maps/scripts/teleport_gate.gd" id="1_teleport"]
[ext_resource type="Texture2D" uid="uid://bkn5seficjirh" path="res://assets/images/ui/teleport_gate_icon.png" id="2_icon"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(120, 120)

[node name="TeleportGate_SuoiThieng2_NorthExit" type="Area2D"]
script = ExtResource("1_teleport")
target_scene = "res://maps/lang_van_lang/scenes/lang_van_lang.tscn"
target_position = Vector2(2500, -1900)
gate_id = "suoi_thieng2_north_exit"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="Visual" type="ColorRect" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -60.0
offset_top = -60.0
offset_right = 60.0
offset_bottom = 60.0
color = Color(0.2, 0.8, 1, 0.7)

[node name="Icon" type="TextureRect" parent="Visual"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.0
offset_top = -32.0
offset_right = 32.0
offset_bottom = 32.0
texture = ExtResource("2_icon")
stretch_mode = 4

[node name="InteractionUI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -100.0
offset_right = 150.0
offset_bottom = -20.0

[node name="InteractionPanel" type="Panel" parent="InteractionUI"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
modulate = Color(1, 1, 1, 0.9)

[node name="VBoxContainer" type="VBoxContainer" parent="InteractionUI/InteractionPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="InteractionLabel" type="Label" parent="InteractionUI/InteractionPanel/VBoxContainer"]
layout_mode = 2
text = "Nhấn [ENTER] để đi về phía BẮC ↑ Làng Văn Lang"
horizontal_alignment = 1

[node name="DestinationLabel" type="Label" parent="InteractionUI/InteractionPanel/VBoxContainer"]
layout_mode = 2
text = "🏘️ LÀNG VĂN LANG (BẮC ↑)"
horizontal_alignment = 1
