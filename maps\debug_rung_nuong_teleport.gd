# Debug script cho vấn đề teleport mất player từ rung_nuong
extends Node

func _ready():
	print("🔍 === RUNG NUONG TELEPORT DEBUG ===")
	call_deferred("debug_current_state")

func debug_current_state():
	await get_tree().process_frame
	
	print("\n📋 Current Scene Analysis:")
	print("Scene name: %s" % get_tree().current_scene.name)
	print("Scene file: %s" % get_tree().current_scene.scene_file_path)
	
	print("\n👤 Player Analysis:")
	var player = get_tree().get_first_node_in_group("player")
	if player:
		print("✅ Player found: %s" % player.name)
		print("   Position: %s" % player.global_position)
		print("   Visible: %s" % player.visible)
		print("   Process mode: %s" % player.process_mode)
	else:
		print("❌ Player NOT FOUND in current scene")
		
		# Alternative search methods
		player = get_tree().current_scene.find_child("Player", true, false)
		if player:
			print("🔍 Player found via find_child: %s" % player.name)
		else:
			print("❌ Player not found via find_child either")
	
	print("\n🌀 SceneManager Analysis:")
	if SceneManager:
		print("✅ SceneManager exists")
		print("   Has spawn position: %s" % SceneManager.has_next_spawn_position())
		if SceneManager.has_next_spawn_position():
			# Don't consume it, just check
			print("   Next spawn position available")
	else:
		print("❌ SceneManager not found")
	
	print("\n🎯 Map Controller Analysis:")
	var controllers = get_tree().get_nodes_in_group("map_controllers")
	if controllers.size() > 0:
		print("✅ Map controllers found: %d" % controllers.size())
		for controller in controllers:
			print("   Controller: %s" % controller.name)
	else:
		print("❌ No map controllers found")
		
		# Look for specific controller
		var dong_dau_controller = get_tree().current_scene.find_child("DongDauMapController", true, false)
		if dong_dau_controller:
			print("🔍 DongDauMapController found via find_child")
		else:
			print("❌ DongDauMapController not found")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		debug_teleport_coordinates()
	elif event.is_action_pressed("ui_select"):  # Space key
		test_player_repositioning()

func debug_teleport_coordinates():
	print("\n📍 TELEPORT COORDINATES DEBUG:")
	print("RungNuong Gate target_position: Vector2(-1200, -429)")
	print("DongDau Player default position: Vector2(-1421, -429)")
	print("Distance: %s" % Vector2(-1200, -429).distance_to(Vector2(-1421, -429)))
	
	var player = get_tree().get_first_node_in_group("player")
	if player:
		print("Current player position: %s" % player.global_position)
	
func test_player_repositioning():
	print("\n🧪 TESTING PLAYER REPOSITIONING:")
	var player = get_tree().get_first_node_in_group("player")
	if player:
		var target_pos = Vector2(-1200, -429)
		print("Moving player from %s to %s" % [player.global_position, target_pos])
		player.global_position = target_pos
		print("New position: %s" % player.global_position)
	else:
		print("❌ No player to reposition")
