# Hướng dẫn sử dụng Inventory Button Manager

## <PERSON><PERSON> tả

Inventory Button Manager tạo một nút ở cạnh bên trái màn hình để mở/đóng giao diện hành trang.

## Tính năng

- **Nút vật lý**: Nút ở vị trí cố định bên trái màn hình
- **Phím tắt**: Nhấn phím **I** để mở/đóng hành trang
- **Hiệu ứng**: Mở/đóng với hiệu ứng fade in/out
- **Tự động load**: <PERSON><PERSON><PERSON><PERSON> tích hợp sẵn qua GlobalUIManager

## Cách sử dụng

### 1. Trong Game

- Nhấn nút 📦 ở cạnh trái màn hình
- Hoặc nhấn phím **I**

### 2. Trong Code

```gdscript
# Mở/đóng inventory
GlobalUIManager.toggle_inventory()

# Kiểm tra trạng thái
if GlobalUIManager.is_inventory_open():
    print("Inventory đang mở")
```

## Cấu hình

### Thay đổi vị trí nút

Trong `inventory_button_manager.gd`, sửa dòng:

```gdscript
inventory_button.position = Vector2(20, 200)  # x, y
```

### Thay đổi texture nút

Thêm file ảnh vào các đường dẫn được hỗ trợ:

- `res://assets/images/ui/inventory_button.png`
- `res://assets/images/item/bag.png`

### Thay đổi phím tắt

Trong Project Settings > Input Map, sửa action `open_inventory`

## Autoload được sử dụng

- `GlobalUIManager`: Quản lý UI toàn cục
- `InventoryManager`: Quản lý dữ liệu hành trang (đã có sẵn)

## Files liên quan

- `ui/scripts/inventory_button_manager.gd` - Script chính
- `ui/scenes/inventory_button_manager.tscn` - Scene
- `ui/scripts/global_ui_manager.gd` - Global manager
- `hud_progress/scenes/inventory_tab.tscn` - Giao diện hành trang
