[gd_resource type="Resource" script_class="AnimalDefinition" load_steps=4 format=3 uid="uid://i5v7ybm4fwh6"]

[ext_resource type="SpriteFrames" uid="uid://cssgn2yjxnicb" path="res://animals/sprites/Snail.tres" id="1_4yj3c"]
[ext_resource type="Script" path="res://animals/scripts/animal_definition.gd" id="1_jqr5g"]
[ext_resource type="Shape2D" uid="uid://4ifyps2pyfla" path="res://animals/collisions/snail.tres" id="2_tbhsk"]

[resource]
script = ExtResource("1_jqr5g")
health = 100
damage = 30
attack_cd = 1.5
speed = 100.0
jump_agility = -400.0
animatedSprite = ExtResource("1_4yj3c")
collisionShape = ExtResource("2_tbhsk")
isflying = false
scale = 0.5
isgoing = true
