# Loading Screen Manager - Autoload to handle loading screen issues
extends Node

var monitoring_enabled: bool = true
var check_interval: float = 2.0
var max_loading_time: float = 8.0

func _ready():
	print("🎬 Loading Screen Manager initialized")
	
	# Start monitoring after a delay to let the game initialize
	call_deferred("start_monitoring")

func start_monitoring():
	"""Start monitoring for loading screen issues"""
	if not monitoring_enabled:
		return
	
	print("🔍 Starting loading screen monitoring...")
	
	# Create a timer to check periodically
	var timer = Timer.new()
	timer.wait_time = check_interval
	timer.timeout.connect(_check_loading_screens)
	timer.autostart = true
	add_child(timer)

func _check_loading_screens():
	"""Check for problematic loading screens"""
	var loading_screens = find_all_loading_screens()
	
	for loading_screen in loading_screens:
		if is_loading_screen_problematic(loading_screen):
			print("🚨 Problematic loading screen detected: %s" % loading_screen.name)
			fix_loading_screen(loading_screen)

func find_all_loading_screens() -> Array:
	"""Find all loading screen instances"""
	var loading_screens = []
	var root = get_tree().root
	
	# Search for loading screen nodes
	var all_nodes = get_all_children_recursive(root)
	for node in all_nodes:
		if is_loading_screen_node(node):
			loading_screens.append(node)
	
	return loading_screens

func get_all_children_recursive(node: Node) -> Array:
	"""Get all children recursively"""
	var children = [node]
	for child in node.get_children():
		children.append_array(get_all_children_recursive(child))
	return children

func is_loading_screen_node(node: Node) -> bool:
	"""Check if node is a loading screen"""
	if not node or not is_instance_valid(node):
		return false
	
	# Check by name
	var name = node.name.to_lower()
	if "loading" in name:
		return true
	
	# Check by script
	var script = node.get_script()
	if script and "loading" in script.resource_path.to_lower():
		return true
	
	# Check by group
	if node.is_in_group("loading_screen"):
		return true
	
	return false

func is_loading_screen_problematic(loading_screen: Node) -> bool:
	"""Check if loading screen is problematic"""
	if not loading_screen or not is_instance_valid(loading_screen):
		return false
	
	# Check if progress is at 100% but screen is still visible
	var progress_bar = loading_screen.find_child("ProgressBar", true, false)
	if progress_bar and progress_bar.value >= 100.0:
		print("🔍 Loading screen at 100%: %s" % loading_screen.name)
		return true
	
	# Check if loading screen has been active too long
	if loading_screen.has_method("get_loading_start_time"):
		var start_time = loading_screen.call("get_loading_start_time")
		var current_time = Time.get_ticks_msec() / 1000.0
		var elapsed = current_time - start_time
		
		if elapsed > max_loading_time:
			print("🔍 Loading screen active too long: %.1fs" % elapsed)
			return true
	
	return false

func fix_loading_screen(loading_screen: Node):
	"""Fix a problematic loading screen"""
	print("🔧 Fixing loading screen: %s" % loading_screen.name)
	
	# Method 1: Try enhanced completion methods
	if loading_screen.has_method("force_completion"):
		print("✅ Using force_completion method")
		loading_screen.force_completion()
		return
	
	# Method 2: Try to trigger auto-completion
	if loading_screen.has_method("_handle_completion"):
		print("✅ Using _handle_completion method")
		loading_screen.call_deferred("_handle_completion")
		return
	
	# Method 3: Configure and trigger completion
	if loading_screen.has_method("set_auto_transition") and loading_screen.has_method("update_progress_enhanced"):
		print("✅ Configuring and triggering completion")
		loading_screen.set_auto_transition(true)
		loading_screen.set_target_scene("res://Home/scenes/Startmenu.tscn")
		loading_screen.update_progress_enhanced(100.0, "Hoàn thành!")
		return
	
	# Method 4: Direct scene transition
	print("🚨 Using direct scene transition")
	apply_direct_fix(loading_screen)

func apply_direct_fix(loading_screen: Node):
	"""Apply direct fix as last resort"""
	print("🚨 Applying direct fix...")
	
	# Determine target scene
	var target_scene = "res://Home/scenes/Startmenu.tscn"
	
	if loading_screen.has_method("get_target_scene"):
		var custom_target = loading_screen.call("get_target_scene")
		if custom_target != "" and ResourceLoader.exists(custom_target):
			target_scene = custom_target
	
	# Remove loading screen
	if is_instance_valid(loading_screen):
		loading_screen.queue_free()
	
	# Wait and change scene
	await get_tree().process_frame
	print("🎬 Direct scene change to: %s" % target_scene)
	get_tree().change_scene_to_file(target_scene)

# Public API functions
func force_fix_all_loading_screens():
	"""Manually fix all loading screens"""
	print("🚨 MANUAL: Fixing all loading screens")
	var loading_screens = find_all_loading_screens()
	for loading_screen in loading_screens:
		fix_loading_screen(loading_screen)

func emergency_goto_main_menu():
	"""Emergency transition to main menu"""
	print("🚨 EMERGENCY: Going to main menu")
	
	# Remove all loading screens
	var loading_screens = find_all_loading_screens()
	for loading_screen in loading_screens:
		if is_instance_valid(loading_screen):
			loading_screen.queue_free()
	
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://Home/scenes/Startmenu.tscn")

func debug_all_loading_screens():
	"""Debug all loading screens"""
	print("🔍 DEBUG: All loading screens")
	print("="*40)
	
	var loading_screens = find_all_loading_screens()
	print("Found %d loading screen(s)" % loading_screens.size())
	
	for i in range(loading_screens.size()):
		var loading_screen = loading_screens[i]
		print("\n%d. %s" % [i+1, loading_screen.name])
		print("   Path: %s" % loading_screen.get_path())
		print("   Valid: %s" % is_instance_valid(loading_screen))
		print("   Problematic: %s" % is_loading_screen_problematic(loading_screen))
		
		var progress_bar = loading_screen.find_child("ProgressBar", true, false)
		if progress_bar:
			print("   Progress: %.1f%%" % progress_bar.value)
		
		if loading_screen.has_method("debug_loading_state"):
			loading_screen.debug_loading_state()

# Input handling for emergency fixes
func _input(event):
	if event is InputEventKey and event.pressed:
		# Ctrl+Alt+F = Force fix all loading screens
		if event.keycode == KEY_F and event.ctrl_pressed and event.alt_pressed:
			force_fix_all_loading_screens()
		
		# Ctrl+Alt+M = Emergency main menu
		elif event.keycode == KEY_M and event.ctrl_pressed and event.alt_pressed:
			emergency_goto_main_menu()
		
		# Ctrl+Alt+D = Debug loading screens
		elif event.keycode == KEY_D and event.ctrl_pressed and event.alt_pressed:
			debug_all_loading_screens()

# Configuration functions
func set_monitoring_enabled(enabled: bool):
	"""Enable or disable monitoring"""
	monitoring_enabled = enabled
	print("🔍 Loading screen monitoring: %s" % ("enabled" if enabled else "disabled"))

func set_check_interval(interval: float):
	"""Set check interval in seconds"""
	check_interval = interval
	print("⏱️ Check interval set to: %.1fs" % interval)

func set_max_loading_time(time: float):
	"""Set maximum loading time before intervention"""
	max_loading_time = time
	print("⏰ Max loading time set to: %.1fs" % time)
