# Comprehensive System Test - Test all systems working together
extends Node

# Test results tracking
var test_results: Dictionary = {}
var total_tests: int = 0
var passed_tests: int = 0
var failed_tests: int = 0

# System references
var systems_to_test: Dictionary = {}

func _ready():
	print("🧪 Comprehensive System Test initialized (manual start required)")
	# CRITICAL FIX: Don't auto-run tests during normal game startup

func run_all_system_tests():
	"""Run comprehensive tests for all systems"""
	print("\n" + "="*60)
	print("🧪 COMPREHENSIVE SYSTEM COMPATIBILITY TEST")
	print("="*60)
	
	# Phase 1: System Availability Test
	await test_system_availability()
	
	# Phase 2: UI System Integration Test
	await test_ui_system_integration()
	
	# Phase 3: Teleportation System Test
	await test_teleportation_system()
	
	# Phase 4: Game Systems Integration Test
	await test_game_systems_integration()
	
	# Phase 5: Performance and Memory Test
	await test_performance_and_memory()
	
	# Phase 6: Error Handling Test
	await test_error_handling()
	
	# Print final results
	print_comprehensive_summary()

func test_system_availability() -> void:
	"""Test that all critical systems are available"""
	print("\n🔍 Phase 1: System Availability Test")
	
	var critical_systems = {
		"SceneManager": SceneManager,
		"GlobalMapNameUI": GlobalMapNameUI,
		"XPManager": XPManager,
		"MissionSystem": MissionSystem,
		"QuestSystem": QuestSystem,
		"TeleportPositionMapping": TeleportPositionMapping,
		"AbilityLibrary": AbilityLibrary,
		"RankingSystem": RankingSystem
	}
	
	for system_name in critical_systems.keys():
		var system = critical_systems[system_name]
		var test_name = "System Available: %s" % system_name
		total_tests += 1
		
		if system != null:
			systems_to_test[system_name] = system
			record_test_result(test_name, true, "System is available and accessible")
		else:
			record_test_result(test_name, false, "System is null or not accessible")

func test_ui_system_integration() -> void:
	"""Test UI system integration"""
	print("\n🎨 Phase 2: UI System Integration Test")
	
	# Test 2.1: Map Name UI Integration
	var test_name = "Map Name UI Integration"
	total_tests += 1
	
	if systems_to_test.has("GlobalMapNameUI"):
		var ui_system = systems_to_test["GlobalMapNameUI"]
		
		# Test basic functionality
		ui_system.set_map_name("Integration Test Map")
		await get_tree().create_timer(0.2).timeout
		
		var current_name = ui_system.get_current_map_name()
		if current_name == "Integration Test Map":
			record_test_result(test_name, true, "Map name UI working correctly")
		else:
			record_test_result(test_name, false, "Map name not set correctly: %s" % current_name)
	else:
		record_test_result(test_name, false, "GlobalMapNameUI not available")
	
	# Test 2.2: Scene Manager Integration
	test_name = "Scene Manager Integration"
	total_tests += 1
	
	if systems_to_test.has("SceneManager"):
		var scene_manager = systems_to_test["SceneManager"]
		
		# Test spawn position functionality
		scene_manager.set_next_spawn_position(Vector2(100, 200))
		var has_spawn = scene_manager.has_next_spawn_position()
		var spawn_pos = scene_manager.get_and_clear_spawn_position()
		
		if has_spawn and spawn_pos == Vector2(100, 200):
			record_test_result(test_name, true, "Scene manager spawn system working")
		else:
			record_test_result(test_name, false, "Scene manager spawn system failed")
	else:
		record_test_result(test_name, false, "SceneManager not available")

func test_teleportation_system() -> void:
	"""Test teleportation system integration"""
	print("\n🌀 Phase 3: Teleportation System Test")
	
	# Test 3.1: Teleport Position Mapping
	var test_name = "Teleport Position Mapping"
	total_tests += 1
	
	if systems_to_test.has("TeleportPositionMapping"):
		var teleport_mapping = systems_to_test["TeleportPositionMapping"]
		
		# Test position mapping functionality
		if teleport_mapping.has_method("get_accurate_spawn_position"):
			var spawn_pos = teleport_mapping.get_accurate_spawn_position("lang_van_lang", "dong_dau")
			if spawn_pos != Vector2.ZERO:
				record_test_result(test_name, true, "Teleport mapping working: %s" % spawn_pos)
			else:
				record_test_result(test_name, false, "No spawn position found for test mapping")
		else:
			record_test_result(test_name, false, "Teleport mapping method not available")
	else:
		record_test_result(test_name, false, "TeleportPositionMapping not available")
	
	# Test 3.2: Scene Manager + Teleport Integration
	test_name = "Scene Manager + Teleport Integration"
	total_tests += 1
	
	if systems_to_test.has("SceneManager") and systems_to_test.has("TeleportPositionMapping"):
		var scene_manager = systems_to_test["SceneManager"]
		var teleport_mapping = systems_to_test["TeleportPositionMapping"]
		
		# Simulate teleport flow
		if teleport_mapping.has_method("get_accurate_spawn_position"):
			var test_spawn = teleport_mapping.get_accurate_spawn_position("lang_van_lang", "hang_an")
			if test_spawn != Vector2.ZERO:
				scene_manager.set_next_spawn_position(test_spawn)
				var retrieved_spawn = scene_manager.get_and_clear_spawn_position()
				
				if retrieved_spawn == test_spawn:
					record_test_result(test_name, true, "Teleport + Scene Manager integration working")
				else:
					record_test_result(test_name, false, "Spawn position mismatch")
			else:
				record_test_result(test_name, false, "No test spawn position available")
		else:
			record_test_result(test_name, false, "Teleport mapping method not available")
	else:
		record_test_result(test_name, false, "Required systems not available")

func test_game_systems_integration() -> void:
	"""Test game systems integration"""
	print("\n🎮 Phase 4: Game Systems Integration Test")
	
	# Test 4.1: XP Manager Integration
	var test_name = "XP Manager Integration"
	total_tests += 1
	
	if systems_to_test.has("XPManager"):
		var xp_manager = systems_to_test["XPManager"]
		
		# Test basic XP functionality
		if xp_manager.has_method("add_xp"):
			# This is a read-only test, don't actually modify XP
			record_test_result(test_name, true, "XP Manager accessible and has required methods")
		else:
			record_test_result(test_name, false, "XP Manager missing required methods")
	else:
		record_test_result(test_name, false, "XPManager not available")
	
	# Test 4.2: Mission System Integration
	test_name = "Mission System Integration"
	total_tests += 1
	
	if systems_to_test.has("MissionSystem"):
		var mission_system = systems_to_test["MissionSystem"]
		
		# Test mission system accessibility
		if mission_system.has_method("get_current_missions") or mission_system.has_method("add_mission"):
			record_test_result(test_name, true, "Mission System accessible")
		else:
			record_test_result(test_name, false, "Mission System missing expected methods")
	else:
		record_test_result(test_name, false, "MissionSystem not available")
	
	# Test 4.3: Quest System Integration
	test_name = "Quest System Integration"
	total_tests += 1
	
	if systems_to_test.has("QuestSystem"):
		var quest_system = systems_to_test["QuestSystem"]
		
		# Test quest system accessibility
		record_test_result(test_name, true, "Quest System accessible")
	else:
		record_test_result(test_name, false, "QuestSystem not available")

func test_performance_and_memory() -> void:
	"""Test performance and memory usage"""
	print("\n⚡ Phase 5: Performance and Memory Test")
	
	# Test 5.1: System Initialization Performance
	var test_name = "System Initialization Performance"
	total_tests += 1
	
	var start_time = Time.get_ticks_msec()
	
	# Simulate system operations
	for i in range(100):
		if systems_to_test.has("GlobalMapNameUI"):
			systems_to_test["GlobalMapNameUI"].set_map_name("Performance Test %d" % i)
		await get_tree().process_frame
	
	var end_time = Time.get_ticks_msec()
	var duration = end_time - start_time
	
	if duration < 2000:  # Less than 2 seconds for 100 operations
		record_test_result(test_name, true, "Performance acceptable (%d ms)" % duration)
	else:
		record_test_result(test_name, false, "Performance too slow (%d ms)" % duration)
	
	# Test 5.2: Memory Usage Test
	test_name = "Memory Usage Test"
	total_tests += 1
	
	# Simple memory test - create and destroy objects
	var test_objects = []
	for i in range(50):
		var test_node = Node.new()
		test_node.name = "TestNode_%d" % i
		add_child(test_node)
		test_objects.append(test_node)
	
	await get_tree().process_frame
	
	# Clean up
	for obj in test_objects:
		obj.queue_free()
	
	await get_tree().create_timer(0.1).timeout
	
	record_test_result(test_name, true, "Memory test completed without crashes")

func test_error_handling() -> void:
	"""Test error handling capabilities"""
	print("\n🛡️ Phase 6: Error Handling Test")
	
	# Test 6.1: Null Reference Handling
	var test_name = "Null Reference Handling"
	total_tests += 1
	
	# Test null safety in systems
	var null_test_passed = true
	
	try:
		# Test GlobalMapNameUI with null inputs
		if systems_to_test.has("GlobalMapNameUI"):
			systems_to_test["GlobalMapNameUI"].set_map_name("")  # Empty string test
		
		# Test SceneManager with invalid positions
		if systems_to_test.has("SceneManager"):
			systems_to_test["SceneManager"].set_next_spawn_position(Vector2.ZERO)
		
		record_test_result(test_name, true, "Systems handle null/invalid inputs gracefully")
	except:
		record_test_result(test_name, false, "Systems crashed on null/invalid inputs")

func record_test_result(test_name: String, passed: bool, message: String):
	"""Record a test result"""
	test_results[test_name] = {
		"passed": passed,
		"message": message
	}
	
	if passed:
		passed_tests += 1
		print("  ✅ %s: %s" % [test_name, message])
	else:
		failed_tests += 1
		print("  ❌ %s: %s" % [test_name, message])

func print_comprehensive_summary():
	"""Print comprehensive test summary"""
	var separator = "============================================================"
	print("\n" + separator)
	print("🧪 COMPREHENSIVE SYSTEM TEST SUMMARY")
	print(separator)
	print("Total Tests: %d" % total_tests)
	print("Passed: %d (%.1f%%)" % [passed_tests, (passed_tests * 100.0 / total_tests) if total_tests > 0 else 0.0])
	print("Failed: %d (%.1f%%)" % [failed_tests, (failed_tests * 100.0 / total_tests) if total_tests > 0 else 0.0])
	print(separator)
	
	# Overall system health assessment
	var health_percentage = (passed_tests * 100.0 / total_tests) if total_tests > 0 else 0.0
	var health_status = ""
	
	if health_percentage >= 95:
		health_status = "🟢 EXCELLENT - All systems working perfectly"
	elif health_percentage >= 85:
		health_status = "🟡 GOOD - Minor issues detected"
	elif health_percentage >= 70:
		health_status = "🟠 FAIR - Several issues need attention"
	else:
		health_status = "🔴 POOR - Major issues detected"
	
	print("System Health: %.1f%% - %s" % [health_percentage, health_status])
	print(separator)
	
	if failed_tests > 0:
		print("\n❌ FAILED TESTS:")
		for test_name in test_results.keys():
			var result = test_results[test_name]
			if not result["passed"]:
				print("  - %s: %s" % [test_name, result["message"]])
	
	print("\n🎯 RECOMMENDATIONS:")
	if health_percentage >= 95:
		print("  - System is ready for production")
		print("  - Continue regular monitoring")
	elif health_percentage >= 85:
		print("  - Address minor issues before release")
		print("  - Monitor system performance")
	else:
		print("  - Critical issues must be fixed before release")
		print("  - Consider additional testing and debugging")
	
	print("\n✅ Comprehensive system test completed!")

# Public API for manual testing
func run_quick_test():
	"""Run a quick version of the comprehensive test"""
	print("🚀 Running quick system test...")
	await test_system_availability()
	await test_ui_system_integration()
	print_comprehensive_summary()
