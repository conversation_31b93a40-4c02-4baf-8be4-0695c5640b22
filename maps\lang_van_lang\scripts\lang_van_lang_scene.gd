# lang_van_lang_scene.gd - Main scene script for Lang Van Lang map
extends Node2D

# Node references
@onready var teleport_system: LangVanLangTeleportSystem = null
@onready var player: Player = null

func _ready() -> void:
	print("🏛️ === LANG VAN LANG SCENE KHỞI TẠO ===")
	call_deferred("_initialize_scene")

func _initialize_scene() -> void:
	# Tìm player
	_find_player()
	
	# Thi<PERSON>t lập hệ thống teleport
	_setup_teleport_system()
	
	# Hiển thị thông báo chào mừng
	_show_welcome_message()

func _find_player() -> void:
	player = get_tree().get_first_node_in_group("player")
	if not player:
		player = find_child("Player", true, false)
	
	if player:
		print("👤 Player found: %s at position %s" % [player.name, player.global_position])
	else:
		print("⚠️ WARNING: Player not found!")

func _setup_teleport_system() -> void:
	# <PERSON><PERSON><PERSON> <PERSON>ệ thống teleport
	teleport_system = preload("res://maps/lang_van_lang/scripts/lang_van_lang_teleport_system.gd").new()
	teleport_system.name = "TeleportSystem"
	add_child(teleport_system)
	
	print("🌀 Teleport system initialized")

func _show_welcome_message() -> void:
	if not player:
		return
	
	# Tạo welcome message
	var welcome_label = Label.new()
	welcome_label.text = "🏛️ Chào mừng đến với Làng Văn Lang!\nSử dụng các cổng dịch chuyển để khám phá thế giới."
	welcome_label.add_theme_color_override("font_color", Color.CYAN)
	welcome_label.add_theme_font_size_override("font_size", 20)
	welcome_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	welcome_label.position = Vector2(player.global_position.x - 200, player.global_position.y - 100)
	welcome_label.size = Vector2(400, 60)
	welcome_label.modulate.a = 0.0
	
	add_child(welcome_label)
	
	# Hiệu ứng fade in/out
	var tween = create_tween()
	tween.tween_property(welcome_label, "modulate:a", 1.0, 1.0)
	await tween.finished
	
	# Wait for 3 seconds
	await get_tree().create_timer(3.0).timeout
	
	# Fade out
	var fade_out_tween = create_tween()
	fade_out_tween.tween_property(welcome_label, "modulate:a", 0.0, 1.0)
	await fade_out_tween.finished
	welcome_label.queue_free()

# Debug methods
func _input(event: InputEvent) -> void:
	if not OS.is_debug_build():
		return
	
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F12:
				if teleport_system:
					teleport_system.debug_info()
				else:
					print("No teleport system available")
