# ConsolidatedTeleportFix.gd - Fix all teleport system inconsistencies
extends Node

func _ready():
	print("🔧 === CONSOLIDATED TELEPORT SYSTEM FIX ===")
	call_deferred("apply_all_fixes")

func apply_all_fixes():
	print("\n🔍 Applying comprehensive teleport system fixes...")
	
	# Fix 1: Consolidate position mapping systems
	consolidate_position_systems()
	
	# Fix 2: Ensure all map controllers use SceneManager
	verify_map_controller_integration()
	
	# Fix 3: Fix spatial consistency issues
	fix_spatial_consistency()
	
	# Fix 4: Validate all teleport routes
	validate_all_routes()
	
	print("\n✅ All teleport system fixes applied!")

func consolidate_position_systems():
	print("\n🎯 CONSOLIDATING POSITION MAPPING SYSTEMS...")
	
	# Issue: Multiple conflicting systems
	var systems_found = []
	
	# Check SceneManager (Primary)
	if SceneManager:
		systems_found.append("SceneManager ✅ (Primary)")
	
	# Check PlayerSpawnManager (Should be disabled)
	var player_spawn_manager = get_node_or_null("/root/PlayerSpawnManager")
	if player_spawn_manager:
		systems_found.append("PlayerSpawnManager ⚠️ (Conflicting)")
	
	# Check TeleportPositionMapping (Legacy)
	var teleport_mapping = get_node_or_null("/root/TeleportPositionMapping")
	if teleport_mapping:
		systems_found.append("TeleportPositionMapping ⚠️ (Legacy)")
	
	# Check SpatialTeleportSystem (New)
	var spatial_system = SpatialTeleportSystem.new()
	if spatial_system:
		systems_found.append("SpatialTeleportSystem ✅ (New)")
	
	print("Systems found: " + str(systems_found))
	
	# Recommendation: Use SceneManager + SpatialTeleportSystem only
	print("✅ Recommended: SceneManager (spawn handling) + SpatialTeleportSystem (position mapping)")

func verify_map_controller_integration():
	print("\n🎮 VERIFYING MAP CONTROLLER INTEGRATION...")
	
	var map_controllers = get_tree().get_nodes_in_group("map_controllers")
	print("Found %d map controllers" % map_controllers.size())
	
	for controller in map_controllers:
		var controller_name = controller.name
		var has_spawn_check = controller.has_method("_check_and_setup_teleport_spawn")
		var has_auto_fix = controller.has_method("_auto_fix_teleport_position")
		
		print("🗺️ %s:" % controller_name)
		print("   - Has spawn check: %s" % ("✅" if has_spawn_check else "❌"))
		print("   - Has auto fix: %s" % ("✅" if has_auto_fix else "❌"))
		
		if not has_spawn_check or not has_auto_fix:
			print("   - ⚠️ Needs teleport integration update")

func fix_spatial_consistency():
	print("\n🗺️ FIXING SPATIAL CONSISTENCY...")
	
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	# Test critical routes
	var critical_routes = [
		"lang_van_lang_left_exit",
		"lang_van_lang_right_exit", 
		"dong_dau_left_exit",
		"dong_dau_right_exit",
		"doi_tre_right_exit",
		"suoi_thieng_north_exit"
	]
	
	print("Testing critical teleport routes:")
	for route in critical_routes:
		var spawn_pos = spatial_system.get_spawn_position(route)
		var gate_pos = spatial_system.get_gate_position(route)
		
		var status = "✅" if spawn_pos != Vector2.ZERO else "❌"
		print("   %s %s: spawn=%s, gate=%s" % [status, route, spawn_pos, gate_pos])

func validate_all_routes():
	print("\n🔍 VALIDATING ALL TELEPORT ROUTES...")
	
	# Check reciprocal connections
	var route_pairs = [
		["lang_van_lang_left_exit", "dong_dau_right_exit"],
		["dong_dau_left_exit", "doi_tre_right_exit"],
		["lang_van_lang_right_exit", "hang_an_south_exit"],
		["suoi_thieng_north_exit", "lang_van_lang_south_exit"]
	]
	
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	print("Checking reciprocal connections:")
	for pair in route_pairs:
		var route_a = pair[0]
		var route_b = pair[1]
		
		var spawn_a = spatial_system.get_spawn_position(route_a)
		var spawn_b = spatial_system.get_spawn_position(route_b)
		
		var valid_a = spawn_a != Vector2.ZERO
		var valid_b = spawn_b != Vector2.ZERO
		var reciprocal = valid_a and valid_b
		
		var status = "✅" if reciprocal else "❌"
		print("   %s %s ↔ %s" % [status, route_a, route_b])

func create_teleport_system_health_report():
	print("\n📊 === TELEPORT SYSTEM HEALTH REPORT ===")
	
	var health_score = 0
	var max_score = 0
	
	# Test 1: SceneManager availability (20 points)
	max_score += 20
	if SceneManager and SceneManager.has_method("set_next_spawn_position"):
		health_score += 20
		print("✅ SceneManager integration: 20/20")
	else:
		print("❌ SceneManager integration: 0/20")
	
	# Test 2: Map controller integration (30 points)
	max_score += 30
	var controllers = get_tree().get_nodes_in_group("map_controllers")
	var integrated_controllers = 0
	
	for controller in controllers:
		if controller.has_method("_auto_fix_teleport_position"):
			integrated_controllers += 1
	
	var controller_score = int((float(integrated_controllers) / controllers.size()) * 30)
	health_score += controller_score
	print("✅ Map controller integration: %d/30 (%d/%d controllers)" % [controller_score, integrated_controllers, controllers.size()])
	
	# Test 3: Spatial consistency (25 points)
	max_score += 25
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	var test_routes = ["lang_van_lang_left_exit", "dong_dau_right_exit", "doi_tre_right_exit"]
	var valid_routes = 0
	
	for route in test_routes:
		if spatial_system.get_spawn_position(route) != Vector2.ZERO:
			valid_routes += 1
	
	var spatial_score = int((float(valid_routes) / test_routes.size()) * 25)
	health_score += spatial_score
	print("✅ Spatial consistency: %d/25 (%d/%d routes valid)" % [spatial_score, valid_routes, test_routes.size()])
	
	# Test 4: Gate scene integrity (25 points)
	max_score += 25
	var gate_files = [
		"res://maps/doi_tre/scenes/TeleportGate_DoiTre.tscn",
		"res://maps/dong_dau/scenes/TeleportGate_DongDau_DoiTre.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DongDau.tscn"
	]
	
	var valid_gates = 0
	for gate_file in gate_files:
		if FileAccess.file_exists(gate_file):
			valid_gates += 1
	
	var gate_score = int((float(valid_gates) / gate_files.size()) * 25)
	health_score += gate_score
	print("✅ Gate scene integrity: %d/25 (%d/%d files exist)" % [gate_score, valid_gates, gate_files.size()])
	
	# Final score
	var health_percentage = int((float(health_score) / max_score) * 100)
	print("\n🏆 OVERALL TELEPORT SYSTEM HEALTH: %d/100 (%d%%)" % [health_score, health_percentage])
	
	if health_percentage >= 90:
		print("🟢 EXCELLENT - System is highly stable")
	elif health_percentage >= 70:
		print("🟡 GOOD - Minor issues need attention")
	elif health_percentage >= 50:
		print("🟠 FAIR - Several issues need fixing")
	else:
		print("🔴 POOR - Major fixes required")

func generate_fix_recommendations():
	print("\n💡 === FIX RECOMMENDATIONS ===")
	
	print("1. **Immediate Actions:**")
	print("   - Ensure all teleport gates call SceneManager.set_next_spawn_position()")
	print("   - Verify all map controllers call SceneManager.get_and_clear_spawn_position()")
	print("   - Test critical teleport routes manually")
	
	print("2. **System Cleanup:**")
	print("   - Disable conflicting spawn managers (PlayerSpawnManager)")
	print("   - Consolidate position mappings into SpatialTeleportSystem")
	print("   - Remove legacy TeleportPositionMapping if not needed")
	
	print("3. **Testing Protocol:**")
	print("   - Test each teleport route in both directions")
	print("   - Verify player spawns at correct positions")
	print("   - Check console logs for spawn position messages")
	
	print("4. **Maintenance:**")
	print("   - Run this health check regularly")
	print("   - Update spatial mappings when adding new gates")
	print("   - Document any new teleport routes")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running teleport system fixes...")
		apply_all_fixes()
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n📊 Generating health report...")
		create_teleport_system_health_report()
	elif event.is_action_pressed("ui_select"):  # Space key
		print("\n💡 Generating fix recommendations...")
		generate_fix_recommendations()
