[gd_scene load_steps=3 format=3 uid="uid://vnspo33guhnt"]

[ext_resource type="Texture2D" uid="uid://dpfo7ey164plh" path="res://assets/images/background/screens/loading_screen.png" id="1_yabul"]
[ext_resource type="Script" uid="uid://ch3gt0lhta0dk" path="res://ui/scripts/loading_screen.gd" id="2_0lhwf"]

[node name="LoadingScreen" type="CanvasLayer" groups=["loading_screen"]]
layer = 10
script = ExtResource("2_0lhwf")

[node name="Background" type="TextureRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("1_yabul")
expand_mode = 1
stretch_mode = 6

[node name="LoadingContainer" type="VBoxContainer" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = 150.0
offset_right = 200.0
offset_bottom = 220.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="LoadingText" type="Label" parent="LoadingContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Loading..."
horizontal_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="LoadingContainer"]
custom_minimum_size = Vector2(400, 30)
layout_mode = 2
step = 1.0

[node name="AnimationPlayer" type="AnimationPlayer" parent="." groups=["loading_screen"]]
