extends PlayerState
class_name DeathState

func get_state_name():
	return "death"

func enter(owner: Player) -> void:
	print("Player entered Death State")
	
	# Play death animation if it exists
	if owner.devineMission.sprite_frames.has_animation("die"):
		owner.play_animation("die")
	else:
		# Fallback to a simple visual effect if no death animation
		owner.devineMission.modulate = Color(0.5, 0.5, 0.5)  # Gray out
		
		# Create a tween to fade out
		var tween = owner.create_tween()
		tween.tween_property(owner.devineMission, "modulate:a", 0.5, 1.0)
	
	# Disable player input
	owner.set_physics_process(false)
	owner.set_process_input(false)
	
	# Notify any systems that need to know the player died
	if owner.has_signal("player_defeated"):
		owner.emit_signal("player_defeated")

func exit(owner: Player) -> void:
	print("Player exited Death State")
	
	# Reset visual effects
	owner.devineMission.modulate = Color(1, 1, 1, 1)
	
	# Re-enable player input
	owner.set_physics_process(true)
	owner.set_process_input(true)

func handle_input(owner: Player, event: InputEvent) -> void:
	# No input handling in death state
	pass

func update(owner: Player, delta: float) -> void:
	# No updates in death state
	pass
