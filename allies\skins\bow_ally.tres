[gd_resource type="SpriteFrames" load_steps=35 format=3 uid="uid://cbujhuaiqxofd"]

[ext_resource type="Texture2D" uid="uid://uy2rql0cyv6i" path="res://assets/images/characters/allies/sheets/linh_cung.png" id="1_4jio4"]

[sub_resource type="AtlasTexture" id="AtlasTexture_mlfw3"]
atlas = ExtResource("1_4jio4")
region = Rect2(0, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ccx0"]
atlas = ExtResource("1_4jio4")
region = Rect2(68, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6qk4y"]
atlas = ExtResource("1_4jio4")
region = Rect2(136, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qrkl1"]
atlas = ExtResource("1_4jio4")
region = Rect2(204, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_x51os"]
atlas = ExtResource("1_4jio4")
region = Rect2(272, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_2tcb5"]
atlas = ExtResource("1_4jio4")
region = Rect2(340, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_nrq5h"]
atlas = ExtResource("1_4jio4")
region = Rect2(408, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ircea"]
atlas = ExtResource("1_4jio4")
region = Rect2(0, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_rlh7f"]
atlas = ExtResource("1_4jio4")
region = Rect2(68, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_27efw"]
atlas = ExtResource("1_4jio4")
region = Rect2(136, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_k0ore"]
atlas = ExtResource("1_4jio4")
region = Rect2(204, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_d5x6y"]
atlas = ExtResource("1_4jio4")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_lp0mm"]
atlas = ExtResource("1_4jio4")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ewiuu"]
atlas = ExtResource("1_4jio4")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_lcup5"]
atlas = ExtResource("1_4jio4")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_e55av"]
atlas = ExtResource("1_4jio4")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qgup2"]
atlas = ExtResource("1_4jio4")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_x2p3v"]
atlas = ExtResource("1_4jio4")
region = Rect2(204, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ub5pm"]
atlas = ExtResource("1_4jio4")
region = Rect2(272, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_5dyd2"]
atlas = ExtResource("1_4jio4")
region = Rect2(0, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_vjgk3"]
atlas = ExtResource("1_4jio4")
region = Rect2(68, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_2uyuf"]
atlas = ExtResource("1_4jio4")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_3m28w"]
atlas = ExtResource("1_4jio4")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_dicpx"]
atlas = ExtResource("1_4jio4")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_q5qfl"]
atlas = ExtResource("1_4jio4")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_r0drt"]
atlas = ExtResource("1_4jio4")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_nu4n3"]
atlas = ExtResource("1_4jio4")
region = Rect2(340, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_77fv5"]
atlas = ExtResource("1_4jio4")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xod53"]
atlas = ExtResource("1_4jio4")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_c1r3k"]
atlas = ExtResource("1_4jio4")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_f53a6"]
atlas = ExtResource("1_4jio4")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_i6k0h"]
atlas = ExtResource("1_4jio4")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_gpon2"]
atlas = ExtResource("1_4jio4")
region = Rect2(340, 68, 68, 68)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mlfw3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ccx0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6qk4y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qrkl1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x51os")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2tcb5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nrq5h")
}],
"loop": false,
"name": &"die",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ircea")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rlh7f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_27efw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k0ore")
}],
"loop": false,
"name": &"hurt",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_d5x6y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lp0mm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ewiuu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lcup5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e55av")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qgup2")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_x2p3v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ub5pm")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_5dyd2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vjgk3")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2uyuf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3m28w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dicpx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q5qfl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r0drt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nu4n3")
}],
"loop": true,
"name": &"running",
"speed": 10.0
}, {
"frames": [],
"loop": false,
"name": &"slash_1",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_77fv5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xod53")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c1r3k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f53a6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i6k0h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gpon2")
}],
"loop": true,
"name": &"walking",
"speed": 10.0
}]
