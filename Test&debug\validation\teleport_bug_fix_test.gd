# teleport_bug_fix_test.gd - Test for the teleportation bug fix
extends Node

var test_results = []
var current_test = 0

func _ready():
	print("🧪 Teleport Bug Fix Test - Testing consecutive teleportation functionality")
	await get_tree().process_frame  # Wait for autoloads to load
	run_teleport_bug_tests()

func run_teleport_bug_tests():
	print("\n=== Testing Teleport Bug Fix ===")
	
	# Test 1: Check if TeleportGate class exists and has the fix
	test_teleport_gate_class()
	
	# Test 2: Test activation state management
	test_activation_state_management()
	
	# Test 3: Test multiple consecutive activations
	test_consecutive_activations()
	
	# Test 4: Test state reset mechanisms
	test_state_reset_mechanisms()
	
	# Print final results
	print_test_results()

func test_teleport_gate_class():
	print("\n--- Test 1: TeleportGate Class Validation ---")
	
	# Check if TeleportGate class exists
	var teleport_gate_script = load("res://maps/scripts/teleport_gate.gd")
	if not teleport_gate_script:
		add_test_result("TeleportGate Script", "FAIL", "Script not found")
		return
	
	add_test_result("TeleportGate Script", "PASS", "Script loaded successfully")
	
	# Create a test instance to check methods
	var test_gate = Node2D.new()
	test_gate.set_script(teleport_gate_script)
	
	# Check if the fix methods exist
	if test_gate.has_method("_reset_activation_for_success"):
		add_test_result("Reset Success Method", "PASS", "_reset_activation_for_success method exists")
	else:
		add_test_result("Reset Success Method", "FAIL", "_reset_activation_for_success method missing")
	
	if test_gate.has_method("_ensure_clean_initial_state"):
		add_test_result("Clean State Method", "PASS", "_ensure_clean_initial_state method exists")
	else:
		add_test_result("Clean State Method", "FAIL", "_ensure_clean_initial_state method missing")
	
	test_gate.queue_free()

func test_activation_state_management():
	print("\n--- Test 2: Activation State Management ---")
	
	# Create a mock TeleportGate to test state management
	var mock_gate = create_mock_teleport_gate()
	if not mock_gate:
		add_test_result("Mock Gate Creation", "FAIL", "Could not create mock gate")
		return
	
	add_test_result("Mock Gate Creation", "PASS", "Mock gate created successfully")
	
	# Test initial state
	if not mock_gate._is_activated:
		add_test_result("Initial State", "PASS", "_is_activated starts as false")
	else:
		add_test_result("Initial State", "FAIL", "_is_activated should start as false")
	
	# Test state after simulated activation
	mock_gate._is_activated = true
	if mock_gate._is_activated:
		add_test_result("Activation State", "PASS", "Can set _is_activated to true")
	else:
		add_test_result("Activation State", "FAIL", "Failed to set _is_activated")
	
	# Test reset functionality
	if mock_gate.has_method("_reset_activation_for_success"):
		mock_gate._reset_activation_for_success()
		if not mock_gate._is_activated:
			add_test_result("Reset Functionality", "PASS", "_reset_activation_for_success works")
		else:
			add_test_result("Reset Functionality", "FAIL", "_reset_activation_for_success failed to reset")
	
	mock_gate.queue_free()

func test_consecutive_activations():
	print("\n--- Test 3: Consecutive Activations ---")
	
	var mock_gate = create_mock_teleport_gate()
	if not mock_gate:
		add_test_result("Consecutive Test Setup", "FAIL", "Could not create mock gate")
		return
	
	# Simulate multiple activation cycles
	var success_count = 0
	for i in range(3):
		# Reset to clean state
		mock_gate._is_activated = false
		mock_gate._player_inside = true
		
		# Simulate activation
		if not mock_gate._is_activated:
			mock_gate._is_activated = true
			# Simulate successful teleportation reset
			if mock_gate.has_method("_reset_activation_for_success"):
				mock_gate._reset_activation_for_success()
			
			# Check if gate is ready for next use
			if not mock_gate._is_activated:
				success_count += 1
	
	if success_count == 3:
		add_test_result("Consecutive Activations", "PASS", "All 3 consecutive activations succeeded")
	else:
		add_test_result("Consecutive Activations", "FAIL", "Only %d/3 activations succeeded" % success_count)
	
	mock_gate.queue_free()

func test_state_reset_mechanisms():
	print("\n--- Test 4: State Reset Mechanisms ---")
	
	var mock_gate = create_mock_teleport_gate()
	if not mock_gate:
		add_test_result("Reset Test Setup", "FAIL", "Could not create mock gate")
		return
	
	# Test clean initial state method
	if mock_gate.has_method("_ensure_clean_initial_state"):
		mock_gate._is_activated = true
		mock_gate._player_inside = true
		mock_gate._activation_timer = 5.0
		
		mock_gate._ensure_clean_initial_state()
		
		var all_clean = (not mock_gate._is_activated and 
						not mock_gate._player_inside and 
						mock_gate._activation_timer == 0.0)
		
		if all_clean:
			add_test_result("Clean Initial State", "PASS", "All state variables properly reset")
		else:
			add_test_result("Clean Initial State", "FAIL", "Some state variables not reset")
	else:
		add_test_result("Clean Initial State", "FAIL", "Method not found")
	
	mock_gate.queue_free()

func create_mock_teleport_gate():
	"""Create a mock TeleportGate for testing"""
	var teleport_gate_script = load("res://maps/scripts/teleport_gate.gd")
	if not teleport_gate_script:
		return null
	
	var mock_gate = Area2D.new()
	mock_gate.set_script(teleport_gate_script)
	
	# Set required properties
	mock_gate.target_scene = "res://test_scene.tscn"
	mock_gate.gate_id = "test_gate"
	
	add_child(mock_gate)
	return mock_gate

func add_test_result(test_name: String, result: String, details: String):
	test_results.append({
		"name": test_name,
		"result": result,
		"details": details
	})
	
	var status_icon = "✅" if result == "PASS" else "❌"
	print("%s %s: %s" % [status_icon, test_name, details])

func print_test_results():
	print("\n=== Test Results Summary ===")
	
	var passed = 0
	var failed = 0
	
	for result in test_results:
		if result.result == "PASS":
			passed += 1
		else:
			failed += 1
	
	print("Total Tests: %d" % test_results.size())
	print("Passed: %d" % passed)
	print("Failed: %d" % failed)
	
	if failed == 0:
		print("🎉 All tests passed! Teleport bug fix is working correctly.")
	else:
		print("⚠️ Some tests failed. Please review the implementation.")
	
	print("\n🔍 To test in-game:")
	print("1. Load a map with teleport gates")
	print("2. Use a teleport gate to travel to another map")
	print("3. Return to the original map")
	print("4. Try using the same teleport gate again")
	print("5. The gate should work without issues")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n🔄 Re-running tests...")
		test_results.clear()
		run_teleport_bug_tests()
