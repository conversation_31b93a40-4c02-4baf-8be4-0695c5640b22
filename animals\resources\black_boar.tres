[gd_resource type="Resource" script_class="AnimalDefinition" load_steps=4 format=3 uid="uid://d16rfom0jlina"]

[ext_resource type="Script" path="res://animals/scripts/animal_definition.gd" id="1_4i0gn"]
[ext_resource type="SpriteFrames" uid="uid://bu01oodc00m4g" path="res://animals/sprites/Black_Boar.tres" id="1_07grj"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_hc21f"]

[resource]
script = ExtResource("1_4i0gn")
health = 100
damage = 30
attack_cd = 1.5
speed = 100.0
jump_agility = -400.0
animatedSprite = ExtResource("1_07grj")
collisionShape = SubResource("CapsuleShape2D_hc21f")
isflying = false
scale = 1.0
isgoing = false
