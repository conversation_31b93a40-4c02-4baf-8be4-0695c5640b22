extends CharacterBody2D
class_name JumpingAlly

# — State & stats —
var duration: float                           = 0.0
var facing_right: bool                        = true
var is_hurt: bool                             = false
var is_dead: bool                             = false
var is_jumping: bool                          = false
var jump_cooldown: float                      = 0.0

@export var health: float                     = 100.0
@export var peak_duration: float              = 1.0
@export var damage: float                     = 10.0

@export var speed: float                      = 450.0
@export var acceleration: float               = 15.0
@export var follow_distance: float            = 150.0    # how close to the player
@export var attack_range: float               = 35.0     # how close to the enemy
@export var attack_cooldown: float            = 0.7      # seconds between swings
@export var sprites: SpriteFrames             = preload("res://allies/skins/spear_ally.tres")
@export var jump_force: float                 = -800.0   # Lực nh<PERSON>y
@export var jump_detection_height: float      = 250.0    # Phát hiện độ cao cần nhảy
@export var jump_cooldown_time: float         = 0.5      # Thời gian chờ giữa các lần nh<PERSON>

# — Nodes & Areas —
@onready var animated_sprite: AnimatedSprite2D   = $AnimatedSprite2D
@onready var tracking_zone: Area2D               = $TrackingZone
@onready var detect_zone: Area2D                 = $DetectZone
@onready var hitbox_area: Area2D                 = $Hitbox
@onready var hitbox_collision: CollisionShape2D   = $Hitbox/CollisionShape2D

# Who we're chasing/fighting
@export var player_target: CharacterBody2D    = null
var attacking_target: CharacterBody2D = null

# Internal timer
var _attack_timer: float = 0.0

func _ready() -> void:
	# assign sprites + disable looping on hurt/die
	animated_sprite.sprite_frames = sprites
	var sf = animated_sprite.sprite_frames
	sf.set_animation_loop("hurt", false)
	sf.set_animation_loop("die",  false)

	duration = peak_duration
	animated_sprite.play("idle")

	# connect zones
	tracking_zone.body_entered.connect(_on_tracking_zone_enter)
	tracking_zone.body_exited.connect(_on_tracking_zone_exit)
	detect_zone.body_entered.connect(_on_detect_zone_enter)
	detect_zone.body_exited.connect(_on_detect_zone_exit)
	hitbox_area.body_entered.connect(_on_body_hit)
	animated_sprite.animation_finished.connect(_on_animation_finished)

	_attack_timer = 0.0

	# Đảm bảo hitbox bị vô hiệu hóa khi khởi tạo
	hitbox_collision.disabled = true

	# Đảm bảo đồng minh thuộc nhóm ally_group
	if not is_in_group("ally_group"):
		add_to_group("ally_group")

func get_gravity_value() -> Vector2:
	return Vector2(0, 980.0)

func _physics_process(delta: float) -> void:
	if is_hurt or is_dead:
		return

	_attack_timer = max(_attack_timer - delta, 0.0)
	jump_cooldown = max(jump_cooldown - delta, 0.0)

	# Kiểm tra vị trí để tránh di chuyển ra khỏi map
	_check_map_boundaries()

	# Tìm mục tiêu trong tầm để tấn công trước
	var target_in_range = _find_target_in_range()
	if target_in_range:
		attacking_target = target_in_range
		print("Jumping ally found target in range: ", target_in_range.name)
	# Chủ động tìm kiếm kẻ địch nếu không có mục tiêu tấn công hoặc mục tiêu hiện tại không hợp lệ
	elif !attacking_target or !is_instance_valid(attacking_target):
		_search_for_enemies()

	# Kiểm tra lại mục tiêu tấn công
	if attacking_target and is_instance_valid(attacking_target):
		# Kiểm tra xem kẻ địch có còn sống không
		var is_enemy_dead = false
		if attacking_target.has_method("is_dead") or attacking_target.get("is_dead") != null:
			is_enemy_dead = attacking_target.is_dead

		if is_enemy_dead:
			attacking_target = null
			_search_for_enemies()  # Tìm mục tiêu mới ngay lập tức
		else:
			_chase_and_attack(attacking_target, delta)
	else:
		# Không có mục tiêu, tiếp tục tìm kiếm
		_search_for_enemies()

		# Nếu vẫn không tìm thấy mục tiêu, đứng yên
		if !attacking_target:
			_enter_idle()

	if not is_on_floor():
		velocity.y += get_gravity_value().y * delta
		if velocity.y > 0 and animated_sprite.animation != "jump_end" and animated_sprite.animation != "jump_down" and !is_hurt:
			animated_sprite.play("jump_end")
	elif is_jumping and is_on_floor():
		is_jumping = false
		animated_sprite.play("idle")

	move_and_slide()

func _chase_and_attack(target_node: CharacterBody2D, delta: float) -> void:
	# Kiểm tra xem mục tiêu có còn tồn tại không
	if not is_instance_valid(target_node):
		attacking_target = null
		_search_for_enemies()  # Tìm mục tiêu mới ngay lập tức
		return

	# Kiểm tra xem mục tiêu có phải là đồng minh không, nếu là đồng minh thì bỏ qua
	if target_node.is_in_group("ally_group") or target_node.is_in_group("player"):
		attacking_target = null
		_search_for_enemies()  # Tìm mục tiêu mới ngay lập tức
		return

	# Kiểm tra xem kẻ địch có còn sống không
	var is_enemy_dead = false
	if target_node.has_method("is_dead"):
		is_enemy_dead = target_node.is_dead()
	elif target_node.get("is_dead") != null:
		is_enemy_dead = target_node.is_dead

	if is_enemy_dead:
		attacking_target = null
		_search_for_enemies()  # Tìm mục tiêu mới ngay lập tức
		return

	var to_enemy = target_node.global_position - global_position
	var dist = to_enemy.length()
	var height_diff = abs(global_position.y - target_node.global_position.y)

	# Tấn công khi đủ gần hoặc khi có vũ khí tầm xa (cung)
	var can_attack = false

	# Kiểm tra xem có phải là lính cung không (dựa vào sprite)
	var is_bow_ally = false
	if animated_sprite.sprite_frames:
		var sprite_name = animated_sprite.sprite_frames.resource_path
		is_bow_ally = "bow" in sprite_name or "archer" in sprite_name or "range" in sprite_name

	# Tăng phạm vi tấn công để đảm bảo đồng minh có thể tấn công kẻ địch
	var effective_attack_range = attack_range * 2.0
	if is_bow_ally:
		effective_attack_range = attack_range * 5.0

	print("Jumping ally distance to enemy: ", dist, " - Attack range: ", effective_attack_range)

	# Nếu là lính cung hoặc đủ gần để tấn công
	if (is_bow_ally and dist <= effective_attack_range) or dist <= effective_attack_range:
		can_attack = true
		print("Jumping ally can attack enemy: ", can_attack)

	if can_attack:
		# attack
		velocity.x = lerp(velocity.x, 0.0, acceleration * delta)
		if _attack_timer == 0.0 and is_on_floor():
			print("Jumping ally starting attack animation")
			_attack_timer = attack_cooldown * 0.7  # Giảm thời gian hồi chiêu
			hitbox_collision.disabled = false

			# Chọn animation tấn công phù hợp
			if is_bow_ally and animated_sprite.sprite_frames.has_animation("shoot"):
				animated_sprite.play("shoot")
			else:
				animated_sprite.play("slash_1")

			# Đảm bảo quay mặt về phía kẻ địch khi tấn công
			facing_right = to_enemy.x > 0
			hitbox_area.scale.x = 1 if facing_right else -1
			animated_sprite.flip_h = not facing_right

			# Nếu là lính cung, tạo mũi tên
			if is_bow_ally:
				_shoot_arrow(target_node.global_position)
			else:
				# Đối với lính cận chiến, di chuyển hitbox về phía kẻ địch
				var hitbox_offset = 15 if facing_right else -15
				hitbox_collision.position.x = abs(hitbox_collision.position.x) * sign(hitbox_offset)

				# Tăng kích thước hitbox để dễ dàng tiếp xúc với kẻ địch
				if hitbox_collision.shape:
					var current_shape = hitbox_collision.shape
					if current_shape is CircleShape2D:
						current_shape.radius = 15.0  # Tăng kích thước hitbox cho jumping ally
	else:
		# Nếu không đủ gần để tấn công, di chuyển đến mục tiêu
		var dir = to_enemy.normalized()
		var chase_speed = speed * 1.8  # Tăng tốc độ đuổi theo rất nhiều cho jumping ally
		velocity.x = lerp(velocity.x, dir.x * chase_speed, acceleration * delta * 2.0)  # Tăng tốc độ phản ứng
		facing_right = dir.x > 0
		hitbox_area.scale.x = 1 if facing_right else -1
		animated_sprite.flip_h = not facing_right

		# Kiểm tra xem có cần nhảy không
		_check_and_jump(target_node.global_position)

		if is_on_floor() and animated_sprite.animation != "running" and !is_jumping:
			animated_sprite.play("running")

# Chủ động tìm kiếm kẻ địch trên toàn bản đồ
func _search_for_enemies() -> void:
	var enemies = get_tree().get_nodes_in_group("enemy_group")
	var closest_enemy: CharacterBody2D = null
	var closest_distance = 999999.0  # Tìm trên toàn bản đồ

	# Biến để theo dõi kẻ địch ở vị trí thấp hơn
	var lowest_enemy: CharacterBody2D = null
	var lowest_enemy_height_diff = 0
	var lowest_enemy_distance = 999999.0

	print("Jumping ally searching for enemies. Found ", enemies.size(), " enemies in group.")

	for enemy in enemies:
		if enemy is CharacterBody2D and is_instance_valid(enemy):
			# Kiểm tra an toàn thuộc tính is_dead
			var is_enemy_dead = false
			if enemy.has_method("is_dead") or enemy.get("is_dead") != null:
				is_enemy_dead = enemy.is_dead

			if not is_enemy_dead:
				var distance = global_position.distance_to(enemy.global_position)
				var height_diff = global_position.y - enemy.global_position.y

				print("Checking enemy at distance: ", distance, ", height diff: ", height_diff)

				# Tìm kẻ địch gần nhất
				if distance < closest_distance:
					closest_distance = distance
					closest_enemy = enemy as CharacterBody2D
					print("Found closer enemy at distance: ", distance)

				# Tìm kẻ địch ở vị trí thấp hơn và trong phạm vi hợp lý
				# Đồng minh nhảy có thể phát hiện kẻ địch ở xa hơn và thấp hơn
				if height_diff < 0 and abs(height_diff) > jump_detection_height * 0.5 and distance < 700:
					# Nếu kẻ địch này ở thấp hơn kẻ địch thấp nhất đã tìm thấy hoặc gần hơn
					if abs(height_diff) > abs(lowest_enemy_height_diff) or (abs(height_diff) > jump_detection_height * 0.8 and distance < lowest_enemy_distance):
						lowest_enemy_height_diff = height_diff
						lowest_enemy = enemy as CharacterBody2D
						lowest_enemy_distance = distance
						print("Found enemy at lower position, height diff: ", height_diff)

	# Ưu tiên tấn công kẻ địch ở vị trí thấp hơn nếu có
	if lowest_enemy:
		attacking_target = lowest_enemy
		print("Prioritizing enemy at lower position")

		# Nếu kẻ địch ở thấp hơn, chuẩn bị nhảy xuống nhưng chỉ khi thực sự cần thiết
		if is_on_floor() and jump_cooldown <= 0:
			var height_diff = global_position.y - lowest_enemy.global_position.y
			var horizontal_diff = abs(global_position.x - lowest_enemy.global_position.x)

			# Chỉ nhảy xuống khi kẻ địch ở xa và thấp hơn đáng kể
			if abs(height_diff) > jump_detection_height * 0.8 and horizontal_diff < 300:
				print("Preparing to jump down to enemy")
				# Kiểm tra xem có đường đi xuống không
				var ray_cast = RayCast2D.new()
				add_child(ray_cast)
				ray_cast.enabled = true
				ray_cast.target_position = Vector2(sign(lowest_enemy.global_position.x - global_position.x) * horizontal_diff, height_diff)
				ray_cast.force_raycast_update()

				# Chỉ nhảy xuống nếu có mặt đất bên dưới
				if ray_cast.is_colliding():
					print("Path clear, jumping down to enemy")
					# Nhảy xuống với lực nhảy thấp hơn (lực dương để đẩy xuống)
					velocity.y = abs(jump_force) * 0.4  # Lực nhảy xuống nhẹ, nhưng mạnh hơn đồng minh thường
					is_jumping = true
					jump_cooldown = jump_cooldown_time * 2.0  # Tăng thời gian chờ

					# Phát animation nhảy xuống nếu có
					if animated_sprite.sprite_frames.has_animation("jump_down"):
						animated_sprite.play("jump_down")
					elif animated_sprite.sprite_frames.has_animation("jump"):
						animated_sprite.play("jump")
					else:
						animated_sprite.play("running")

					# Thêm một chút vận tốc ngang để tiếp cận mục tiêu
					velocity.x = sign(lowest_enemy.global_position.x - global_position.x) * speed * 1.5

				ray_cast.queue_free()

	# Nếu không có kẻ địch ở vị trí thấp hơn, tấn công kẻ địch gần nhất
	elif closest_enemy:
		attacking_target = closest_enemy
		print("Targeting closest enemy at distance: ", closest_distance)

func _check_and_jump(target_position: Vector2) -> void:
	# Chỉ nhảy khi đang ở trên mặt đất và hết thời gian chờ nhảy
	if is_on_floor() and jump_cooldown <= 0:
		var height_diff = global_position.y - target_position.y
		var horizontal_diff = abs(global_position.x - target_position.x)

		# Thêm kiểm tra khoảng cách ngang để tránh nhảy liên tục khi không thể tiếp cận
		var should_jump = false
		var max_horizontal_distance = 300.0  # Khoảng cách ngang tối đa để nhảy

		# Chỉ nhảy khi mục tiêu ở trong phạm vi hợp lý theo chiều ngang
		if horizontal_diff < max_horizontal_distance:
			# Nếu mục tiêu ở cao hơn một khoảng nhất định, nhảy lên
			if height_diff < -jump_detection_height:
				# Thêm kiểm tra để tránh nhảy liên tục khi không thể tiếp cận
				var ray_cast = RayCast2D.new()
				add_child(ray_cast)
				ray_cast.enabled = true
				ray_cast.target_position = Vector2(sign(target_position.x - global_position.x) * horizontal_diff, height_diff)
				ray_cast.force_raycast_update()

				# Chỉ nhảy nếu không có vật cản hoặc có đường đi hợp lý
				if !ray_cast.is_colliding() or abs(height_diff) < 100:
					# Kiểm tra thêm xem có thể đi bộ đến mục tiêu không
					var can_walk_to_target = false
					var walk_ray = RayCast2D.new()
					add_child(walk_ray)
					walk_ray.enabled = true
					walk_ray.target_position = Vector2(sign(target_position.x - global_position.x) * horizontal_diff, 0)
					walk_ray.force_raycast_update()

					# Nếu có vật cản theo chiều ngang, cần nhảy
					if walk_ray.is_colliding():
						should_jump = true

					walk_ray.queue_free()

				ray_cast.queue_free()

				if should_jump:
					velocity.y = jump_force
					is_jumping = true
					jump_cooldown = jump_cooldown_time * 2.5  # Tăng thời gian chờ để tránh nhảy liên tục
					animated_sprite.play("jump")

			# Nếu mục tiêu ở thấp hơn một khoảng nhất định, nhảy xuống
			elif height_diff > jump_detection_height * 0.5:
				# Thêm kiểm tra để tránh nhảy xuống vực
				var ray_cast = RayCast2D.new()
				add_child(ray_cast)
				ray_cast.enabled = true
				ray_cast.target_position = Vector2(sign(target_position.x - global_position.x) * horizontal_diff, height_diff)
				ray_cast.force_raycast_update()

				# Chỉ nhảy xuống nếu có mặt đất bên dưới
				if ray_cast.is_colliding():
					should_jump = true

				ray_cast.queue_free()

				if should_jump:
					# Nhảy xuống với lực nhảy thấp hơn (lực dương để đẩy xuống)
					velocity.y = abs(jump_force) * 0.4  # Lực nhảy xuống nhẹ, nhưng mạnh hơn đồng minh thường
					is_jumping = true
					jump_cooldown = jump_cooldown_time * 2.0  # Tăng thời gian chờ để tránh nhảy liên tục

					# Phát animation nhảy xuống nếu có, nếu không thì dùng animation nhảy thông thường
					if animated_sprite.sprite_frames.has_animation("jump_down"):
						animated_sprite.play("jump_down")
					else:
						animated_sprite.play("jump")

					# Thêm một chút vận tốc ngang để tiếp cận mục tiêu
					velocity.x = sign(target_position.x - global_position.x) * speed * 1.5  # Nhanh hơn đồng minh thường

			# Loại bỏ nhảy ngẫu nhiên để tránh nhảy không cần thiết
			# Chỉ nhảy khi thực sự cần thiết để vượt qua chướng ngại vật

func _enter_idle() -> void:
	velocity.x = lerp(velocity.x, 0.0, acceleration * get_physics_process_delta_time())
	if is_on_floor() and animated_sprite.animation != "idle":
		animated_sprite.play("idle")

func take_damage(amount: float) -> void:
	if is_hurt or is_dead or health <= 0:
		return

	# Giảm máu
	health -= amount
	is_hurt = true
	animated_sprite.play("hurt")

	# Hiển thị số sát thương
	var damage_manager = get_node_or_null("/root/DamageEffects")
	if damage_manager:
		# Tạo số sát thương với màu xanh cho đồng minh
		damage_manager.create_damage_number(global_position + Vector2(0, -20), amount, false, Color(0.3, 0.7, 1.0))

		# Tạo hiệu ứng hit
		damage_manager.create_hit_effect(global_position)

		# Rung màn hình nếu sát thương lớn
		if amount > 20:
			damage_manager.apply_screen_shake(2.0, 0.15)

	# Hiệu ứng flash khi bị đánh
	if animated_sprite:
		animated_sprite.modulate = Color(1.5, 1.5, 1.5)  # Trắng sáng
		var tween = create_tween()
		tween.tween_property(animated_sprite, "modulate", Color(1, 1, 1), 0.2)

	if health <= 0:
		is_dead = true

# — Zone signals —

func _on_tracking_zone_enter(body: Node) -> void:
	# Chỉ theo dõi kẻ địch
	if body is CharacterBody2D and body != self and body.is_in_group("enemy_group"):
		# Kiểm tra xem kẻ địch có còn sống không
		var is_enemy_dead = false
		if body.has_method("is_dead"):
			is_enemy_dead = body.is_dead()
		elif body.get("is_dead") != null:
			is_enemy_dead = body.is_dead

		if not is_enemy_dead:
			player_target = body
			print("Jumping ally tracking enemy: ", body.name)

func _on_tracking_zone_exit(body: Node) -> void:
	if body == player_target:
		player_target = null

func _on_detect_zone_enter(body: Node) -> void:
	# Chỉ tấn công kẻ địch, không tấn công đồng minh hoặc người chơi
	if body.is_in_group("enemy_group") and !attacking_target:
		# Kiểm tra xem kẻ địch có còn sống không
		var is_enemy_dead = false
		if body.has_method("is_dead"):
			is_enemy_dead = body.is_dead()
		elif body.get("is_dead") != null:
			is_enemy_dead = body.is_dead

		if not is_enemy_dead:
			attacking_target = body

func _on_detect_zone_exit(body: Node) -> void:
	if body == attacking_target:
		attacking_target = null

func _on_body_hit(body: Node) -> void:
	# Chỉ gây sát thương cho kẻ địch, không gây sát thương cho đồng minh hoặc người chơi
	if body.has_method("take_damage") and body.is_in_group("enemy_group"):
		# Kiểm tra xem kẻ địch có còn sống không
		var is_enemy_dead = false
		if body.has_method("is_dead"):
			is_enemy_dead = body.is_dead()
		elif body.get("is_dead") != null:
			is_enemy_dead = body.is_dead

		if is_enemy_dead:
			return

		# Đảm bảo hitbox đã được kích hoạt và đang trong animation tấn công
		var is_attacking = animated_sprite.animation == "slash_1" or animated_sprite.animation.begins_with("attack") or animated_sprite.animation.begins_with("slash") or animated_sprite.animation == "shoot"

		# Kiểm tra xem có phải là lính cung không
		var is_bow_ally = false
		if animated_sprite.sprite_frames:
			var sprite_name = animated_sprite.sprite_frames.resource_path
			is_bow_ally = "bow" in sprite_name or "archer" in sprite_name or "range" in sprite_name

		# Luôn gây sát thương khi va chạm với kẻ địch trong hitbox
		print("Jumping ally hit enemy: ", body.name, " - Is attacking: ", is_attacking)

		# Tính toán sát thương dựa vào loại vũ khí
		var damage_multiplier = 2.0  # Jumping ally gây sát thương cao hơn ally thường
		if is_bow_ally:
			damage_multiplier = 2.5  # Lính cung nhảy gây sát thương cao nhất

		body.take_damage(damage * damage_multiplier)

		# Đặt kẻ địch này làm mục tiêu tấn công
		attacking_target = body

		# Hiển thị hiệu ứng hit
		var damage_manager = get_node_or_null("/root/DamageEffects")
		if damage_manager:
			damage_manager.create_hit_effect(body.global_position)

			# Tạo số sát thương
			damage_manager.create_damage_number(body.global_position + Vector2(0, -20), damage * damage_multiplier, false, Color(0.3, 0.7, 1.0))

			# Rung màn hình nhẹ khi tấn công
			damage_manager.apply_screen_shake(1.5, 0.15)

		# Đặt lại thời gian hồi chiêu để có thể tấn công tiếp
		_attack_timer = attack_cooldown * 0.5  # Giảm thời gian hồi chiêu nhiều hơn khi đã gây sát thương

func _on_animation_finished() -> void:
	var anim = animated_sprite.animation
	print("Jumping ally animation finished: ", anim)

	match anim:
		"slash_1", "attack", "attack_1", "attack_2", "shoot":
			print("Jumping ally attack animation finished")
			# Không vô hiệu hóa hitbox ngay lập tức để tăng cơ hội gây sát thương
			# Thay vào đó, để nó hoạt động trong một khoảng thời gian ngắn
			var timer = get_tree().create_timer(0.1)
			timer.timeout.connect(func(): hitbox_collision.disabled = true)

			# Đặt lại thời gian hồi chiêu để có thể tấn công tiếp
			_attack_timer = attack_cooldown * 0.7  # Giảm thời gian hồi chiêu để tấn công nhanh hơn

			# Nếu là animation bắn cung, quay lại idle
			if anim == "shoot":
				animated_sprite.play("idle")
		"hurt":
			is_hurt = false
			if is_dead:
				animated_sprite.play("die")
			else:
				animated_sprite.play("idle")
		"die":
			queue_free()
		"jump":
			if !is_on_floor():
				animated_sprite.play("jump_end")
		"jump_down":
			if !is_on_floor():
				# Sử dụng animation jump_end thay cho falling
				if animated_sprite.sprite_frames.has_animation("jump_end"):
					animated_sprite.play("jump_end")
				else:
					# Nếu không có animation rơi, tiếp tục animation nhảy xuống
					animated_sprite.play("jump_down")
		_:
			if is_on_floor() and anim not in ["idle", "running", "slash_1", "shoot"]:
				animated_sprite.play("idle")

	# Nếu có mục tiêu tấn công, tiếp tục tấn công
	if attacking_target and is_instance_valid(attacking_target):
		var distance = global_position.distance_to(attacking_target.global_position)
		if distance <= attack_range * 2.0:
			# Nếu đủ gần, bắt đầu tấn công lại ngay lập tức
			if _attack_timer <= 0.1 and is_on_floor():
				print("Jumping ally continuing attack on target")
				_attack_timer = attack_cooldown * 0.7
				hitbox_collision.disabled = false

				# Chọn animation tấn công phù hợp
				var is_bow_ally = false
				if animated_sprite.sprite_frames:
					var sprite_name = animated_sprite.sprite_frames.resource_path
					is_bow_ally = "bow" in sprite_name or "archer" in sprite_name or "range" in sprite_name

				if is_bow_ally and animated_sprite.sprite_frames.has_animation("shoot"):
					animated_sprite.play("shoot")
				else:
					animated_sprite.play("slash_1")

# Kiểm tra vị trí để tránh di chuyển ra khỏi map
func _check_map_boundaries() -> void:
	var viewport_size = get_viewport_rect().size
	var screen_position = global_position

	# Nếu đang ở gần rìa trái của map và đang di chuyển sang trái, đổi hướng
	if screen_position.x < viewport_size.x * 0.1 and velocity.x < 0:
		velocity.x = abs(velocity.x)  # Đổi hướng sang phải
		facing_right = true
		hitbox_area.scale.x = 1
		animated_sprite.flip_h = false

	# Nếu đang ở gần rìa phải của map và đang di chuyển sang phải, đổi hướng
	elif screen_position.x > viewport_size.x * 0.9 and velocity.x > 0:
		velocity.x = -abs(velocity.x)  # Đổi hướng sang trái
		facing_right = false
		hitbox_area.scale.x = -1
		animated_sprite.flip_h = true

# Tìm mục tiêu trong tầm tấn công
func _find_target_in_range() -> CharacterBody2D:
	var enemies = get_tree().get_nodes_in_group("enemy_group")
	var targets = []

	print("Jumping ally searching for targets. Found ", enemies.size(), " enemies in group.")

	# Thêm tất cả kẻ địch vào danh sách mục tiêu
	for enemy in enemies:
		if is_instance_valid(enemy) and enemy is CharacterBody2D:
			var is_enemy_dead = false
			# Kiểm tra an toàn thuộc tính is_dead
			if enemy.has_method("is_dead"):
				is_enemy_dead = enemy.is_dead()
			elif enemy.get("is_dead") != null:
				is_enemy_dead = enemy.is_dead

			if not is_enemy_dead:
				targets.append(enemy)
				print("Jumping ally added valid enemy to targets: ", enemy.name)

	# Tìm mục tiêu gần nhất trong tầm tấn công
	var closest_target: CharacterBody2D = null
	var closest_distance = attack_range * 15.0  # Mở rộng phạm vi rất nhiều để phát hiện kẻ địch từ xa

	for target in targets:
		if target is CharacterBody2D:
			var distance = global_position.distance_to(target.global_position)
			print("Jumping ally checking enemy at distance: ", distance)

			# Bỏ qua giới hạn về độ cao - có thể tấn công kẻ địch ở bất kỳ vị trí nào
			if distance < closest_distance:
				closest_distance = distance
				closest_target = target as CharacterBody2D
				print("Jumping ally found closer target at distance: ", distance)

				# Nếu kẻ địch rất gần, ưu tiên tấn công ngay
				if distance < attack_range * 3:
					print("Target is very close, prioritizing it")
					break

	if closest_target:
		print("Jumping ally found target in range: ", closest_target.name, " at distance: ", closest_distance)
	else:
		print("Jumping ally found no targets in range")

	return closest_target

# Hàm trả về mục tiêu tấn công hiện tại
func get_attacking_target() -> Node2D:
	return attacking_target

# Hàm bắn tên cho lính cung
func _shoot_arrow(target_position: Vector2) -> void:
	# Tạo mũi tên
	var arrow = Area2D.new()
	arrow.name = "JumpingAllyArrow"

	# Thêm hình dạng cho mũi tên
	var collision_shape = CollisionShape2D.new()
	var shape = RectangleShape2D.new()
	shape.size = Vector2(20, 5)
	collision_shape.shape = shape
	arrow.add_child(collision_shape)

	# Thêm sprite cho mũi tên
	var sprite = Sprite2D.new()
	sprite.texture = preload("res://icon.svg")  # Sử dụng icon mặc định nếu không có texture mũi tên
	sprite.scale = Vector2(0.2, 0.05)  # Thu nhỏ sprite
	arrow.add_child(sprite)

	# Thêm mũi tên vào scene
	get_tree().get_root().add_child(arrow)

	# Đặt vị trí ban đầu của mũi tên
	arrow.global_position = global_position + Vector2(0, -10)

	# Tính toán hướng bắn
	var direction = (target_position - arrow.global_position).normalized()

	# Xoay mũi tên theo hướng bắn
	sprite.rotation = direction.angle()
	collision_shape.rotation = direction.angle()

	# Tạo script cho mũi tên
	var script = GDScript.new()
	script.source_code = """
extends Area2D

var velocity = Vector2.ZERO
var speed = 600
var damage = 20
var max_lifetime = 2.0
var lifetime = 0.0

func _ready():
	# Kết nối tín hiệu va chạm
	body_entered.connect(_on_body_entered)

func _physics_process(delta):
	# Di chuyển mũi tên
	position += velocity * delta

	# Tăng thời gian sống
	lifetime += delta
	if lifetime > max_lifetime:
		queue_free()

func _on_body_entered(body):
	# Chỉ gây sát thương cho kẻ địch
	if body.is_in_group("enemy_group") and body.has_method("take_damage"):
		body.take_damage(damage)

		# Hiển thị hiệu ứng hit
		var damage_manager = get_node_or_null("/root/DamageEffects")
		if damage_manager:
			damage_manager.create_hit_effect(body.global_position)
			damage_manager.create_damage_number(body.global_position + Vector2(0, -20), damage, false, Color(0.3, 0.7, 1.0))

		# Xóa mũi tên sau khi trúng mục tiêu
		queue_free()
"""
	script.reload()
	arrow.set_script(script)

	# Đặt vận tốc cho mũi tên
	arrow.velocity = direction * 600
