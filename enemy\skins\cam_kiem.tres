[gd_resource type="SpriteFrames" load_steps=40 format=3 uid="uid://cswnw1ggtmrk4"]

[ext_resource type="Texture2D" uid="uid://bxhfp8o7qms5y" path="res://assets/images/characters/enimies/linh_dich_kiem.png" id="1_fsm3e"]

[sub_resource type="AtlasTexture" id="AtlasTexture_jil0y"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_k2gtu"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_yvii4"]
atlas = ExtResource("1_fsm3e")
region = Rect2(136, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_30tuy"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_a3t51"]
atlas = ExtResource("1_fsm3e")
region = Rect2(272, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_7e8bi"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ggeyt"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_m4dbu"]
atlas = ExtResource("1_fsm3e")
region = Rect2(136, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_uh472"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_op6s5"]
atlas = ExtResource("1_fsm3e")
region = Rect2(272, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_14ptc"]
atlas = ExtResource("1_fsm3e")
region = Rect2(340, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qhiol"]
atlas = ExtResource("1_fsm3e")
region = Rect2(408, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ogdtq"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_084br"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_25o5l"]
atlas = ExtResource("1_fsm3e")
region = Rect2(136, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_nl74y"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_d0p5b"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_21kbc"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_jyx57"]
atlas = ExtResource("1_fsm3e")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_s83fa"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_17wsl"]
atlas = ExtResource("1_fsm3e")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_mgj3o"]
atlas = ExtResource("1_fsm3e")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_f8ql8"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_k4li7"]
atlas = ExtResource("1_fsm3e")
region = Rect2(272, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_yaqgj"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8xb27"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_u2cdo"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_yj1oa"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_rpqq6"]
atlas = ExtResource("1_fsm3e")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_f687d"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_7e7y5"]
atlas = ExtResource("1_fsm3e")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_825jf"]
atlas = ExtResource("1_fsm3e")
region = Rect2(340, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_lhdy4"]
atlas = ExtResource("1_fsm3e")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_tk4is"]
atlas = ExtResource("1_fsm3e")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0woj5"]
atlas = ExtResource("1_fsm3e")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_tacaq"]
atlas = ExtResource("1_fsm3e")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_knc7p"]
atlas = ExtResource("1_fsm3e")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_j53xp"]
atlas = ExtResource("1_fsm3e")
region = Rect2(340, 68, 68, 68)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jil0y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k2gtu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yvii4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_30tuy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a3t51")
}],
"loop": false,
"name": &"attack",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7e8bi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ggeyt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m4dbu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uh472")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_op6s5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_14ptc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qhiol")
}],
"loop": true,
"name": &"die",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ogdtq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_084br")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_25o5l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nl74y")
}],
"loop": false,
"name": &"hurt",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_d0p5b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_21kbc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jyx57")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s83fa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_17wsl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mgj3o")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_f8ql8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k4li7")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_yaqgj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8xb27")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_u2cdo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yj1oa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rpqq6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f687d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7e7y5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_825jf")
}],
"loop": true,
"name": &"run",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_lhdy4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tk4is")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0woj5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tacaq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_knc7p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j53xp")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}]
