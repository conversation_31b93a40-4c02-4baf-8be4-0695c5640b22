[gd_resource type="SpriteFrames" load_steps=14 format=3 uid="uid://d04tt6sfvdfyq"]

[sub_resource type="AtlasTexture" id="AtlasTexture_v0uu2"]
region = Rect2(0, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_a8onp"]
region = Rect2(64, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tqfu2"]
region = Rect2(128, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_d4tdc"]
region = Rect2(192, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vet1f"]
region = Rect2(256, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_8pkfn"]
region = Rect2(320, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_fk2ce"]
region = Rect2(384, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ycodr"]
region = Rect2(448, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_4xfbv"]
region = Rect2(512, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_qnmel"]
region = Rect2(576, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_kl6r2"]
region = Rect2(640, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_exitq"]
region = Rect2(704, 448, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_4puww"]
region = Rect2(768, 448, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_v0uu2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a8onp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tqfu2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_d4tdc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vet1f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8pkfn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fk2ce")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ycodr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4xfbv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qnmel")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kl6r2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_exitq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4puww")
}],
"loop": true,
"name": &"burst",
"speed": 10.0
}]
