[gd_resource type="Resource" script_class="ClassDefinition" load_steps=3 format=3 uid="uid://c834sa6bgw7y4"]

[ext_resource type="Script" path="res://classes/scripts/class_definition.gd" id="1_fbt4t"]
[ext_resource type="SpriteFrames" uid="uid://cxkkmyos44qi0" path="res://classes/frames/spearman_frame.tres" id="2_gbu6x"]

[resource]
script = ExtResource("1_fbt4t")
mission = "Spearman"
base_health = 140.0
base_mana = 80.0
base_damage = 16.0
base_speed = 11.0
base_agility = 13.0
sprite_fames = ExtResource("2_gbu6x")
class_description = ""
