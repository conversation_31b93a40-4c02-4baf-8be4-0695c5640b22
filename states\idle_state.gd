# IdleState.gd
extends PlayerState
class_name IdleState

func get_state_name():
	return "idle"

func enter(owner):
	if owner.is_attacking:
		return
	owner.play_animation("idle")
	print("Player entered Idle State")

func exit(owner):
	print("Player exited Idle State")

func handle_input(owner, event: InputEvent):
	if owner.is_attacking:
		return
	# If a left/right input is detected, switch to the run state.
	var direction = Input.get_axis("move_left", "move_right")
	if abs(direction) > 0:
		owner.change_state(WalkState.new())

	# If jump is pressed and the player is on the floor
	if event.is_action_pressed("jump") and owner.is_on_floor():
		owner.change_state(JumpState.new())

func update(owner, delta):
	# Stay idle; you might also decide to transition out if other conditions occur.
	pass
