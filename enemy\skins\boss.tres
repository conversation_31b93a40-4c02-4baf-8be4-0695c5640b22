[gd_resource type="SpriteFrames" load_steps=70 format=3 uid="uid://dunuud7tbqfb5"]

[ext_resource type="Texture2D" uid="uid://b253yd4hidr23" path="res://assets/images/characters/enimies/boss.png" id="1_usg3k"]

[sub_resource type="AtlasTexture" id="AtlasTexture_10sqw"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 297, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_h3hbl"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 297, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_4oyyd"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 297, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_em7wf"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 297, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_hl3e5"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 297, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_4avwy"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 297, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_g5sdt"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 396, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_cikm0"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 396, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_ux2ix"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 396, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_y6rcl"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 396, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_fpt6k"]
atlas = ExtResource("1_usg3k")
region = Rect2(891, 792, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_q0mok"]
atlas = ExtResource("1_usg3k")
region = Rect2(891, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_8og4l"]
atlas = ExtResource("1_usg3k")
region = Rect2(792, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_tr70t"]
atlas = ExtResource("1_usg3k")
region = Rect2(693, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_l5j0y"]
atlas = ExtResource("1_usg3k")
region = Rect2(594, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_iodpj"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_n3c8l"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_2ucv0"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_4bd57"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_id3ku"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_rpnld"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 693, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_yienh"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_cd4qx"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_s6np8"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_j0vys"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_ejei0"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_05xr4"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_p7674"]
atlas = ExtResource("1_usg3k")
region = Rect2(594, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_6cg7q"]
atlas = ExtResource("1_usg3k")
region = Rect2(693, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_xuh1v"]
atlas = ExtResource("1_usg3k")
region = Rect2(792, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_l4u1x"]
atlas = ExtResource("1_usg3k")
region = Rect2(891, 594, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_20bar"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_aq1fk"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_1hhxb"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_xlo6b"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_bml0u"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_ktdv4"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_ci2oo"]
atlas = ExtResource("1_usg3k")
region = Rect2(594, 891, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_1jhbi"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 792, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_tsgr0"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 792, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_88mkb"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 792, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_j1jhb"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 792, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_oefot"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 0, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_wc5wh"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 0, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_3sxin"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 0, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_hxaej"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 0, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_3x6rv"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 198, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_142xx"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 198, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_qsjkd"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 198, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_q66k0"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 198, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_pc45w"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 198, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_rmfds"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 198, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_q2npr"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 99, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_dqn4g"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 99, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_uyydm"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 99, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_tgqh8"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 99, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_b1njh"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 99, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_4f5jn"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 99, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_luqfv"]
atlas = ExtResource("1_usg3k")
region = Rect2(0, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_3o6vw"]
atlas = ExtResource("1_usg3k")
region = Rect2(99, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_2twm5"]
atlas = ExtResource("1_usg3k")
region = Rect2(198, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_bouu1"]
atlas = ExtResource("1_usg3k")
region = Rect2(297, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_nwdqe"]
atlas = ExtResource("1_usg3k")
region = Rect2(396, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_hupo4"]
atlas = ExtResource("1_usg3k")
region = Rect2(495, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_ary1m"]
atlas = ExtResource("1_usg3k")
region = Rect2(594, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_a2vio"]
atlas = ExtResource("1_usg3k")
region = Rect2(693, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_p3qfl"]
atlas = ExtResource("1_usg3k")
region = Rect2(792, 495, 99, 99)

[sub_resource type="AtlasTexture" id="AtlasTexture_pyfi5"]
atlas = ExtResource("1_usg3k")
region = Rect2(891, 495, 99, 99)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_10sqw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h3hbl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4oyyd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_em7wf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hl3e5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4avwy")
}],
"loop": false,
"name": &"attack",
"speed": 25.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_g5sdt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cikm0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ux2ix")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y6rcl")
}],
"loop": false,
"name": &"attack_2",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fpt6k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q0mok")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8og4l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tr70t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l5j0y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iodpj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n3c8l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2ucv0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4bd57")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_id3ku")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rpnld")
}],
"loop": false,
"name": &"combine",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_yienh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cd4qx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s6np8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j0vys")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ejei0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_05xr4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p7674")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6cg7q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xuh1v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l4u1x")
}],
"loop": false,
"name": &"decay",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_20bar")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aq1fk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1hhxb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xlo6b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bml0u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ktdv4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ci2oo")
}],
"loop": false,
"name": &"die",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1jhbi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tsgr0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_88mkb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j1jhb")
}],
"loop": false,
"name": &"hurt",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_oefot")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wc5wh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3sxin")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hxaej")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3x6rv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_142xx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qsjkd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q66k0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pc45w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rmfds")
}],
"loop": true,
"name": &"jump",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_q2npr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dqn4g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uyydm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tgqh8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b1njh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4f5jn")
}],
"loop": true,
"name": &"running",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_luqfv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3o6vw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2twm5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bouu1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nwdqe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hupo4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ary1m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a2vio")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p3qfl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pyfi5")
}],
"loop": true,
"name": &"stage",
"speed": 10.0
}]
