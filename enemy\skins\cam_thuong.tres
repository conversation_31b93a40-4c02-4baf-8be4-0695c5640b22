[gd_resource type="SpriteFrames" load_steps=40 format=3 uid="uid://kbq1smp4buo4"]

[ext_resource type="Texture2D" uid="uid://caxf31rnwdoec" path="res://assets/images/characters/enimies/linh_dich_thuong.png" id="1_efnp2"]

[sub_resource type="AtlasTexture" id="AtlasTexture_ajnjq"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_f0nly"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_sjf3j"]
atlas = ExtResource("1_efnp2")
region = Rect2(136, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_pj0d5"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0acmb"]
atlas = ExtResource("1_efnp2")
region = Rect2(272, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_r5odx"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_hmo7m"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_5jtbe"]
atlas = ExtResource("1_efnp2")
region = Rect2(136, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_edeml"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_jdpul"]
atlas = ExtResource("1_efnp2")
region = Rect2(272, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_r2fhv"]
atlas = ExtResource("1_efnp2")
region = Rect2(340, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_txkqe"]
atlas = ExtResource("1_efnp2")
region = Rect2(408, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_fhgsx"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0i0to"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_mt851"]
atlas = ExtResource("1_efnp2")
region = Rect2(136, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bpre0"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8d8yg"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ju1k"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_mhcxb"]
atlas = ExtResource("1_efnp2")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_spioa"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_pwgia"]
atlas = ExtResource("1_efnp2")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ehusf"]
atlas = ExtResource("1_efnp2")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8xvu0"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_82jec"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_mgpmv"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xofcj"]
atlas = ExtResource("1_efnp2")
region = Rect2(272, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0hnm7"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_m1p4o"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_7idbw"]
atlas = ExtResource("1_efnp2")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_kuvam"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_hjnyk"]
atlas = ExtResource("1_efnp2")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_rwtjf"]
atlas = ExtResource("1_efnp2")
region = Rect2(340, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ndgtm"]
atlas = ExtResource("1_efnp2")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_u0fo5"]
atlas = ExtResource("1_efnp2")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xe58v"]
atlas = ExtResource("1_efnp2")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qveg7"]
atlas = ExtResource("1_efnp2")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_wmigw"]
atlas = ExtResource("1_efnp2")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_cxbfn"]
atlas = ExtResource("1_efnp2")
region = Rect2(340, 68, 68, 68)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ajnjq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f0nly")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sjf3j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pj0d5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0acmb")
}],
"loop": false,
"name": &"attack",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_r5odx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hmo7m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5jtbe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_edeml")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jdpul")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r2fhv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_txkqe")
}],
"loop": true,
"name": &"die",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fhgsx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0i0to")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mt851")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bpre0")
}],
"loop": false,
"name": &"hurt",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_8d8yg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ju1k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mhcxb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_spioa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pwgia")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ehusf")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_8xvu0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_82jec")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mgpmv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xofcj")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_0hnm7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m1p4o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7idbw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kuvam")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hjnyk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rwtjf")
}],
"loop": true,
"name": &"run",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ndgtm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u0fo5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xe58v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qveg7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wmigw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cxbfn")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}]
