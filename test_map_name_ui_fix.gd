extends Node2D

"""
Test script to verify the map name UI fix for the persistent display issue
"""

var map_name_ui: GlobalMapNameUI

func _ready():
	print("🧪 Testing Map Name UI Fix")
	
	# Create the map name UI
	map_name_ui = GlobalMapNameUI.new()
	add_child(map_name_ui)
	
	# Test the fix
	test_map_name_display_fix()

func test_map_name_display_fix():
	"""Test that the map name UI properly hides and doesn't get stuck"""
	print("🔧 Testing map name display fix...")
	
	# Test 1: Normal display cycle
	print("📍 Test 1: Normal display cycle")
	map_name_ui.set_map_name("dong_dau")
	map_name_ui.show_map_name()
	
	# Wait and check if it auto-hides
	await get_tree().create_timer(6.0).timeout
	if map_name_ui.is_showing():
		print("❌ Test 1 FAILED: Map name still showing after 6 seconds")
	else:
		print("✅ Test 1 PASSED: Map name properly hidden")
	
	# Test 2: Force hide
	await get_tree().create_timer(1.0).timeout
	print("📍 Test 2: Force hide test")
	map_name_ui.set_map_name("lang_van_lang")
	map_name_ui.show_map_name()
	await get_tree().create_timer(1.0).timeout
	map_name_ui.hide_map_name()
	
	if map_name_ui.is_showing():
		print("❌ Test 2 FAILED: Map name still showing after force hide")
	else:
		print("✅ Test 2 PASSED: Force hide works correctly")
	
	# Test 3: Safety timer test
	await get_tree().create_timer(1.0).timeout
	print("📍 Test 3: Safety timer test (this will take 10+ seconds)")
	map_name_ui.set_map_name("rung_nuong")
	map_name_ui.show_map_name()
	
	# Wait for safety timer to trigger (should be 10 seconds)
	await get_tree().create_timer(12.0).timeout
	if map_name_ui.is_showing():
		print("❌ Test 3 FAILED: Safety timer didn't work")
	else:
		print("✅ Test 3 PASSED: Safety timer works correctly")
	
	print("🏁 All tests completed!")

func _input(event):
	"""Handle input for manual testing"""
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("🔄 Manual test - showing map name")
		map_name_ui.set_map_name("dong_dau")
		map_name_ui.show_map_name()
	
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("❌ Manual test - hiding map name")
		map_name_ui.hide_map_name()
	
	elif event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_S:
				print("📊 Status check:")
				print("   - Is showing: %s" % map_name_ui.is_showing())
				print("   - Current map: %s" % map_name_ui.current_map_name)
			KEY_R:
				print("🔄 Reset test")
				map_name_ui.hide_map_name()
				test_map_name_display_fix()
