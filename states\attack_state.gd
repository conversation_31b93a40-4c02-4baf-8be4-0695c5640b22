extends PlayerState
class_name AttackState

func get_state_name():
	return "attack"

func enter(owner: Player) -> void:
	if owner.is_attacking:
		return
	print("Entered AttackState: Attack animation started.")
	owner.is_attacking = true

func exit(owner: Player) -> void:
	owner.is_attacking = false
	print("Exited AttackState.")

func handle_input(owner: Player, event: InputEvent) -> void:
	if owner.is_attacking:
		return
	owner.abilityManager.handle_combo_input("")
