extends Node2D

signal wave_started(wave_number: int)
signal enemy_defeated(total_defeated: int, total_to_defeat: int)
signal all_waves_completed

# Enemy scene to spawn
@export var enemy_scene: PackedScene
# Spawn points for the front gate
@export var front_gate_spawn_points: Array[Node2D] = []
# Spawn points for the back gate
@export var back_gate_spawn_points: Array[Node2D] = []
# Total number of enemies to spawn (10 enemies per wave * number of waves)
@export var total_enemies: int = 50
# Number of waves
@export var num_waves: int = 5
# Time between waves in seconds (10 phút = 600 giây)
@export var time_between_waves: float = 600.0
# Time between enemy spawns in seconds
@export var time_between_spawns: float = 0.7
# Thời gian hiển thị đếm ngược cho mỗi đợt (1 phút = 60 giây)
@export var wave_countdown_time: float = 60.0
# Số lượng kẻ địch mỗi cổng mỗi đợt
@export var enemies_per_gate: int = 5
# Enemy types to spawn (sword, spear, bow)
@export var enemy_types: Array[String] = ["sword", "spear", "bow"]
# Visual gates for spawning
@export var left_gate_path: NodePath
@export var right_gate_path: NodePath

# Internal variables
var current_wave: int = 0
var enemies_spawned: int = 0
var enemies_defeated: int = 0
var wave_in_progress: bool = false
var spawn_timer: float = 0.0
var wave_timer: float = 0.0
var wave_countdown: float = 0.0  # Đếm ngược thời gian cho mỗi đợt
var active_enemies: Array = []
var quest_id: String = "defend_village"
var is_active: bool = false
var allies_alive: bool = true  # Trạng thái đồng minh còn sống

func _ready() -> void:
	# Initialize
	set_process(false)  # Don't process until activated

	# Add to enemy_spawners group for easy finding
	add_to_group("enemy_spawners")

	print("EnemyWaveSpawner initialized and added to enemy_spawners group")

	# Kiểm tra enemy_scene
	if not enemy_scene:
		print("WARNING: Enemy scene not set, trying to load it")
		var enemy_paths = [
			"res://enemy/scenes/Enemy.tscn",
			"res://enemies/Enemy.tscn",
			"res://npcs/Enemy.tscn",
			"res://characters/Enemy.tscn"
		]

		for path in enemy_paths:
			if FileAccess.file_exists(path):
				enemy_scene = load(path)
				if enemy_scene:
					print("Successfully loaded enemy scene from: " + path)
					break

		if not enemy_scene:
			print("ERROR: Failed to load enemy scene from any path")
			print("Available paths checked: " + str(enemy_paths))

	# Kiểm tra và cập nhật spawn points từ scene
	_update_spawn_points_from_scene()

	print("Front gate spawn points after initialization: " + str(front_gate_spawn_points.size()))
	print("Back gate spawn points after initialization: " + str(back_gate_spawn_points.size()))

	# Kiểm tra lại nếu vẫn không có spawn points
	if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
		print("WARNING: Spawn points not found after initialization, creating emergency spawn points")
		_create_emergency_spawn_points()
		print("Front gate spawn points after emergency creation: " + str(front_gate_spawn_points.size()))
		print("Back gate spawn points after emergency creation: " + str(back_gate_spawn_points.size()))

	# Connect to visual gates if they exist
	_connect_to_gates()

	# Tạo bảng thông báo nhỏ ở góc trên bên phải
	_create_enemy_info_panel()

func start_spawning() -> void:
	print("Starting enemy wave spawner")
	print("Enemy scene: " + str(enemy_scene))
	print("Front gate spawn points: " + str(front_gate_spawn_points.size()))
	print("Back gate spawn points: " + str(back_gate_spawn_points.size()))
	print("Left gate path: " + str(left_gate_path))
	print("Right gate path: " + str(right_gate_path))

	if is_active:
		print("Spawner already active, returning")
		return

	# Kiểm tra các điều kiện cần thiết
	if not enemy_scene:
		print("ERROR: Enemy scene not set, cannot start spawning")
		# Thử tải enemy scene nếu chưa có
		enemy_scene = load("res://enemy/scenes/Enemy.tscn")
		if not enemy_scene:
			print("ERROR: Failed to load enemy scene")
			return
		else:
			print("Successfully loaded enemy scene")

	# Cập nhật spawn points từ scene nếu chưa có
	if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
		print("Spawn points not set, updating from scene...")
		_update_spawn_points_from_scene()

	# Kiểm tra và in thông tin chi tiết về spawn points
	print("Front gate spawn points (" + str(front_gate_spawn_points.size()) + "): " + str(front_gate_spawn_points))
	print("Back gate spawn points (" + str(back_gate_spawn_points.size()) + "): " + str(back_gate_spawn_points))

	# Kiểm tra lại xem có spawn points không
	if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
		print("ERROR: Failed to set spawn points, creating emergency spawn points")
		_create_emergency_spawn_points()

		# Kiểm tra lại một lần nữa
		if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
			print("ERROR: Still no spawn points after emergency creation, cannot start spawning")
			return

	# Khởi tạo spawner
	is_active = true
	current_wave = 0
	enemies_spawned = 0
	enemies_defeated = 0
	wave_in_progress = false
	spawn_timer = 0.0
	wave_timer = 0.0
	active_enemies.clear()

	print("Spawner initialized, setting process to true")
	set_process(true)

	print("Starting first wave")
	start_next_wave()

	print("Spawner started successfully")

func stop_spawning() -> void:
	print("Stopping enemy wave spawner")
	is_active = false
	set_process(false)

	# Clean up any remaining enemies
	for enemy in active_enemies:
		if is_instance_valid(enemy):
			enemy.queue_free()

	active_enemies.clear()

func _process(delta: float) -> void:
	if not is_active:
		# Không in log quá nhiều
		return

	# Kiểm tra trạng thái đồng minh
	_check_allies_status()

	# Nếu tất cả đồng minh đã chết, kết thúc nhiệm vụ với thất bại
	if not allies_alive:
		_fail_mission()
		return

	# Xử lý logic khi đợt đang diễn ra
	if wave_in_progress:
		# Giảm thời gian đếm ngược và đảm bảo không âm
		wave_countdown = max(0.0, wave_countdown - delta)

		# Cập nhật hiển thị thời gian đếm ngược
		_update_countdown_display(wave_countdown)

		# Mỗi đợt chỉ sinh 10 kẻ địch (mỗi cổng 5)
		var total_enemies_per_wave = enemies_per_gate * 2  # Tổng cộng 10 kẻ địch mỗi đợt

		# Giảm thời gian giữa các lần sinh kẻ địch
		spawn_timer -= delta

		# Sinh kẻ địch theo thời gian
		if spawn_timer <= 0.0 and enemies_spawned < total_enemies_per_wave:
			# Đặt lại bộ đếm thời gian
			spawn_timer = time_between_spawns

			# Tính toán số lượng kẻ địch cần sinh ở mỗi cổng
			var front_gate_spawned = 0
			var back_gate_spawned = 0

			# Đếm số lượng kẻ địch đã sinh ở mỗi cổng
			for i in range(enemies_spawned):
				if i % 2 == 0:
					front_gate_spawned += 1
				else:
					back_gate_spawned += 1

			# Kiểm tra xem cần sinh kẻ địch ở cổng nào
			var spawn_front = front_gate_spawned < enemies_per_gate
			var spawn_back = back_gate_spawned < enemies_per_gate

			print("Current spawned: " + str(enemies_spawned) + "/" + str(total_enemies_per_wave))
			print("Front gate: " + str(front_gate_spawned) + "/" + str(enemies_per_gate))
			print("Back gate: " + str(back_gate_spawned) + "/" + str(enemies_per_gate))

			# Sinh kẻ địch ở cổng trước nếu cần
			if spawn_front:
				print("Spawning enemy at front gate...")
				var front_enemy = spawn_enemy_at_gate(front_gate_spawn_points, true)
				if front_enemy:
					enemies_spawned += 1
					print("Successfully spawned enemy at front gate. Total: " + str(enemies_spawned))
					# Cập nhật thông tin trên bảng thông báo
					_update_enemy_count_on_panel()

			# Sinh kẻ địch ở cổng sau nếu cần
			if spawn_back:
				print("Spawning enemy at back gate...")
				var back_enemy = spawn_enemy_at_gate(back_gate_spawn_points, false)
				if back_enemy:
					enemies_spawned += 1
					print("Successfully spawned enemy at back gate. Total: " + str(enemies_spawned))
					# Cập nhật thông tin trên bảng thông báo
					_update_enemy_count_on_panel()

		# Kiểm tra nếu hết thời gian đếm ngược
		if wave_countdown <= 0 and enemies_spawned < enemies_per_gate * 2:
			# Nếu chưa sinh đủ kẻ địch, sinh nhanh các kẻ địch còn lại
			_flash_enemy_info_panel("Thời gian hết! Kẻ địch tấn công ồ ạt!")

			# Tính toán số lượng kẻ địch cần sinh ở mỗi cổng
			var front_gate_spawned = 0
			var back_gate_spawned = 0

			# Đếm số lượng kẻ địch đã sinh ở mỗi cổng
			for i in range(enemies_spawned):
				if i % 2 == 0:
					front_gate_spawned += 1
				else:
					back_gate_spawned += 1

			print("Time's up! Rapid spawning remaining enemies...")
			print("Front gate: " + str(front_gate_spawned) + "/" + str(enemies_per_gate))
			print("Back gate: " + str(back_gate_spawned) + "/" + str(enemies_per_gate))

			# Sinh nhanh các kẻ địch còn lại ở cổng trước
			while front_gate_spawned < enemies_per_gate:
				print("Rapid spawning enemy at front gate...")
				var front_enemy = spawn_enemy_at_gate(front_gate_spawn_points, true)
				if front_enemy:
					enemies_spawned += 1
					front_gate_spawned += 1
					print("Successfully spawned enemy at front gate. Total: " + str(enemies_spawned))
					# Cập nhật thông tin trên bảng thông báo
					_update_enemy_count_on_panel()
				await get_tree().create_timer(0.2).timeout

			# Sinh nhanh các kẻ địch còn lại ở cổng sau
			while back_gate_spawned < enemies_per_gate:
				print("Rapid spawning enemy at back gate...")
				var back_enemy = spawn_enemy_at_gate(back_gate_spawn_points, false)
				if back_enemy:
					enemies_spawned += 1
					back_gate_spawned += 1
					print("Successfully spawned enemy at back gate. Total: " + str(enemies_spawned))
					# Cập nhật thông tin trên bảng thông báo
					_update_enemy_count_on_panel()
				await get_tree().create_timer(0.2).timeout

		# Kiểm tra nếu đợt đã hoàn thành
		if enemies_spawned >= enemies_per_gate * 2 and active_enemies.size() == 0:
			wave_in_progress = false
			wave_timer = time_between_waves
			print("Wave " + str(current_wave) + " completed")

			if current_wave >= num_waves:
				print("All waves completed")
				emit_signal("all_waves_completed")
				stop_spawning()
			else:
				print("Starting wave timer for next wave")
				_flash_enemy_info_panel("Đợt tiếp theo sẽ bắt đầu sau 10 phút!")
	else:
		# Wait for next wave
		wave_timer = max(0.0, wave_timer - delta)  # Đảm bảo không âm

		# Hiển thị thời gian chờ đợt tiếp theo
		var minutes = int(wave_timer) / 60
		var seconds = int(wave_timer) % 60
		_update_wave_timer_display(minutes, seconds)

		# Chỉ in log khi bắt đầu wave mới
		if wave_timer <= 0.0:
			print("Timer expired, starting next wave")
			start_next_wave()

func start_next_wave() -> void:
	current_wave += 1
	print("Starting wave " + str(current_wave))
	wave_in_progress = true
	enemies_spawned = 0
	spawn_timer = 0.0  # Spawn first enemy immediately
	wave_countdown = wave_countdown_time  # Khởi tạo đếm ngược 1 phút

	# Debug info
	print("Wave in progress: " + str(wave_in_progress))
	print("Enemies spawned: " + str(enemies_spawned))
	print("Spawn timer: " + str(spawn_timer))
	print("Wave countdown: " + str(wave_countdown))
	print("Enemy scene: " + str(enemy_scene))
	print("Front gate spawn points: " + str(front_gate_spawn_points))
	print("Back gate spawn points: " + str(back_gate_spawn_points))

	# Hiển thị thông báo bắt đầu đợt mới
	_flash_enemy_info_panel("Đợt " + str(current_wave) + " bắt đầu! Chuẩn bị chiến đấu!")

	# Sinh kẻ địch đầu tiên ở cổng trước
	print("Spawning initial enemy at front gate...")
	var front_enemy = spawn_enemy_at_gate(front_gate_spawn_points, true)
	if front_enemy:
		enemies_spawned += 1
		print("Successfully spawned initial enemy at front gate. Total: " + str(enemies_spawned))
	else:
		print("ERROR: Failed to spawn initial enemy at front gate")

	# Cập nhật thông tin trên bảng thông báo
	_update_enemy_count_on_panel()

	# Chờ một chút trước khi sinh kẻ địch ở cổng sau
	await get_tree().create_timer(0.5).timeout

	# Sinh kẻ địch đầu tiên ở cổng sau
	print("Spawning initial enemy at back gate...")
	var back_enemy = spawn_enemy_at_gate(back_gate_spawn_points, false)
	if back_enemy:
		enemies_spawned += 1
		print("Successfully spawned initial enemy at back gate. Total: " + str(enemies_spawned))
	else:
		print("ERROR: Failed to spawn initial enemy at back gate")

	# Cập nhật thông tin trên bảng thông báo
	_update_enemy_count_on_panel()

	print("Initial enemies spawned, total: " + str(enemies_spawned))

	emit_signal("wave_started", current_wave)

func spawn_enemy() -> void:
	print("Attempting to spawn enemies...")

	if not enemy_scene:
		print("ERROR: Enemy scene not set")
		# Thử tải enemy scene nếu chưa có
		enemy_scene = load("res://enemy/scenes/Enemy.tscn")
		if not enemy_scene:
			print("ERROR: Failed to load enemy scene")
			return
		else:
			print("Successfully loaded enemy scene")

	# Kiểm tra và in thông tin chi tiết về spawn points
	print("Front gate spawn points (" + str(front_gate_spawn_points.size()) + "): " + str(front_gate_spawn_points))
	print("Back gate spawn points (" + str(back_gate_spawn_points.size()) + "): " + str(back_gate_spawn_points))

	# Kiểm tra các điều kiện cần thiết
	if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
		print("ERROR: Spawn points not set, cannot spawn enemies")

		# Cập nhật spawn points từ scene
		_update_spawn_points_from_scene()

		# Nếu vẫn không có, tạo spawn points khẩn cấp
		if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
			print("ERROR: Still no spawn points after scene update, creating emergency spawn points")
			_create_emergency_spawn_points()

		# Kiểm tra lại sau khi tạo
		if front_gate_spawn_points.size() == 0 or back_gate_spawn_points.size() == 0:
			print("ERROR: Still no spawn points after emergency creation")
			return
		else:
			print("Emergency spawn points created successfully")

	# QUAN TRỌNG: Sinh kẻ địch ở CẢ HAI cổng một cách riêng biệt
	# Sinh kẻ địch ở cổng trước
	var front_enemy = spawn_enemy_at_gate(front_gate_spawn_points, true)
	if front_enemy:
		print("Successfully spawned enemy at front gate")
	else:
		print("ERROR: Failed to spawn enemy at front gate")

	# Sinh kẻ địch ở cổng sau
	var back_enemy = spawn_enemy_at_gate(back_gate_spawn_points, false)
	if back_enemy:
		print("Successfully spawned enemy at back gate")
	else:
		print("ERROR: Failed to spawn enemy at back gate")

func _connect_to_gates() -> void:
	print("Connecting to visual gates...")

	# Get the left gate
	var left_gate = null
	if left_gate_path:
		left_gate = get_node_or_null(left_gate_path)
	if not left_gate:
		# Try to find the gate in the parent scene
		left_gate = get_tree().get_first_node_in_group("left_enemy_gate")
		if not left_gate:
			print("Left gate not found, searching by name...")
			var parent = get_parent()
			if parent:
				left_gate = parent.get_node_or_null("EnemyGates/LeftGate")

	# Get the right gate
	var right_gate = null
	if right_gate_path:
		right_gate = get_node_or_null(right_gate_path)
	if not right_gate:
		# Try to find the gate in the parent scene
		right_gate = get_tree().get_first_node_in_group("right_enemy_gate")
		if not right_gate:
			print("Right gate not found, searching by name...")
			var parent = get_parent()
			if parent:
				right_gate = parent.get_node_or_null("EnemyGates/RightGate")

	# Store references to the gates
	if left_gate:
		print("Found left gate: " + left_gate.name)
		left_gate_path = left_gate.get_path()
	else:
		print("Left gate not found")

	if right_gate:
		print("Found right gate: " + right_gate.name)
		right_gate_path = right_gate.get_path()
	else:
		print("Right gate not found")

func spawn_enemy_at_gate(spawn_points: Array, is_front_gate: bool = true) -> Object:
	if spawn_points.size() == 0:
		print("ERROR: No spawn points set for gate")
		return null

	# Validate spawn points
	var valid_spawn_points = []

	# Kiểm tra từng spawn point
	for i in range(spawn_points.size()):
		var current_point = spawn_points[i]

		# Kiểm tra xem spawn point có phải là Node2D không
		if current_point is Node2D and is_instance_valid(current_point):
			# Nếu đã là Node2D hợp lệ, thêm trực tiếp vào danh sách
			valid_spawn_points.append(current_point)
			print("Found valid spawn point (Node2D): " + current_point.name)
		elif current_point is NodePath:
			# Nếu là NodePath, thử lấy node
			var node = get_node_or_null(current_point)
			if node and node is Node2D:
				valid_spawn_points.append(node)
				print("Found valid spawn point (NodePath): " + node.name)
			else:
				print("ERROR: Invalid NodePath spawn point at index " + str(i) + ": " + str(current_point))
		else:
			print("ERROR: Invalid spawn point at index " + str(i) + ": " + str(current_point))

	# Nếu không tìm thấy spawn point hợp lệ, thử tìm trong scene
	if valid_spawn_points.size() == 0:
		print("No valid spawn points found, searching in scene...")

		# Tìm node cha
		var gate_node_name = "FrontGateSpawnPoints" if is_front_gate else "BackGateSpawnPoints"
		var gate_node = get_node_or_null(gate_node_name)

		if gate_node:
			print("Found " + gate_node_name + " with " + str(gate_node.get_child_count()) + " children")
			# Thêm tất cả các node con vào danh sách - thêm null check
			if gate_node and is_instance_valid(gate_node):
				for child in gate_node.get_children():
					if child is Node2D:
						valid_spawn_points.append(child)
						print("Added spawn point from scene: " + child.name)
		else:
			print("ERROR: " + gate_node_name + " not found in scene")

	# Nếu vẫn không tìm thấy spawn point hợp lệ, tạo mới
	if valid_spawn_points.size() == 0:
		print("ERROR: No valid spawn points found, creating emergency spawn points")

		# Gọi hàm tạo spawn points khẩn cấp
		_create_emergency_spawn_points()

		# Lấy các spawn points mới tạo
		if is_front_gate and front_gate_spawn_points.size() > 0:
			valid_spawn_points = front_gate_spawn_points.duplicate()
			print("Using front gate emergency spawn points")
		elif not is_front_gate and back_gate_spawn_points.size() > 0:
			valid_spawn_points = back_gate_spawn_points.duplicate()
			print("Using back gate emergency spawn points")
		else:
			print("ERROR: Failed to create emergency spawn points")
			return null

	# Choose a random spawn point
	var spawn_point = valid_spawn_points[randi() % valid_spawn_points.size()]
	print("Selected spawn point: " + spawn_point.name + " at " + str(spawn_point.global_position))

	# Trigger the visual gate effect
	_trigger_gate_effect(is_front_gate)

	# Choose a random enemy type
	var enemy_type = enemy_types[randi() % enemy_types.size()]
	print("Selected enemy type: " + enemy_type)

	# Instantiate the enemy
	print("Attempting to instantiate enemy scene: " + str(enemy_scene))
	var enemy_instance = enemy_scene.instantiate()
	if not enemy_instance:
		print("ERROR: Failed to instantiate enemy scene")
		return null

	print("Enemy instantiated successfully: " + str(enemy_instance))

	# Thiết lập loại kẻ địch
	print("Setting enemy type to: " + enemy_type)
	enemy_instance.type = enemy_type

	# Kiểm tra class manager
	var class_manager = enemy_instance.get_node_or_null("ClassManager")
	if class_manager and class_manager.has_method("update_class"):
		print("Updating enemy class via ClassManager")
		class_manager.update_class(enemy_instance, enemy_type)
	else:
		print("INFO: ClassManager not available - using default enemy configuration")

	# Thiết lập vị trí
	enemy_instance.global_position = spawn_point.global_position
	print("Set enemy position to: " + str(spawn_point.global_position))

	# Connect to the enemy's death signal
	if enemy_instance.has_signal("enemy_defeated"):
		enemy_instance.connect("enemy_defeated", _on_enemy_defeated)
		print("Connected enemy_defeated signal")
	else:
		print("WARNING: Enemy does not have enemy_defeated signal")

	# Add to the scene
	print("Adding enemy to root to avoid transform issues")
	get_tree().root.call_deferred("add_child", enemy_instance)

	print("Added enemy to scene")

	# Track the enemy - sử dụng call_deferred để đảm bảo enemy đã được thêm vào scene
	call_deferred("_track_enemy", enemy_instance)

	# Không tăng enemies_spawned ở đây nữa, đã xử lý trong _process

	print("Spawned " + enemy_type + " enemy at " + str(spawn_point.global_position))

	# Trả về enemy instance để kiểm tra
	return enemy_instance

func _trigger_gate_effect(is_front_gate: bool) -> void:
	# Get the appropriate gate
	var gate = null
	if is_front_gate:
		if left_gate_path:
			gate = get_node_or_null(left_gate_path)
	else:
		if right_gate_path:
			gate = get_node_or_null(right_gate_path)

	# Trigger the gate effect if the gate exists
	if gate and gate.has_method("trigger_spawn_effect"):
		print("Triggering spawn effect on gate: " + gate.name)
		gate.trigger_spawn_effect()
	else:
		print("Gate not found or doesn't have trigger_spawn_effect method")

	# Hiển thị thông báo trên panel
	var gate_name = "cổng trước" if is_front_gate else "cổng sau"
	_flash_enemy_info_panel("Kẻ địch xuất hiện ở " + gate_name + "!")

# Hàm theo dõi enemy sau khi đã được thêm vào scene
func _track_enemy(enemy_instance) -> void:
	if is_instance_valid(enemy_instance):
		active_enemies.append(enemy_instance)
		print("Enemy tracked: " + str(enemy_instance))
	else:
		print("ERROR: Enemy instance is no longer valid")

func _on_enemy_defeated(enemy) -> void:
	# Đảm bảo enemy đã được theo dõi
	if not active_enemies.has(enemy):
		print("WARNING: Enemy not in active_enemies list, may have been counted twice")
		return

	enemies_defeated += 1
	active_enemies.erase(enemy)

	print("Enemy defeated (" + str(enemies_defeated) + "/" + str(total_enemies) + ")")
	emit_signal("enemy_defeated", enemies_defeated, total_enemies)

	# Cập nhật thông tin trên bảng thông báo
	_update_enemy_info_panel(enemies_defeated, total_enemies)

	# Update quest progress
	if QuestSystem and QuestSystem.has_method("is_quest_active") and QuestSystem.is_quest_active(quest_id):
		print("Updating quest progress for quest: " + quest_id)
		QuestSystem.update_quest_progress(quest_id, 1)
	else:
		print("INFO: QuestSystem not available or quest not active: " + quest_id + " - enemy defeated without quest tracking")

	# Kiểm tra nếu đã đánh bại đủ số lượng kẻ địch
	if enemies_defeated >= total_enemies:
		print("All enemies defeated! Quest completed.")
		stop_spawning()

		# Hiển thị thông báo hoàn thành
		_flash_enemy_info_panel("Tất cả kẻ địch đã bị tiêu diệt! Nhiệm vụ hoàn thành!")

		# Đảm bảo quest được hoàn thành
		if QuestSystem and QuestSystem.has_method("complete_quest") and QuestSystem.is_quest_active(quest_id):
			print("Completing quest: " + quest_id)
			QuestSystem.complete_quest(quest_id)

# Hàm này không còn được sử dụng vì chúng ta đã cố định số lượng kẻ địch mỗi đợt
func enemies_per_wave() -> int:
	# Mỗi cổng sinh 5 kẻ địch, tổng cộng 10 kẻ địch mỗi đợt
	return 5

# Add a custom enemy defeated function for enemies that don't have the signal
func register_enemy_defeated(enemy) -> void:
	if active_enemies.has(enemy):
		_on_enemy_defeated(enemy)

# Tạo bảng thông báo nhỏ ở góc trên bên phải
func _create_enemy_info_panel() -> void:
	print("Creating enemy info panel...")

	# Kiểm tra xem đã có panel chưa
	var existing_panel = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if existing_panel:
		print("Enemy info panel already exists")
		return

	# Tạo canvas layer để hiển thị UI
	var canvas_layer = CanvasLayer.new()
	canvas_layer.name = "EnemyInfoPanelLayer"
	canvas_layer.layer = 10  # Hiển thị trên cùng
	get_tree().root.call_deferred("add_child", canvas_layer)

	# Tạo panel chứa thông tin
	var panel = Panel.new()
	panel.name = "EnemyInfoPanel"
	panel.size = Vector2(200, 120)  # Tăng kích thước để chứa thêm thông tin đếm ngược
	panel.position = Vector2(get_viewport().size.x - 220, 20)  # Góc trên bên phải

	# Tạo nút đóng/mở panel
	var toggle_button = Button.new()
	toggle_button.name = "ToggleButton"
	toggle_button.text = "X"
	toggle_button.size = Vector2(20, 20)
	toggle_button.position = Vector2(180, 0)  # Góc trên bên phải của panel
	toggle_button.add_theme_font_size_override("font_size", 12)
	toggle_button.add_theme_color_override("font_color", Color(1, 0.3, 0.3))
	toggle_button.add_theme_color_override("font_hover_color", Color(1, 0, 0))

	# Kết nối sự kiện nhấn nút
	toggle_button.pressed.connect(func(): _toggle_panel_visibility(panel))

	# Tạo style cho panel
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.1, 0.1, 0.1, 0.7)
	style_box.border_width_left = 2
	style_box.border_width_top = 2
	style_box.border_width_right = 2
	style_box.border_width_bottom = 2
	style_box.border_color = Color(1, 0, 0, 0.7)
	style_box.corner_radius_top_left = 5
	style_box.corner_radius_top_right = 5
	style_box.corner_radius_bottom_left = 5
	style_box.corner_radius_bottom_right = 5
	panel.add_theme_stylebox_override("panel", style_box)

	# Tạo tiêu đề
	var title_label = Label.new()
	title_label.name = "TitleLabel"
	title_label.text = "KẺ ĐỊCH"
	title_label.position = Vector2(10, 5)
	title_label.size = Vector2(180, 20)
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 16)
	title_label.add_theme_color_override("font_color", Color(1, 0.3, 0.3))

	# Tạo đường kẻ phân cách
	var separator = ColorRect.new()
	separator.name = "Separator"
	separator.color = Color(1, 0.3, 0.3, 0.5)
	separator.position = Vector2(10, 30)
	separator.size = Vector2(180, 1)

	# Tạo label hiển thị số lượng kẻ địch
	var count_label = Label.new()
	count_label.name = "CountLabel"
	count_label.text = "Đã xuất hiện: 0"
	count_label.position = Vector2(10, 35)
	count_label.size = Vector2(180, 20)
	count_label.add_theme_font_size_override("font_size", 14)
	count_label.add_theme_color_override("font_color", Color(1, 1, 1))

	# Tạo label hiển thị số lượng kẻ địch đã tiêu diệt
	var defeated_label = Label.new()
	defeated_label.name = "DefeatedLabel"
	defeated_label.text = "Đã tiêu diệt: 0/0"
	defeated_label.position = Vector2(10, 55)
	defeated_label.size = Vector2(180, 20)
	defeated_label.add_theme_font_size_override("font_size", 14)
	defeated_label.add_theme_color_override("font_color", Color(1, 1, 1))

	# Tạo label hiển thị wave hiện tại
	var wave_label = Label.new()
	wave_label.name = "WaveLabel"
	wave_label.text = "Đợt: 0/" + str(num_waves)
	wave_label.position = Vector2(10, 75)
	wave_label.size = Vector2(180, 20)
	wave_label.add_theme_font_size_override("font_size", 14)
	wave_label.add_theme_color_override("font_color", Color(1, 1, 1))

	# Tạo label hiển thị thời gian đếm ngược
	var countdown_label = Label.new()
	countdown_label.name = "CountdownLabel"
	countdown_label.text = "Thời gian: 01:00"
	countdown_label.position = Vector2(10, 95)
	countdown_label.size = Vector2(180, 20)
	countdown_label.add_theme_font_size_override("font_size", 14)
	countdown_label.add_theme_color_override("font_color", Color(1, 0.7, 0.7))

	# Thêm vào panel
	panel.add_child(title_label)
	panel.add_child(separator)
	panel.add_child(count_label)
	panel.add_child(defeated_label)
	panel.add_child(wave_label)
	panel.add_child(countdown_label)
	panel.add_child(toggle_button)

	# Thêm vào canvas layer
	canvas_layer.add_child(panel)

	print("Enemy info panel created")

	# Kết nối tín hiệu để cập nhật panel
	connect("enemy_defeated", _update_enemy_info_panel)
	connect("wave_started", _update_wave_info)

# Cập nhật thông tin kẻ địch trên panel
func _update_enemy_info_panel(enemies_defeated_count: int, total_enemies_count: int) -> void:
	var panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if not panel_layer:
		return

	var panel = panel_layer.get_node_or_null("EnemyInfoPanel")
	if not panel:
		return

	# Cập nhật số lượng kẻ địch đã tiêu diệt
	var defeated_label = panel.get_node_or_null("DefeatedLabel")
	if defeated_label:
		defeated_label.text = "Đã tiêu diệt: " + str(enemies_defeated_count) + "/" + str(total_enemies_count)

	# Cập nhật số lượng kẻ địch đã xuất hiện
	var count_label = panel.get_node_or_null("CountLabel")
	if count_label:
		count_label.text = "Đã xuất hiện: " + str(enemies_spawned)

	# Làm nổi bật panel khi có cập nhật
	var tween = get_tree().create_tween()
	var style_box = panel.get_theme_stylebox("panel")
	if style_box is StyleBoxFlat:
		var original_color = style_box.border_color
		style_box.border_color = Color(1, 0, 0, 1)
		tween.tween_interval(0.3)
		await tween.finished
		style_box.border_color = original_color

# Cập nhật số lượng kẻ địch đã xuất hiện trên panel
func _update_enemy_count_on_panel() -> void:
	var panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if not panel_layer:
		# Tạo panel nếu chưa có
		_create_enemy_info_panel()
		panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
		if not panel_layer:
			return

	var panel = panel_layer.get_node_or_null("EnemyInfoPanel")
	if not panel:
		return

	# Cập nhật số lượng kẻ địch đã xuất hiện
	var count_label = panel.get_node_or_null("CountLabel")
	if count_label:
		count_label.text = "Đã xuất hiện: " + str(enemies_spawned)

	# Làm nổi bật panel khi có kẻ địch mới
	var tween = get_tree().create_tween()
	tween.tween_property(panel, "modulate", Color(1.2, 1.0, 1.0), 0.2)
	tween.tween_property(panel, "modulate", Color(1, 1, 1), 0.2)

	# Làm nổi bật label số lượng
	if count_label:
		count_label.add_theme_color_override("font_color", Color(1, 0.5, 0.5))
		tween.tween_interval(0.5)
		tween.tween_callback(func(): count_label.add_theme_color_override("font_color", Color(1, 1, 1)))

# Cập nhật thông tin wave
func _update_wave_info(wave_number: int) -> void:
	var panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if not panel_layer:
		return

	var panel = panel_layer.get_node_or_null("EnemyInfoPanel")
	if not panel:
		return

	# Cập nhật wave hiện tại
	var wave_label = panel.get_node_or_null("WaveLabel")
	if wave_label:
		wave_label.text = "Đợt: " + str(wave_number) + "/" + str(num_waves)

	# Làm nổi bật panel khi có cập nhật
	var tween = get_tree().create_tween()
	tween.tween_property(panel, "modulate", Color(1.5, 1.5, 1.5), 0.3)
	tween.tween_property(panel, "modulate", Color(1, 1, 1), 0.3)

	# Thêm label hiển thị thời gian đếm ngược nếu chưa có
	if not panel.get_node_or_null("CountdownLabel"):
		var countdown_label = Label.new()
		countdown_label.name = "CountdownLabel"
		countdown_label.text = "Thời gian: 01:00"
		countdown_label.position = Vector2(10, 95)
		countdown_label.size = Vector2(180, 20)
		countdown_label.add_theme_font_size_override("font_size", 14)
		countdown_label.add_theme_color_override("font_color", Color(1, 0.7, 0.7))
		panel.add_child(countdown_label)

		# Điều chỉnh kích thước panel để hiển thị thêm label
		panel.size.y += 20

# Hiển thị thông báo nhấp nháy trên panel
func _flash_enemy_info_panel(message: String) -> void:
	var panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if not panel_layer:
		# Tạo panel nếu chưa có
		_create_enemy_info_panel()
		panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
		if not panel_layer:
			return

	var panel = panel_layer.get_node_or_null("EnemyInfoPanel")
	if not panel:
		return

	# Tạo label thông báo tạm thời
	var flash_label = Label.new()
	flash_label.name = "FlashLabel"
	flash_label.text = message
	flash_label.position = Vector2(10, 35)
	flash_label.size = Vector2(180, 40)
	flash_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	flash_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	flash_label.add_theme_font_size_override("font_size", 14)
	flash_label.add_theme_color_override("font_color", Color(1, 0, 0))
	flash_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.5))
	flash_label.add_theme_constant_override("shadow_offset_x", 1)
	flash_label.add_theme_constant_override("shadow_offset_y", 1)

	# Thêm vào panel
	panel.add_child(flash_label)

	# Đảm bảo panel hiển thị đầy đủ
	if panel.size.y <= 30:
		_toggle_panel_visibility(panel)

	# Làm nổi bật panel
	var panel_tween = get_tree().create_tween()
	panel_tween.tween_property(panel, "modulate", Color(1.5, 1.0, 1.0), 0.2)
	panel_tween.tween_property(panel, "modulate", Color(1, 1, 1), 0.2)

	# Hiệu ứng nhấp nháy cho label
	var label_tween = get_tree().create_tween()
	label_tween.set_loops(3)
	label_tween.tween_property(flash_label, "modulate:a", 0.3, 0.3)
	label_tween.tween_property(flash_label, "modulate:a", 1.0, 0.3)

	# Xóa label sau 2 giây
	await get_tree().create_timer(2.0).timeout
	if is_instance_valid(flash_label):
		flash_label.queue_free()

# Chuyển đổi trạng thái hiển thị của panel
func _toggle_panel_visibility(panel) -> void:
	if not is_instance_valid(panel):
		return

	# Lấy nút toggle
	var toggle_button = panel.get_node_or_null("ToggleButton")

	# Nếu panel đang hiển thị đầy đủ, thu gọn nó
	if panel.size.y > 30:
		# Thu gọn panel
		var tween = get_tree().create_tween()
		tween.tween_property(panel, "size", Vector2(200, 30), 0.3)

		# Ẩn các thành phần khác - thêm null check
		if panel:
			for child in panel.get_children():
				if child != toggle_button and child.name != "TitleLabel":
					child.visible = false

		# Đổi text của nút
		if toggle_button:
			toggle_button.text = "+"
	else:
		# Mở rộng panel
		var tween = get_tree().create_tween()
		tween.tween_property(panel, "size", Vector2(200, 100), 0.3)

		# Hiển thị các thành phần khác - thêm null check
		if panel:
			for child in panel.get_children():
				child.visible = true

		# Đổi text của nút
		if toggle_button:
			toggle_button.text = "X"

# Tạo các spawn points khẩn cấp nếu không có
func _create_emergency_spawn_points() -> void:
	print("Creating emergency spawn points...")

	# Tạo node cha cho front gate nếu chưa có
	var front_gate = get_node_or_null("FrontGateSpawnPoints")
	if not front_gate:
		front_gate = Node2D.new()
		front_gate.name = "FrontGateSpawnPoints"
		add_child(front_gate)
		print("Created FrontGateSpawnPoints node")

	# Tạo node cha cho back gate nếu chưa có
	var back_gate = get_node_or_null("BackGateSpawnPoints")
	if not back_gate:
		back_gate = Node2D.new()
		back_gate.name = "BackGateSpawnPoints"
		add_child(back_gate)
		print("Created BackGateSpawnPoints node")

	# Tìm các cổng để lấy vị trí tham chiếu
	var left_gate = null
	var right_gate = null

	if left_gate_path:
		left_gate = get_node_or_null(left_gate_path)

	if right_gate_path:
		right_gate = get_node_or_null(right_gate_path)

	# Nếu không tìm thấy cổng, tìm trong scene
	if not left_gate:
		left_gate = get_tree().get_first_node_in_group("left_enemy_gate")

	if not right_gate:
		right_gate = get_tree().get_first_node_in_group("right_enemy_gate")

	# Nếu vẫn không tìm thấy, tìm trong parent
	if not left_gate or not right_gate:
		var parent = get_parent()
		if parent:
			left_gate = parent.get_node_or_null("EnemyGates/LeftGate")
			right_gate = parent.get_node_or_null("EnemyGates/RightGate")

	print("Left gate found: " + str(left_gate != null))
	print("Right gate found: " + str(right_gate != null))

	# Tạo các spawn points cho front gate
	if front_gate.get_child_count() == 0:
		for i in range(2):
			var spawn_point = Marker2D.new()
			spawn_point.name = "SpawnPoint" + str(i+1)

			# Sử dụng vị trí tương đối với cổng nếu có
			if left_gate:
				spawn_point.position = Vector2(0, -26 if i == 0 else 26)
			else:
				# Vị trí mặc định nếu không tìm thấy cổng
				spawn_point.position = Vector2(0, -26 if i == 0 else 26)

			front_gate.add_child(spawn_point)
			print("Created front gate spawn point: " + spawn_point.name + " at " + str(spawn_point.position))

	# Tạo các spawn points cho back gate
	if back_gate.get_child_count() == 0:
		for i in range(2):
			var spawn_point = Marker2D.new()
			spawn_point.name = "SpawnPoint" + str(i+1)

			# Sử dụng vị trí tương đối với cổng nếu có
			if right_gate:
				spawn_point.position = Vector2(0, -26 if i == 0 else 26)
			else:
				# Vị trí mặc định nếu không tìm thấy cổng
				spawn_point.position = Vector2(0, -26 if i == 0 else 26)

			back_gate.add_child(spawn_point)
			print("Created back gate spawn point: " + spawn_point.name + " at " + str(spawn_point.position))

	# Cập nhật mảng spawn points
	front_gate_spawn_points.clear()
	back_gate_spawn_points.clear()

	# Thêm các spawn points vào mảng - thêm null check
	if front_gate and is_instance_valid(front_gate):
		for child in front_gate.get_children():
			if child is Marker2D:
				front_gate_spawn_points.append(child)
				print("Added front gate spawn point: " + child.name + " at " + str(child.global_position))

	if back_gate and is_instance_valid(back_gate):
		for child in back_gate.get_children():
			if child is Marker2D:
				back_gate_spawn_points.append(child)
				print("Added back gate spawn point: " + child.name + " at " + str(child.global_position))

	print("Updated spawn points:")
	print("Front gate: " + str(front_gate_spawn_points.size()))
	print("Back gate: " + str(back_gate_spawn_points.size()))

# Cập nhật spawn points từ scene
# Cập nhật hiển thị thời gian đếm ngược
func _update_countdown_display(time_left: float) -> void:
	var panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if not panel_layer:
		return

	var panel = panel_layer.get_node_or_null("EnemyInfoPanel")
	if not panel:
		return

	var countdown_label = panel.get_node_or_null("CountdownLabel")
	if not countdown_label:
		return

	# Định dạng thời gian thành mm:ss
	var minutes = int(time_left) / 60
	var seconds = int(time_left) % 60
	var time_str = "%02d:%02d" % [minutes, seconds]

	countdown_label.text = "Thời gian: " + time_str

	# Đổi màu khi thời gian gần hết
	if time_left <= 10:
		countdown_label.add_theme_color_override("font_color", Color(1, 0, 0))
	elif time_left <= 30:
		countdown_label.add_theme_color_override("font_color", Color(1, 0.5, 0))
	else:
		countdown_label.add_theme_color_override("font_color", Color(1, 0.7, 0.7))

# Hiển thị thời gian chờ đợt tiếp theo
func _update_wave_timer_display(minutes: int, seconds: int) -> void:
	var panel_layer = get_tree().root.get_node_or_null("EnemyInfoPanelLayer")
	if not panel_layer:
		return

	var panel = panel_layer.get_node_or_null("EnemyInfoPanel")
	if not panel:
		return

	var countdown_label = panel.get_node_or_null("CountdownLabel")
	if not countdown_label:
		return

	var time_str = "%02d:%02d" % [minutes, seconds]
	countdown_label.text = "Đợt tiếp theo: " + time_str
	countdown_label.add_theme_color_override("font_color", Color(0.7, 0.7, 1))

# Kiểm tra trạng thái của đồng minh
func _check_allies_status() -> void:
	# Tìm tất cả các đồng minh trong scene
	var allies = get_tree().get_nodes_in_group("ally_group")

	# Nếu không có đồng minh nào, coi như tất cả đã chết
	if allies.size() == 0:
		print("No allies found in the scene, mission failed!")
		allies_alive = false
		return

	# Kiểm tra xem có đồng minh nào còn sống không
	allies_alive = false
	var alive_count = 0
	var total_count = allies.size()

	for ally in allies:
		if is_instance_valid(ally):
			# Kiểm tra theo nhiều cách khác nhau vì cấu trúc đồng minh có thể khác nhau
			var is_ally_alive = false

			if ally.has_method("is_dead_check"):
				# Nếu có hàm kiểm tra
				is_ally_alive = not ally.is_dead_check()
			elif ally.has_method("get_health"):
				# Nếu có hàm lấy máu
				is_ally_alive = ally.get_health() > 0
			elif ally.get("health") != null:
				# Nếu có thuộc tính health
				is_ally_alive = ally.health > 0
			elif ally.get("is_dead") != null:
				# Nếu có thuộc tính is_dead
				is_ally_alive = not ally.is_dead
			else:
				# Mặc định coi như còn sống nếu không thể kiểm tra
				is_ally_alive = true

			if is_ally_alive:
				alive_count += 1
				allies_alive = true

	# In thông tin về trạng thái đồng minh
	if alive_count == 0 and total_count > 0:
		print("All allies are dead! Mission failed!")
	else:
		print("Allies status: " + str(alive_count) + "/" + str(total_count) + " alive")

# Xử lý thất bại nhiệm vụ
func _fail_mission() -> void:
	if not is_active:
		return

	print("Mission failed: All allies are dead!")

	# Dừng sinh kẻ địch
	stop_spawning()

	# Hiển thị thông báo thất bại
	_show_mission_failed_notification()

	# Hiển thị thông báo trên bảng thông tin
	_flash_enemy_info_panel("THẤT BẠI! Tất cả đồng minh đã hy sinh!")

	# Đánh dấu nhiệm vụ thất bại trong hệ thống nhiệm vụ nếu có
	if QuestSystem and QuestSystem.has_method("fail_quest") and QuestSystem.is_quest_active(quest_id):
		QuestSystem.fail_quest(quest_id)
	elif QuestSystem and QuestSystem.has_method("is_quest_active") and QuestSystem.is_quest_active(quest_id):
		# Nếu không có hàm fail_quest, có thể sử dụng hàm khác để xử lý
		print("QuestSystem doesn't have fail_quest method, using alternative")
		# Có thể thêm xử lý thay thế ở đây

# Hiển thị thông báo thất bại nhiệm vụ
func _show_mission_failed_notification() -> void:
	# Tạo canvas layer mới để hiển thị thông báo
	var canvas_layer = CanvasLayer.new()
	canvas_layer.layer = 100  # Hiển thị trên cùng
	get_tree().root.add_child(canvas_layer)

	# Tạo panel nền
	var panel = Panel.new()
	panel.size = Vector2(500, 250)
	panel.position = Vector2((get_viewport().size.x - 500) / 2, (get_viewport().size.y - 250) / 2)

	# Tạo style cho panel
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.1, 0.1, 0.1, 0.95)
	style_box.border_width_left = 4
	style_box.border_width_top = 4
	style_box.border_width_right = 4
	style_box.border_width_bottom = 4
	style_box.border_color = Color(1, 0, 0, 0.9)
	style_box.corner_radius_top_left = 15
	style_box.corner_radius_top_right = 15
	style_box.corner_radius_bottom_left = 15
	style_box.corner_radius_bottom_right = 15
	panel.add_theme_stylebox_override("panel", style_box)

	# Tạo tiêu đề
	var title_label = Label.new()
	title_label.text = "NHIỆM VỤ THẤT BẠI"
	title_label.position = Vector2(0, 30)
	title_label.size = Vector2(500, 50)
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 32)
	title_label.add_theme_color_override("font_color", Color(1, 0, 0))

	# Thêm hiệu ứng đổ bóng cho tiêu đề
	title_label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.7))
	title_label.add_theme_constant_override("shadow_offset_x", 2)
	title_label.add_theme_constant_override("shadow_offset_y", 2)

	# Tạo thông báo
	var message_label = Label.new()
	message_label.text = "Tất cả đồng minh đã hy sinh.\nLàng Văn Lang đã thất thủ!"
	message_label.position = Vector2(0, 100)
	message_label.size = Vector2(500, 70)
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.add_theme_font_size_override("font_size", 20)
	message_label.add_theme_color_override("font_color", Color(1, 0.8, 0.8))

	# Tạo nút đóng
	var close_button = Button.new()
	close_button.text = "Đóng"
	close_button.position = Vector2(200, 190)
	close_button.size = Vector2(100, 40)
	close_button.add_theme_font_size_override("font_size", 18)

	# Tạo style cho nút
	var button_style = StyleBoxFlat.new()
	button_style.bg_color = Color(0.7, 0.1, 0.1, 0.8)
	button_style.border_width_left = 2
	button_style.border_width_top = 2
	button_style.border_width_right = 2
	button_style.border_width_bottom = 2
	button_style.border_color = Color(1, 0.5, 0.5, 0.8)
	button_style.corner_radius_top_left = 5
	button_style.corner_radius_top_right = 5
	button_style.corner_radius_bottom_left = 5
	button_style.corner_radius_bottom_right = 5
	close_button.add_theme_stylebox_override("normal", button_style)
	close_button.add_theme_color_override("font_color", Color(1, 1, 1))
	close_button.add_theme_color_override("font_hover_color", Color(1, 0.9, 0.9))

	# Kết nối sự kiện nhấn nút
	close_button.pressed.connect(func(): canvas_layer.queue_free())

	# Thêm vào panel
	panel.add_child(title_label)
	panel.add_child(message_label)
	panel.add_child(close_button)

	# Thêm vào canvas layer
	canvas_layer.add_child(panel)

	# Hiệu ứng hiển thị
	panel.modulate.a = 0
	var tween = get_tree().create_tween()
	tween.tween_property(panel, "modulate:a", 1.0, 0.5)

	# Thêm hiệu ứng nhấp nháy cho tiêu đề
	var title_tween = get_tree().create_tween()
	title_tween.set_loops(3)
	title_tween.tween_property(title_label, "modulate", Color(1.5, 0.5, 0.5), 0.5)
	title_tween.tween_property(title_label, "modulate", Color(1, 0, 0), 0.5)

func _find_spawn_points_node(primary_name: String, fallback_names: Array) -> Node:
	"""Find spawn points node using multiple name strategies"""
	# Try primary name first
	var node = get_node_or_null(primary_name)
	if node:
		return node

	# Try fallback names
	for node_name in fallback_names:
		node = get_node_or_null(node_name)
		if node:
			print("Found spawn points node with fallback name: " + node_name)
			return node

	# Try searching in current scene
	var current_scene = get_tree().current_scene
	if current_scene:
		for node_name in [primary_name] + fallback_names:
			node = current_scene.find_child(node_name, true, false)
			if node:
				print("Found spawn points node in scene tree: " + node_name)
				return node

	return null

func _update_spawn_points_from_scene() -> void:
	print("Updating spawn points from scene...")

	# Tìm các node chứa spawn points với multiple search strategies
	var front_gate = _find_spawn_points_node("FrontGateSpawnPoints", ["FrontGate", "LeftGate", "Gate1"])
	var back_gate = _find_spawn_points_node("BackGateSpawnPoints", ["BackGate", "RightGate", "Gate2"])

	# Kiểm tra và in thông tin
	if front_gate:
		print("Found front gate spawn points node: " + front_gate.name + " with " + str(front_gate.get_child_count()) + " children")
	else:
		print("Front gate spawn points node not found")

	if back_gate:
		print("Found back gate spawn points node: " + back_gate.name + " with " + str(back_gate.get_child_count()) + " children")
	else:
		print("Back gate spawn points node not found")

	# Nếu đã có spawn points được cấu hình và không có node con, không cần cập nhật
	if front_gate_spawn_points.size() > 0 and back_gate_spawn_points.size() > 0:
		print("Spawn points already configured, skipping update")
		return

	# Nếu không tìm thấy các node, tạo mới
	if not front_gate:
		front_gate = Node2D.new()
		front_gate.name = "FrontGateSpawnPoints"
		add_child(front_gate)
		print("Created FrontGateSpawnPoints node")

	if not back_gate:
		back_gate = Node2D.new()
		back_gate.name = "BackGateSpawnPoints"
		add_child(back_gate)
		print("Created BackGateSpawnPoints node")

	# Tạo spawn points nếu chưa có
	if front_gate.get_child_count() == 0:
		for i in range(2):
			var spawn_point = Marker2D.new()
			spawn_point.name = "SpawnPoint" + str(i+1)
			spawn_point.position = Vector2(0, -26 if i == 0 else 26)
			front_gate.add_child(spawn_point)
			print("Created front gate spawn point: " + spawn_point.name + " at " + str(spawn_point.position))

	if back_gate.get_child_count() == 0:
		for i in range(2):
			var spawn_point = Marker2D.new()
			spawn_point.name = "SpawnPoint" + str(i+1)
			spawn_point.position = Vector2(0, -26 if i == 0 else 26)
			back_gate.add_child(spawn_point)
			print("Created back gate spawn point: " + spawn_point.name + " at " + str(spawn_point.position))

	# Cập nhật mảng spawn points
	front_gate_spawn_points.clear()
	back_gate_spawn_points.clear()

	# Thêm các spawn points vào mảng - thêm null check  
	if front_gate and is_instance_valid(front_gate):
		for child in front_gate.get_children():
			if child is Marker2D:
				front_gate_spawn_points.append(child)
				print("Added front gate spawn point: " + child.name + " at " + str(child.position))

	if back_gate and is_instance_valid(back_gate):
		for child in back_gate.get_children():
			if child is Marker2D:
				back_gate_spawn_points.append(child)
				print("Added back gate spawn point: " + child.name + " at " + str(child.position))

	print("Updated spawn points from scene:")
	print("Front gate: " + str(front_gate_spawn_points.size()))
	print("Back gate: " + str(back_gate_spawn_points.size()))
