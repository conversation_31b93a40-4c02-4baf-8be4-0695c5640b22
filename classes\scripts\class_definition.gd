# used to define classess like: peasant, swordman, bowman, spearman

extends Resource
class_name ClassDefinition

@export var mission: String
@export var base_health: float
@export var base_mana: float
@export var base_damage: float
@export var base_speed: float
@export var base_agility: float
@export var sprite_fames: SpriteFrames
@export var class_description: String
@export var visual_overlay: Texture2D # use to define appearances like UI
