[gd_resource type="SpriteFrames" load_steps=39 format=3 uid="uid://bhes26kmevslm"]

[ext_resource type="Texture2D" uid="uid://cvcpet5ncv5dk" path="res://assets/images/characters/enimies/linh_dich_cung.png" id="1_k4i07"]

[sub_resource type="AtlasTexture" id="AtlasTexture_050im"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_jllm8"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_vxdqg"]
atlas = ExtResource("1_k4i07")
region = Rect2(136, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_dw8os"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 340, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_eoiyl"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_67nfo"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bb7gx"]
atlas = ExtResource("1_k4i07")
region = Rect2(136, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ogll0"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_upblk"]
atlas = ExtResource("1_k4i07")
region = Rect2(272, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_e3jde"]
atlas = ExtResource("1_k4i07")
region = Rect2(340, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8to00"]
atlas = ExtResource("1_k4i07")
region = Rect2(408, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qqs7f"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ajesd"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_cwrc5"]
atlas = ExtResource("1_k4i07")
region = Rect2(136, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bc2q2"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0aiuw"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_768qo"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xq524"]
atlas = ExtResource("1_k4i07")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qak3o"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_p2y1e"]
atlas = ExtResource("1_k4i07")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_q8c46"]
atlas = ExtResource("1_k4i07")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_3bslh"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_5an4h"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_1ec5i"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bjb4q"]
atlas = ExtResource("1_k4i07")
region = Rect2(272, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_n50xl"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_erfd3"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_gjtdv"]
atlas = ExtResource("1_k4i07")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_150d2"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_1g4og"]
atlas = ExtResource("1_k4i07")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_mnhhv"]
atlas = ExtResource("1_k4i07")
region = Rect2(340, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_2kp1s"]
atlas = ExtResource("1_k4i07")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_shscr"]
atlas = ExtResource("1_k4i07")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_olhd1"]
atlas = ExtResource("1_k4i07")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_4ogs5"]
atlas = ExtResource("1_k4i07")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_4yy1p"]
atlas = ExtResource("1_k4i07")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_wbdfm"]
atlas = ExtResource("1_k4i07")
region = Rect2(340, 68, 68, 68)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_050im")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jllm8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vxdqg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dw8os")
}],
"loop": false,
"name": &"attack",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_eoiyl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_67nfo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bb7gx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ogll0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_upblk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e3jde")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8to00")
}],
"loop": true,
"name": &"die",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_qqs7f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ajesd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cwrc5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bc2q2")
}],
"loop": false,
"name": &"hurt",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_0aiuw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_768qo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xq524")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qak3o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p2y1e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q8c46")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3bslh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5an4h")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1ec5i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bjb4q")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_n50xl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_erfd3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gjtdv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_150d2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1g4og")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mnhhv")
}],
"loop": true,
"name": &"run",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2kp1s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_shscr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_olhd1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4ogs5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4yy1p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wbdfm")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}]
