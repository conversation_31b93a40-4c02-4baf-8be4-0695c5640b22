extends CanvasLayer

signal dialog_closed

@onready var npc_name_label = $DialogPanel/NPCName
@onready var dialog_text_label = $DialogPanel/DialogText
@onready var dialog_panel = $DialogPanel

var current_npc = null

func _ready():
	# Ẩn hộp thoại khi khởi động
	hide_dialog()

	# Kết nối nút đóng
	$DialogPanel/CloseButton.pressed.connect(_on_close_button_pressed)

func show_dialog(npc, npc_name, text):
	# Lưu NPC hiện tại
	current_npc = npc

	# Hiển thị tên và nội dung hội thoại
	npc_name_label.text = npc_name
	dialog_text_label.text = text

	# Hiển thị hộp thoại
	visible = true
	dialog_panel.visible = true

	print("Hiển thị hộp thoại cho: " + npc_name)

func hide_dialog():
	# Ẩn hộp thoại
	visible = false
	dialog_panel.visible = false

	# Ph<PERSON>t tín hiệu đã đóng hộp thoại
	dialog_closed.emit()
	current_npc = null

	print("Đã đóng hộp thoại")

func _on_close_button_pressed():
	print("Đã nhấn nút đóng")
	hide_dialog()
