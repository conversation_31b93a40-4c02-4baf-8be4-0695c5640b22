[gd_resource type="Resource" script_class="EnemyDefinition" load_steps=3 format=3 uid="uid://cm1o4y8yh8vok"]

[ext_resource type="Script" path="res://enemy/scripts/enemy_definition.gd" id="1_k8ygf"]
[ext_resource type="SpriteFrames" uid="uid://cswnw1ggtmrk4" path="res://enemy/skins/cam_kiem.tres" id="2_j53eg"]

[resource]
script = ExtResource("1_k8ygf")
name = "sword"
health = 100.0
damage = 10.0
speed = 5.0
agility = 10.0
detection_range = 200.0
xp_value = 15.0
sprite_frames = ExtResource("2_j53eg")
scale = 1.0
cooldown = 1.0
