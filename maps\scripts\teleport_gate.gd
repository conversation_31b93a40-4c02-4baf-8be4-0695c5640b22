# TeleportGate.gd - <PERSON><PERSON> thống cổng dịch chuyển giữa các map
extends Area2D
class_name TeleportGate

signal player_entered_gate(gate: TeleportGate)
signal player_exited_gate(gate: TeleportGate)

# ----- Exported Variables -----
@export_group("Teleport Settings")
@export_file("*.tscn") var target_scene: String = ""
@export var target_position: Vector2 = Vector2.ZERO
@export var gate_id: String = ""
@export var activation_delay: float = 0.5
@export var auto_teleport: bool = false  # Mặc định dùng phím Enter
@export var interaction_key: String = "teleport_interact"  # Phím Enter để dịch chuyển

@export_group("Visual Settings")
@export var gate_color: Color = Color(0.3, 0.7, 1.0, 0.7)
@export var activated_color: Color = Color(0.1, 1.0, 0.3, 0.9)
@export var disabled_color: Color = Color(0.5, 0.5, 0.5, 0.3)
@export var gate_size: Vector2 = Vector2(50, 100)

@export_group("Effects")
@export var enable_particles: bool = true
@export var enable_sound: bool = true
@export var enable_screen_shake: bool = true

# ----- Internal Variables -----
var _is_activated: bool = false
var _player_inside: bool = false
var _activation_timer: float = 0.0
var _current_player: Player = null

# Thêm biến tích lũy thời gian cho hiệu ứng pulse
var _pulse_time: float = 0.0

# ----- Node References -----
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var visual: ColorRect = $Visual
@onready var particles: CPUParticles2D = $TeleportParticles
@onready var activation_label: Label = $ActivationUI/ActivationLabel
@onready var activation_ui: Control = $ActivationUI
@onready var interaction_prompt: Label = $InteractionPrompt

# ----- Constants -----
const PULSE_SPEED: float = 2.0
const PULSE_INTENSITY: float = 0.3

func _ready() -> void:
	# Thêm vào group để dễ tìm kiếm
	if not is_in_group("teleport_gates"):
		add_to_group("teleport_gates")
	
	_setup_visual()
	_setup_collision()
	_setup_particles()
	_setup_ui()
	_connect_signals()
	_setup_input_map()  # Thiết lập phím Enter
	
	# Validate configuration
	if target_scene.is_empty():
		push_warning("TeleportGate '%s' has no target scene set!" % name)

func _setup_input_map() -> void:
	"""Thiết lập phím Enter cho teleport nếu chưa có"""
	if not InputMap.has_action(interaction_key):
		InputMap.add_action(interaction_key)
		var event = InputEventKey.new()
		event.keycode = KEY_ENTER
		InputMap.action_add_event(interaction_key, event)
		print("TeleportGate: Đã thêm phím Enter cho %s" % interaction_key)

func _setup_visual() -> void:
	# Tạo visual nếu chưa có
	if not visual:
		visual = ColorRect.new()
		visual.name = "Visual"
		add_child(visual)
	
	visual.size = gate_size
	visual.position = -gate_size / 2
	visual.color = gate_color
	visual.z_index = -1

func _setup_collision() -> void:
	# Tạo collision shape nếu chưa có
	if not collision_shape:
		collision_shape = CollisionShape2D.new()
		collision_shape.name = "CollisionShape2D"
		add_child(collision_shape)
	
	if not collision_shape.shape:
		var shape = RectangleShape2D.new()
		shape.size = gate_size
		collision_shape.shape = shape

func _setup_particles() -> void:
	# Remove or do not set up particles
	if particles:
		particles.emitting = false

func _setup_ui() -> void:
	# Tạo UI elements nếu chưa có
	if not activation_ui:
		activation_ui = Control.new()
		activation_ui.name = "ActivationUI"
		add_child(activation_ui)
		
		activation_label = Label.new()
		activation_label.name = "ActivationLabel"
		activation_label.text = "Activating..."
		activation_label.position = Vector2(-50, -gate_size.y/2 - 30)
		activation_label.size = Vector2(100, 20)
		activation_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		activation_ui.add_child(activation_label)
	
	if not interaction_prompt:
		interaction_prompt = Label.new()
		interaction_prompt.name = "InteractionPrompt"
		interaction_prompt.text = "Nhấn [ENTER]để dịch chuyển"  # Text hiển thị phím ENTER
		interaction_prompt.position = Vector2(-60, gate_size.y/2 + 10)
		interaction_prompt.size = Vector2(120, 20)
		interaction_prompt.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		add_child(interaction_prompt)
	
	activation_ui.visible = false
	interaction_prompt.visible = false

func _connect_signals() -> void:
	if not body_entered.is_connected(_on_body_entered):
		body_entered.connect(_on_body_entered)
	if not body_exited.is_connected(_on_body_exited):
		body_exited.connect(_on_body_exited)

func _process(delta: float) -> void:
	_pulse_time += delta
	_update_visual_effects(delta)
	_handle_activation_timer(delta)
	_handle_input()

func _update_visual_effects(_delta: float) -> void:
	if not visual:
		return
	
	# Pulsing effect
	var pulse = sin(_pulse_time * PULSE_SPEED) * PULSE_INTENSITY + 1.0
	visual.modulate.a = pulse * 0.7
	
	# Color based on state
	if _player_inside:
		visual.color = activated_color
	else:
		visual.color = gate_color

func _handle_activation_timer(delta: float) -> void:
	if not _player_inside or _is_activated:
		return
		
	if auto_teleport:
		_activation_timer += delta
		
		if activation_ui:
			activation_ui.visible = true
			if activation_label:
				var progress = (_activation_timer / activation_delay) * 100
				activation_label.text = "Teleporting... %.0f%%" % progress
		
		if _activation_timer >= activation_delay:
			_activate_teleport()
	else:
		if interaction_prompt:
			interaction_prompt.visible = true

func _handle_input() -> void:
	if not _player_inside or auto_teleport:
		print("[DEBUG] Not handling input: _player_inside=%s, auto_teleport=%s" % [_player_inside, auto_teleport])
		return
	if Input.is_action_just_pressed(interaction_key):
		print("Enter key pressed, activating teleport!")
		_activate_teleport()
	else:
		print("[DEBUG] Enter key NOT detected. interaction_key=%s" % interaction_key)

func _on_body_entered(body: Node2D) -> void:
	print("Body entered: %s, type: %s, is Player: %s" % [body.name, typeof(body), body is Player])
	if body is Player:
		print("Player detected in gate!")
		_current_player = body as Player
		_player_inside = true
		_activation_timer = 0.0
		print("Player entered teleport gate: %s" % name)
		# Remove or disable particles
		if particles:
			particles.emitting = false
		# Play sound
		if enable_sound:
			_play_enter_sound()
		# Emit signal
		player_entered_gate.emit(self)

func _on_body_exited(body: Node2D) -> void:
	if body is Player:
		_current_player = null
		_player_inside = false
		_activation_timer = 0.0
		
		print("Player exited teleport gate: %s" % name)
		
		# Stop particles
		if particles:
			particles.emitting = false
		
		# Hide UI
		if activation_ui:
			activation_ui.visible = false
		if interaction_prompt:
			interaction_prompt.visible = false
		
		# Emit signal
		player_exited_gate.emit(self)

func _activate_teleport() -> void:
	print("Attempting to activate teleport. Target scene: %s, _is_activated=%s, target_scene.is_empty()=%s" % [target_scene, _is_activated, target_scene.is_empty()])
	if _is_activated or target_scene.is_empty():
		print("[DEBUG] Teleport not activated: _is_activated=%s, target_scene.is_empty()=%s" % [_is_activated, target_scene.is_empty()])
		return
	
	_is_activated = true
	print("🌀 Đang kích hoạt dịch chuyển đến: %s" % target_scene)
	
	# Validate target scene exists
	if not FileAccess.file_exists(target_scene):
		push_error("⚠️ Scene đích không tồn tại: %s" % target_scene)
		_reset_activation()
		return
	
	# Hide UI
	if activation_ui:
		activation_ui.visible = false
	if interaction_prompt:
		interaction_prompt.visible = false
	
	# Save player data trước khi dịch chuyển
	if _current_player:
		# Sử dụng SceneManager để lưu spawn position
		if target_position != Vector2.ZERO and SceneManager:
			SceneManager.set_next_spawn_position(target_position)
			print("💾 Đã lưu vị trí spawn: %s" % target_position)
	
	# Visual effects
	if visual:
		visual.color = activated_color
		# Fade effect
		var tween = create_tween()
		tween.tween_property(visual, "modulate:a", 1.0, 0.3)
	
	# Particles burst
	if particles and enable_particles:
		particles.amount = 200
		particles.emitting = true
	
	# Screen shake
	if enable_screen_shake and _current_player:
		_apply_screen_shake()
	
	# Sound effect
	if enable_sound:
		_play_teleport_sound()
	
	# Show teleporting message
	_show_teleport_message()
	
	# Delay before scene change
	await get_tree().create_timer(0.8).timeout
	
	# Change scene with loading screen
	print("✨ Dịch chuyển đến scene: %s" % target_scene)
	print("🔍 Kiểm tra SceneManager: %s" % SceneManager)
	print("🔍 SceneManager type: %s" % typeof(SceneManager))
	
	# Debug check SceneManager methods
	if SceneManager:
		print("🔍 SceneManager methods:")
		for method in SceneManager.get_method_list():
			print("  - %s" % method.name)
	
	# Thử dùng SceneManager trước
	if SceneManager and SceneManager.has_method("goto_scene"):
		print("🔄 Sử dụng SceneManager với loading screen")
		# Thêm delay để đảm bảo stability
		await get_tree().process_frame
		await get_tree().process_frame
		
		# Gọi SceneManager.goto_scene() và không await nó
		# Để đảm bảo loading screen xuất hiện
		SceneManager.goto_scene(target_scene)
	else:
		# Nếu SceneManager không hoạt động, dùng loading screen thủ công
		print("🔄 Sử dụng loading screen thủ công")
		await _show_manual_loading_screen()
		get_tree().change_scene_to_file(target_scene)

func _show_teleport_message() -> void:
	"""Hiển thị thông báo dịch chuyển"""
	if not activation_label:
		return
	
	activation_label.text = "🌀 Đang dịch chuyển..."
	if activation_ui:
		activation_ui.visible = true
		activation_ui.modulate = Color.WHITE
		
		# Tween hiệu ứng
		var tween = create_tween()
		tween.tween_property(activation_ui, "modulate:a", 0.0, 0.5)

func _reset_activation() -> void:
	"""Reset trạng thái activation khi có lỗi"""
	_is_activated = false
	_activation_timer = 0.0
	
	if activation_ui:
		activation_ui.visible = false
	if interaction_prompt and _player_inside:
		interaction_prompt.visible = true

func _apply_screen_shake() -> void:
	"""Áp dụng hiệu ứng rung màn hình"""
	if not _current_player:
		return
	
	# Tìm camera để rung
	var camera = get_viewport().get_camera_2d()
	if camera:
		var original_offset = camera.offset
		var shake_tween = create_tween()
		shake_tween.set_loops(5)
		shake_tween.tween_method(_shake_camera.bind(camera, original_offset), 0.0, 1.0, 0.1)
		await shake_tween.finished
		_reset_camera(camera, original_offset)

func _shake_camera(camera: Camera2D, original_offset: Vector2, intensity: float) -> void:
	"""Hàm rung camera"""
	var shake_offset = Vector2(
		randf_range(-10, 10) * intensity,
		randf_range(-10, 10) * intensity
	)
	camera.offset = original_offset + shake_offset

func _reset_camera(camera: Camera2D, original_offset: Vector2) -> void:
	"""Reset camera về vị trí ban đầu"""
	camera.offset = original_offset

func _play_teleport_sound() -> void:
	"""Phát âm thanh dịch chuyển"""
	var audio_player = AudioStreamPlayer2D.new()
	add_child(audio_player)
	
	# TODO: Thêm file âm thanh thực tế
	# audio_player.stream = preload("res://audio/teleport_sound.ogg")
	# audio_player.play()
	
	print("🔊 Phát âm thanh dịch chuyển")
	
	# Xóa audio player sau khi phát xong
	audio_player.queue_free.call_deferred()

func _play_enter_sound() -> void:
	"""Phát âm thanh khi vào cổng"""
	print("🔊 Âm thanh vào cổng")

# ----- Utility Methods -----
func set_gate_theme(theme_name: String) -> void:
	"""Thiết lập theme cho cổng theo tên"""
	match theme_name:
		"fire":
			gate_color = Color.RED
			activated_color = Color.ORANGE_RED
		"water":
			gate_color = Color.BLUE
			activated_color = Color.CYAN
		"earth":
			gate_color = Color.BROWN
			activated_color = Color.SANDY_BROWN
		"air":
			gate_color = Color.LIGHT_BLUE
			activated_color = Color.WHITE
		_:
			gate_color = Color(0.3, 0.7, 1.0, 0.7)
			activated_color = Color(0.1, 1.0, 0.3, 0.9)
	
	if visual:
		visual.color = gate_color

# ----- Debug Methods -----
func debug_gate_info() -> void:
	"""In thông tin debug của cổng"""
	print("=== TELEPORT GATE DEBUG ===")
	print("Gate ID: %s" % gate_id)
	print("Position: %s" % global_position)
	print("Target Scene: %s" % target_scene)
	print("Target Position: %s" % target_position)
	print("Player Inside: %s" % _player_inside)
	print("Is Activated: %s" % _is_activated)
	print("Auto Teleport: %s" % auto_teleport)
	print("===========================")

# ----- Public Methods -----
func set_target_scene(scene_path: String) -> void:
	target_scene = scene_path
	
	# Validate scene exists
	if not FileAccess.file_exists(scene_path):
		push_error("Target scene does not exist: %s" % scene_path)

func set_target_position(pos: Vector2) -> void:
	target_position = pos

func enable_gate() -> void:
	set_process(true)
	monitoring = true
	monitorable = true
	if visual:
		visual.color = gate_color

func disable_gate() -> void:
	set_process(false)
	monitoring = false
	monitorable = false
	if visual:
		visual.color = disabled_color

func is_player_inside() -> bool:
	return _player_inside

func get_gate_id() -> String:
	return gate_id if not gate_id.is_empty() else str(name)

func _show_manual_loading_screen() -> void:
	"""Hiển thị loading screen thủ công khi SceneManager không khả dụng"""
	print("🔄 Hiển thị loading screen thủ công")
	
	# Tạo loading screen đơn giản
	var loading_screen = ColorRect.new()
	loading_screen.name = "ManualLoadingScreen"
	loading_screen.color = Color.BLACK
	loading_screen.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_screen.z_index = 1000
	
	# Thêm background image giống như loading screen chính thức
	var bg_texture = TextureRect.new()
	bg_texture.texture = preload("res://assets/images/background/screens/loading_screen.png") if ResourceLoader.exists("res://assets/images/background/screens/loading_screen.png") else null
	bg_texture.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	bg_texture.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	bg_texture.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_screen.add_child(bg_texture)
	
	# Thêm text loading
	var loading_label = Label.new()
	loading_label.text = "🌀 Đang dịch chuyển..."
	loading_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	loading_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	loading_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_label.add_theme_font_size_override("font_size", 32)
	loading_label.add_theme_color_override("font_color", Color.WHITE)
	loading_screen.add_child(loading_label)
	
	# Thêm progress bar giả
	var progress_container = VBoxContainer.new()
	progress_container.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	progress_container.position = Vector2(-200, 100)
	progress_container.size = Vector2(400, 50)
	
	var progress_bar = ProgressBar.new()
	progress_bar.size = Vector2(400, 30)
	progress_bar.value = 0
	progress_container.add_child(progress_bar)
	
	loading_screen.add_child(progress_container)
	
	# Thêm vào scene tree
	get_tree().root.add_child(loading_screen)
	
	# Hiệu ứng fade in
	loading_screen.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(loading_screen, "modulate:a", 1.0, 0.3)
	await tween.finished
	
	# Animate progress bar
	var progress_tween = create_tween()
	progress_tween.tween_property(progress_bar, "value", 100, 1.5)
	await progress_tween.finished
	
	# Chờ một chút để người chơi thấy loading screen
	await get_tree().create_timer(0.5).timeout
	
	# Cleanup sẽ được thực hiện khi scene thay đổi
