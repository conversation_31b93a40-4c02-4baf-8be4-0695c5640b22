[gd_resource type="SpriteFrames" load_steps=16 format=3 uid="uid://chgkb3a2x1m2o"]

[ext_resource type="Texture2D" uid="uid://rubplvdyqbql" path="res://assets/images/characters/mobs/Small Bee/Attack/Attack-Sheet.png" id="1_ojf7u"]
[ext_resource type="Texture2D" uid="uid://ckndo1mlgdvfe" path="res://assets/images/characters/mobs/Small Bee/Hit/Hit-Sheet.png" id="2_pjp3c"]
[ext_resource type="Texture2D" uid="uid://de58pnew0n53w" path="res://assets/images/characters/mobs/Small Bee/Fly/Fly-Sheet.png" id="3_jawg0"]

[sub_resource type="AtlasTexture" id="AtlasTexture_hgdfr"]
atlas = ExtResource("1_ojf7u")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_h17he"]
atlas = ExtResource("1_ojf7u")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ne10i"]
atlas = ExtResource("1_ojf7u")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_wbylu"]
atlas = ExtResource("1_ojf7u")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_6q302"]
atlas = ExtResource("2_pjp3c")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_6scau"]
atlas = ExtResource("2_pjp3c")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_kem10"]
atlas = ExtResource("2_pjp3c")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_4hgb4"]
atlas = ExtResource("2_pjp3c")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_3ruoh"]
atlas = ExtResource("3_jawg0")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_8clij"]
atlas = ExtResource("3_jawg0")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tpj3x"]
atlas = ExtResource("3_jawg0")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_x025o"]
atlas = ExtResource("3_jawg0")
region = Rect2(192, 0, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_hgdfr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h17he")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ne10i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wbylu")
}],
"loop": false,
"name": &"attack",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_6q302")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6scau")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kem10")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4hgb4")
}],
"loop": false,
"name": &"hit",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3ruoh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8clij")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tpj3x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x025o")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}]
