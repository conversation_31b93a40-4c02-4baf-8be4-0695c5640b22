[gd_resource type="SpriteFrames" load_steps=57 format=3 uid="uid://dq8aesv2rdpw3"]

[ext_resource type="Texture2D" uid="uid://cr3oc4t4f1xse" path="res://assets/images/characters/players/sheets/weapons/chuyen_dong_cam_kiem_nvc.png" id="1_niwq3"]
[ext_resource type="Texture2D" uid="uid://c7wmexbk6qwix" path="res://assets/images/characters/players/sheets/weapons/tan_cong_kiem_nvc.png" id="2_d1fsd"]

[sub_resource type="AtlasTexture" id="AtlasTexture_otfya"]
atlas = ExtResource("1_niwq3")
region = Rect2(0, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_jp3b7"]
atlas = ExtResource("1_niwq3")
region = Rect2(68, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_jhs12"]
atlas = ExtResource("1_niwq3")
region = Rect2(136, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_73avo"]
atlas = ExtResource("1_niwq3")
region = Rect2(204, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_a57rb"]
atlas = ExtResource("1_niwq3")
region = Rect2(272, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_7f33k"]
atlas = ExtResource("1_niwq3")
region = Rect2(340, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_lo5ov"]
atlas = ExtResource("1_niwq3")
region = Rect2(408, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ohnrj"]
atlas = ExtResource("1_niwq3")
region = Rect2(0, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_deh81"]
atlas = ExtResource("1_niwq3")
region = Rect2(68, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_rnmll"]
atlas = ExtResource("1_niwq3")
region = Rect2(136, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8sden"]
atlas = ExtResource("1_niwq3")
region = Rect2(204, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_evdt4"]
atlas = ExtResource("1_niwq3")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_1pf5e"]
atlas = ExtResource("1_niwq3")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bawuy"]
atlas = ExtResource("1_niwq3")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_07eji"]
atlas = ExtResource("1_niwq3")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_rutq1"]
atlas = ExtResource("1_niwq3")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_nvu0e"]
atlas = ExtResource("1_niwq3")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_fjmck"]
atlas = ExtResource("1_niwq3")
region = Rect2(136, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_b57l3"]
atlas = ExtResource("1_niwq3")
region = Rect2(204, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_tf5cy"]
atlas = ExtResource("1_niwq3")
region = Rect2(0, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_vk180"]
atlas = ExtResource("1_niwq3")
region = Rect2(68, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_cthqm"]
atlas = ExtResource("1_niwq3")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_b7pvw"]
atlas = ExtResource("1_niwq3")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6630k"]
atlas = ExtResource("1_niwq3")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_w2w5y"]
atlas = ExtResource("1_niwq3")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ogxom"]
atlas = ExtResource("1_niwq3")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_rhxvx"]
atlas = ExtResource("1_niwq3")
region = Rect2(340, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_2q1kv"]
atlas = ExtResource("2_d1fsd")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_wi6ot"]
atlas = ExtResource("2_d1fsd")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_idl03"]
atlas = ExtResource("2_d1fsd")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_y6uj5"]
atlas = ExtResource("2_d1fsd")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_prrm4"]
atlas = ExtResource("2_d1fsd")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6h48b"]
atlas = ExtResource("2_d1fsd")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_h5f2t"]
atlas = ExtResource("2_d1fsd")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_jmrfm"]
atlas = ExtResource("2_d1fsd")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_hprg8"]
atlas = ExtResource("2_d1fsd")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6pl7l"]
atlas = ExtResource("2_d1fsd")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_gt6aw"]
atlas = ExtResource("2_d1fsd")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_38b54"]
atlas = ExtResource("2_d1fsd")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_b4m34"]
atlas = ExtResource("2_d1fsd")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xlmg8"]
atlas = ExtResource("2_d1fsd")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_wclll"]
atlas = ExtResource("2_d1fsd")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_v6muo"]
atlas = ExtResource("2_d1fsd")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_uddtv"]
atlas = ExtResource("2_d1fsd")
region = Rect2(0, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_e0hsb"]
atlas = ExtResource("2_d1fsd")
region = Rect2(68, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_hitta"]
atlas = ExtResource("2_d1fsd")
region = Rect2(136, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ldgrs"]
atlas = ExtResource("2_d1fsd")
region = Rect2(204, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xb3rt"]
atlas = ExtResource("2_d1fsd")
region = Rect2(272, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bmifr"]
atlas = ExtResource("1_niwq3")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_tekhv"]
atlas = ExtResource("1_niwq3")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_4sxh7"]
atlas = ExtResource("1_niwq3")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_s0pxe"]
atlas = ExtResource("1_niwq3")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_lyom2"]
atlas = ExtResource("1_niwq3")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_udknp"]
atlas = ExtResource("1_niwq3")
region = Rect2(340, 68, 68, 68)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_otfya")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jp3b7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jhs12")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_73avo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a57rb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7f33k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lo5ov")
}],
"loop": false,
"name": &"die",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ohnrj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_deh81")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rnmll")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8sden")
}],
"loop": false,
"name": &"hurt",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_evdt4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1pf5e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bawuy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_07eji")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rutq1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nvu0e")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fjmck")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b57l3")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_tf5cy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vk180")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cthqm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b7pvw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6630k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w2w5y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ogxom")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rhxvx")
}],
"loop": true,
"name": &"running",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2q1kv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wi6ot")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_idl03")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y6uj5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_prrm4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6h48b")
}],
"loop": false,
"name": &"slash_1",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_h5f2t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jmrfm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hprg8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6pl7l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gt6aw")
}],
"loop": false,
"name": &"slash_2",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_38b54")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b4m34")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xlmg8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wclll")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v6muo")
}],
"loop": false,
"name": &"slash_3",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_uddtv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e0hsb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hitta")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ldgrs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xb3rt")
}],
"loop": false,
"name": &"slash_4",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_bmifr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tekhv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4sxh7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s0pxe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lyom2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_udknp")
}],
"loop": true,
"name": &"walking",
"speed": 10.0
}]
