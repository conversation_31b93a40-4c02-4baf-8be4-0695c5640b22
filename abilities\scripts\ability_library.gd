# In MissionSystem.gd (or in a separate AbilityLibrary.gd)
extends Node

var mission_abilities: Dictionary = {
	"Swordman": {
		"Attack 1": preload("res://abilities/resources/swordman/attack_1.tres"),
		"Attack 2": preload("res://abilities/resources/swordman/attack_2.tres"),
		"Attack 3": preload("res://abilities/resources/swordman/attack_3.tres"),
		"Attack 4": preload("res://abilities/resources/swordman/attack_4.tres")
	},
	"Spearman": {
		"Attack 1": preload("res://abilities/resources/spearman/attack_1.tres"),
		"Attack 2": preload("res://abilities/resources/spearman/attack_2.tres"),
		"Attack 3": preload("res://abilities/resources/spearman/attack_3.tres"),
		"Attack 4": preload("res://abilities/resources/spearman/attack_4.tres")
	},
	"Bowman": {
		"Attack 1": preload("res://abilities/resources/bowman/shoot.tres"),
	},
	# Thêm Peasant để tr<PERSON>h trả về từ điển rỗng
	"Peasant": {
		"Attack 1": preload("res://abilities/resources/swordman/attack_1.tres"),
	}
}

func get_mission_ability_set(mission_name: String) -> Dictionary:
	if mission_abilities.has(mission_name):
		return mission_abilities[mission_name]
	else:
		return {}
