[gd_resource type="SpriteFrames" load_steps=37 format=3 uid="uid://cssgn2yjxnicb"]

[ext_resource type="Texture2D" uid="uid://ifr03pd2cih1" path="res://assets/images/characters/mobs/Snail/Dead-Sheet.png" id="1_7lmkm"]
[ext_resource type="Texture2D" uid="uid://dj8wfp50srpfn" path="res://assets/images/characters/mobs/Snail/Hide-Sheet.png" id="2_pgefc"]
[ext_resource type="Texture2D" uid="uid://lv1msjdtpyko" path="res://assets/images/characters/mobs/Snail/all.png" id="3_5lpp3"]
[ext_resource type="Texture2D" uid="uid://dsq83re2210oh" path="res://assets/images/characters/mobs/Snail/walk-Sheet.png" id="4_qrxpb"]

[sub_resource type="AtlasTexture" id="AtlasTexture_1ui3x"]
atlas = ExtResource("1_7lmkm")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vk5fr"]
atlas = ExtResource("1_7lmkm")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_56864"]
atlas = ExtResource("1_7lmkm")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_rnbhk"]
atlas = ExtResource("1_7lmkm")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_m07fa"]
atlas = ExtResource("1_7lmkm")
region = Rect2(192, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_sxq7v"]
atlas = ExtResource("1_7lmkm")
region = Rect2(240, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_xqe45"]
atlas = ExtResource("1_7lmkm")
region = Rect2(288, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5k5ph"]
atlas = ExtResource("1_7lmkm")
region = Rect2(336, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4cxdb"]
atlas = ExtResource("2_pgefc")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6uj6l"]
atlas = ExtResource("2_pgefc")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_v7oum"]
atlas = ExtResource("2_pgefc")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_qi4lr"]
atlas = ExtResource("2_pgefc")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_a1kp3"]
atlas = ExtResource("2_pgefc")
region = Rect2(192, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_un632"]
atlas = ExtResource("2_pgefc")
region = Rect2(240, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_x0fh0"]
atlas = ExtResource("2_pgefc")
region = Rect2(288, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_cnrim"]
atlas = ExtResource("2_pgefc")
region = Rect2(336, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_nwabd"]
atlas = ExtResource("3_5lpp3")
region = Rect2(0, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3pcd0"]
atlas = ExtResource("3_5lpp3")
region = Rect2(48, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_wqjs1"]
atlas = ExtResource("3_5lpp3")
region = Rect2(96, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6r7ml"]
atlas = ExtResource("3_5lpp3")
region = Rect2(144, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_o8f7j"]
atlas = ExtResource("3_5lpp3")
region = Rect2(192, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_s37mg"]
atlas = ExtResource("3_5lpp3")
region = Rect2(240, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_wfcvr"]
atlas = ExtResource("3_5lpp3")
region = Rect2(288, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5k34i"]
atlas = ExtResource("3_5lpp3")
region = Rect2(336, 64, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mty4k"]
atlas = ExtResource("4_qrxpb")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_rswth"]
atlas = ExtResource("4_qrxpb")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_dcs3a"]
atlas = ExtResource("4_qrxpb")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_duaxx"]
atlas = ExtResource("4_qrxpb")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_h0vnp"]
atlas = ExtResource("4_qrxpb")
region = Rect2(192, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_04nxo"]
atlas = ExtResource("4_qrxpb")
region = Rect2(240, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_pvn7k"]
atlas = ExtResource("4_qrxpb")
region = Rect2(288, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_xv87f"]
atlas = ExtResource("4_qrxpb")
region = Rect2(336, 0, 48, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1ui3x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vk5fr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_56864")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rnbhk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m07fa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sxq7v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xqe45")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5k5ph")
}],
"loop": false,
"name": &"dead",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4cxdb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6uj6l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v7oum")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qi4lr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a1kp3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_un632")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x0fh0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cnrim")
}],
"loop": false,
"name": &"hide",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_nwabd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3pcd0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wqjs1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6r7ml")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o8f7j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s37mg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wfcvr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5k34i")
}],
"loop": false,
"name": &"unhide",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mty4k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rswth")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dcs3a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_duaxx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h0vnp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_04nxo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pvn7k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xv87f")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}]
