extends Node
class_name ClassManager

const class_map: Dictionary = {
	"sword": preload("res://enemy/resources/cam_kiem.tres"),
	"spear": preload("res://enemy/resources/cam_thuong.tres"),
	"bow": preload("res://enemy/resources/cam_cung.tres"),
	"boss": preload("res://enemy/resources/boss.tres")
}

func get_current_class(current_class: String):
	return class_map[current_class]

func update_class(enemy: Enemy, definition: String):
	print("ClassManager: Updating enemy class to " + definition)

	if not class_map.has(definition):
		print("ERROR: Enemy type '" + definition + "' not found in class_map!")
		print("Available types: " + str(class_map.keys()))
		# Sử dụng loại mặc định nếu không tìm thấy
		definition = "sword"
		print("Using default type: " + definition)

	var tres = class_map.get(definition)
	if tres:
		print("Found class definition: " + str(tres))
		enemy.adapt_to_class(tres)
	else:
		print("ERROR: Failed to get class definition for " + definition)
