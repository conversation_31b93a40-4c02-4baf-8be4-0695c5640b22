extends Node2D

func _ready() -> void:
	print("Ally Test Scene loaded")
	print("Testing ally following behavior")
	
	# Get references to the player and ally
	var player = $Player
	var ally = $Ally
	
	if player and ally:
		print("Player and ally found")
		print("Player collision layer: ", player.collision_layer)
		print("Ally tracking zone collision mask: ", ally.tracking_zone.collision_mask)
		print("Ally stop tracking zone collision mask: ", ally.stop_tracking_zone.collision_mask)
	else:
		print("Player or ally not found!")
