extends Node
class_name AbilityManager

# The current set of abilities loaded from the mission (key -> AbilityDefinition resource).
var ability_set: Dictionary = {}

# To track the current ability used in the combo chain.
var current_combo_ability: AbilityDefinition = null
var combo_timer: Timer
var combo_window: float = 2.0  # seconds to chain abilities

# To manage individual ability cooldown timers (ability_key -> Timer)
var cooldown_timers: Dictionary = {}

# Tham chiếu đến người chơi
var player: Player

func _ready() -> void:
	# Setup combo-window timer
	combo_timer = Timer.new()
	combo_timer.wait_time = combo_window
	combo_timer.one_shot = true
	add_child(combo_timer)
	# Godot 4.3: connect via Signal property; no binds
	combo_timer.timeout.connect(Callable(self, "_on_combo_timeout"))

	# <PERSON><PERSON><PERSON> b<PERSON><PERSON> lấy tham chiếu đến người chơi
	await get_tree().process_frame
	player = get_parent() as Player

func set_ability_set(new_set: Dictionary) -> void:
	# Tear down old cooldown timers
	for t in cooldown_timers.values():
		if is_instance_valid(t) and t.get_parent() == self:
			t.queue_free()
	cooldown_timers.clear()

	ability_set = new_set

	# Create a cooldown timer for each ability
	for key in ability_set.keys():
		var ability: AbilityDefinition = ability_set[key]
		var timer = Timer.new()
		timer.one_shot = true
		timer.wait_time = ability.cooldown
		add_child(timer)
		# Godot 4.3: use named binds parameter
		timer.timeout.connect(Callable(self, "_on_cooldown_timeout").bind(key))
		cooldown_timers[key] = timer

func perform_attack(ability_key: String) -> void:
	if not ability_set.has(ability_key):
		print("Ability '%s' not found" % ability_key)
		return

	var timer: Timer = cooldown_timers.get(ability_key) as Timer

	# Kiểm tra God Mode - bỏ qua cooldown nếu đang trong God Mode
	var is_in_god_mode = is_instance_valid(player) and player.is_god_mode
	if timer and not timer.is_stopped() and not is_in_god_mode:
		print("'%s' on cooldown: %.2f s left" % [ability_key, timer.time_left])
		return

	var ability: AbilityDefinition = ability_set[ability_key]

	# Kiểm tra mana - bỏ qua nếu đang trong God Mode
	if ability.mana_cost > 0 and not is_in_god_mode:
		if is_instance_valid(player) and player.mana < ability.mana_cost:
			print("Not enough mana for '%s'" % ability.ability_name)
			return

	_execute_ability(ability)

	# Chỉ bắt đầu cooldown nếu không ở trong God Mode
	if timer and not is_in_god_mode:
		timer.start()

func _execute_ability(ability: AbilityDefinition) -> void:
	if is_instance_valid(player) and player.has_method("play_animation"):
		player.play_animation(ability.animation_name)

	var dmg = ability.base_damage
	if is_instance_valid(player):
		dmg *= player.damage

	# Tiêu hao mana nếu không ở trong God Mode
	var is_in_god_mode = is_instance_valid(player) and player.is_god_mode
	if not is_in_god_mode and ability.mana_cost > 0 and is_instance_valid(player):
		player.mana -= ability.mana_cost

	print("Executing '%s' with damage %d" % [ability.ability_name, dmg])

	current_combo_ability = ability
	combo_timer.start()

func handle_combo_input(fallback_key: String) -> void:
	if current_combo_ability and current_combo_ability.chain_options.size() > 0:
		perform_attack(current_combo_ability.chain_options[0])
	else:
		perform_attack(fallback_key)

func get_next_attack(current_index: int) -> String:
	if current_combo_ability and current_index + 1 < current_combo_ability.chain_options.size():
		return current_combo_ability.chain_options[current_index + 1]
	return ""

func _on_combo_timeout() -> void:
	current_combo_ability = null

func _on_cooldown_timeout(ability_key: String) -> void:
	print("Ability '%s' is ready again" % ability_key)
