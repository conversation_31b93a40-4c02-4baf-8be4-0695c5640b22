# Emergency Loading Screen Fix - Resolves stuck loading screens
extends Node

var fix_timer: Timer = null

func _ready():
	print("🚨 Emergency Loading Fix initialized")
	
	# Start monitoring for stuck loading screens
	start_monitoring()

func start_monitoring():
	"""Start monitoring for stuck loading screens"""
	if fix_timer:
		fix_timer.queue_free()
	
	fix_timer = Timer.new()
	fix_timer.wait_time = 3.0  # Check every 3 seconds
	fix_timer.timeout.connect(_check_for_stuck_loading)
	fix_timer.autostart = true
	add_child(fix_timer)
	
	print("🔍 Started monitoring for stuck loading screens")

func _check_for_stuck_loading():
	"""Check for stuck loading screens and fix them"""
	var loading_screens = find_loading_screens()
	
	for loading_screen in loading_screens:
		if is_loading_screen_stuck(loading_screen):
			print("🚨 Stuck loading screen detected, applying emergency fix...")
			fix_stuck_loading_screen(loading_screen)

func find_loading_screens() -> Array:
	"""Find all loading screen instances in the scene tree"""
	var loading_screens = []
	var root = get_tree().root
	
	# Look for loading screen nodes
	var all_nodes = get_all_children(root)
	for node in all_nodes:
		if is_loading_screen_node(node):
			loading_screens.append(node)
	
	return loading_screens

func get_all_children(node: Node) -> Array:
	"""Recursively get all children of a node"""
	var children = []
	children.append(node)
	
	for child in node.get_children():
		children.append_array(get_all_children(child))
	
	return children

func is_loading_screen_node(node: Node) -> bool:
	"""Check if a node is a loading screen"""
	if not node:
		return false
	
	# Check by name patterns
	var node_name = node.name.to_lower()
	if "loading" in node_name:
		return true
	
	# Check by script path
	var script = node.get_script()
	if script:
		var script_path = script.resource_path
		if "loading" in script_path.to_lower():
			return true
	
	# Check for progress bar child (common in loading screens)
	var progress_bar = node.find_child("ProgressBar", true, false)
	if progress_bar:
		return true
	
	return false

func is_loading_screen_stuck(loading_screen: Node) -> bool:
	"""Check if a loading screen is stuck"""
	if not loading_screen or not is_instance_valid(loading_screen):
		return false
	
	# Check if progress bar is at 100%
	var progress_bar = loading_screen.find_child("ProgressBar", true, false)
	if progress_bar and progress_bar.value >= 100.0:
		print("🔍 Found loading screen at 100%: %s" % loading_screen.name)
		return true
	
	# Check if loading screen has been visible for too long
	if loading_screen.has_method("get_loading_start_time"):
		var start_time = loading_screen.get_loading_start_time()
		var current_time = Time.get_ticks_msec() / 1000.0
		var elapsed = current_time - start_time
		
		if elapsed > 10.0:  # More than 10 seconds
			print("🔍 Found loading screen running too long: %.1fs" % elapsed)
			return true
	
	return false

func fix_stuck_loading_screen(loading_screen: Node):
	"""Apply emergency fix to stuck loading screen"""
	print("🔧 Applying emergency fix to loading screen: %s" % loading_screen.name)
	
	# Method 1: Try to force completion using built-in methods
	if loading_screen.has_method("force_completion"):
		print("🎯 Using force_completion method")
		loading_screen.force_completion()
		return
	
	# Method 2: Try to trigger completion manually
	if loading_screen.has_method("_handle_completion"):
		print("🎯 Using _handle_completion method")
		loading_screen.call_deferred("_handle_completion")
		return
	
	# Method 3: Try to set target scene and force transition
	if loading_screen.has_method("set_target_scene") and loading_screen.has_method("_transition_to_target_scene"):
		print("🎯 Setting target scene and forcing transition")
		loading_screen.set_target_scene("res://Home/scenes/Startmenu.tscn")
		loading_screen.call_deferred("_transition_to_target_scene")
		return
	
	# Method 4: Direct scene change as last resort
	print("🚨 Using direct scene change as last resort")
	apply_direct_scene_change(loading_screen)

func apply_direct_scene_change(loading_screen: Node):
	"""Apply direct scene change as emergency measure"""
	print("🚨 Applying direct scene change...")
	
	# Try to determine target scene
	var target_scene = "res://Home/scenes/Startmenu.tscn"
	
	if loading_screen.has_method("get_target_scene"):
		var custom_target = loading_screen.get_target_scene()
		if custom_target != "" and ResourceLoader.exists(custom_target):
			target_scene = custom_target
	
	# Remove loading screen
	if is_instance_valid(loading_screen):
		loading_screen.queue_free()
	
	# Wait a frame then change scene
	await get_tree().process_frame
	
	print("🎬 Emergency scene change to: %s" % target_scene)
	get_tree().change_scene_to_file(target_scene)

# Manual fix functions that can be called from console or other scripts
func force_fix_all_loading_screens():
	"""Manually force fix all loading screens"""
	print("🚨 MANUAL FIX: Forcing fix for all loading screens")
	
	var loading_screens = find_loading_screens()
	print("🔍 Found %d loading screen(s)" % loading_screens.size())
	
	for loading_screen in loading_screens:
		fix_stuck_loading_screen(loading_screen)

func emergency_goto_main_menu():
	"""Emergency function to go to main menu"""
	print("🚨 EMERGENCY: Going to main menu")
	
	# Remove all loading screens
	var loading_screens = find_loading_screens()
	for loading_screen in loading_screens:
		if is_instance_valid(loading_screen):
			loading_screen.queue_free()
	
	# Wait a frame then go to main menu
	await get_tree().process_frame
	get_tree().change_scene_to_file("res://Home/scenes/Startmenu.tscn")

func emergency_goto_scene(scene_path: String):
	"""Emergency function to go to specific scene"""
	print("🚨 EMERGENCY: Going to scene: %s" % scene_path)
	
	if not ResourceLoader.exists(scene_path):
		print("❌ Scene does not exist: %s" % scene_path)
		emergency_goto_main_menu()
		return
	
	# Remove all loading screens
	var loading_screens = find_loading_screens()
	for loading_screen in loading_screens:
		if is_instance_valid(loading_screen):
			loading_screen.queue_free()
	
	# Wait a frame then go to scene
	await get_tree().process_frame
	get_tree().change_scene_to_file(scene_path)

# Debug functions
func debug_loading_screens():
	"""Debug function to print info about all loading screens"""
	print("🔍 DEBUG: Loading screen information")
	print("="*50)
	
	var loading_screens = find_loading_screens()
	print("Found %d loading screen(s):" % loading_screens.size())
	
	for i in range(loading_screens.size()):
		var loading_screen = loading_screens[i]
		print("\n%d. %s" % [i+1, loading_screen.name])
		print("   Path: %s" % loading_screen.get_path())
		print("   Valid: %s" % is_instance_valid(loading_screen))
		print("   Stuck: %s" % is_loading_screen_stuck(loading_screen))
		
		var progress_bar = loading_screen.find_child("ProgressBar", true, false)
		if progress_bar:
			print("   Progress: %.1f%%" % progress_bar.value)
		
		if loading_screen.has_method("debug_loading_state"):
			loading_screen.debug_loading_state()

# Console commands for easy access
func _input(event):
	if event is InputEventKey and event.pressed:
		# Ctrl+Shift+F = Force fix all loading screens
		if event.keycode == KEY_F and event.ctrl_pressed and event.shift_pressed:
			force_fix_all_loading_screens()
		
		# Ctrl+Shift+M = Emergency goto main menu
		elif event.keycode == KEY_M and event.ctrl_pressed and event.shift_pressed:
			emergency_goto_main_menu()
		
		# Ctrl+Shift+D = Debug loading screens
		elif event.keycode == KEY_D and event.ctrl_pressed and event.shift_pressed:
			debug_loading_screens()
