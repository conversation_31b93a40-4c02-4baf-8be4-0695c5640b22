extends Node

var player: Player = null
var rank_order: Array[String] = ["Begin<PERSON>", "Starter", "Experienced Player", "Pro Player", "Game Master"]
var ranks: Dictionary = {
	"Beginner": preload("res://ranks/resources/beginner_resource.tres"),
	"Starter": preload("res://ranks/resources/starter_resource.tres"),
	"Experienced Player": preload("res://ranks/resources/experienced_player_resource.tres"),
	"Pro Player": preload("res://ranks/resources/pro_player_resource.tres"),
	"Game Master": preload("res://ranks/resources/master_resource.tres")
}

func set_up_player(player_custom: Player) -> void:
	player = player_custom

	var title = get_title_information(player.rank_title)
	if title != null:
		player.rank_up(title)
	else:
		# Nếu không tìm thấy rank, sử dụng rank mặc định (Beginner)
		var default_title = get_title_information("Beginner")
		if default_title != null:
			player.rank_up(default_title)

func check_for_rank_up() -> void:
	var next_rank_title = get_next_rank(player.rank_title)

	# Ki<PERSON><PERSON> tra nếu không có rank tiếp theo
	if next_rank_title == null:
		return

	var next_rank = get_title_information(next_rank_title)
	if next_rank == null:
		return

	var next_rank_xp = next_rank.xp_to_lose
	var next_rank_lvl = next_rank.level_to_reach

	if player.xp >= next_rank_xp && player.level >= next_rank_lvl:
		print("Player has ranked up!")
		player.rank_up(next_rank)

func get_next_rank(current_rank: String):
	var index = rank_order.find(current_rank)
	if index == -1 or index == rank_order.size() - 1:
		return null  # no next rank
	else:
		return rank_order[index + 1]

func get_title_information(title: String) -> RankDefinition:
	return ranks.get(title)
