# Startup Diagnostic - Debug loading screen freeze issues
extends Node

var diagnostic_log: Array = []
var start_time: float = 0.0

func _ready():
	start_time = Time.get_ticks_msec() / 1000.0
	print("🔍 Startup Diagnostic initialized")
	call_deferred("run_startup_diagnostic")

func run_startup_diagnostic():
	"""Run comprehensive startup diagnostic"""
	print("\n" + "="*50)
	print("🔍 STARTUP DIAGNOSTIC REPORT")
	print("="*50)
	
	# Check 1: Autoload Systems
	check_autoload_systems()
	
	# Check 2: Scene State
	check_scene_state()
	
	# Check 3: UI Systems
	check_ui_systems()
	
	# Check 4: Performance Metrics
	check_performance_metrics()
	
	# Check 5: Resource Loading
	check_resource_loading()
	
	# Print summary
	print_diagnostic_summary()

func check_autoload_systems():
	"""Check status of all autoload systems"""
	print("\n🔧 AUTOLOAD SYSTEMS CHECK:")
	
	var autoload_systems = [
		"XPManager", "RankingSystem", "MissionSystem", "AbilityLibrary",
		"SceneManager", "PlayerSpawnManager", "QuestSystem", 
		"DamageEffects", "GlobalMapNameUI", "TeleportPositionMapping"
	]
	
	var available_count = 0
	var total_count = autoload_systems.size()
	
	for system_name in autoload_systems:
		var system = get_node_or_null("/root/" + system_name)
		if system:
			available_count += 1
			print("  ✅ %s - Available" % system_name)
			diagnostic_log.append("AUTOLOAD_OK: " + system_name)
		else:
			print("  ❌ %s - Missing" % system_name)
			diagnostic_log.append("AUTOLOAD_MISSING: " + system_name)
	
	print("  📊 Autoload Status: %d/%d available" % [available_count, total_count])

func check_scene_state():
	"""Check current scene state"""
	print("\n🎬 SCENE STATE CHECK:")
	
	var current_scene = get_tree().current_scene
	if current_scene:
		print("  ✅ Current Scene: %s" % current_scene.name)
		print("  📁 Scene File: %s" % current_scene.scene_file_path)
		diagnostic_log.append("SCENE_OK: " + current_scene.name)
	else:
		print("  ❌ No current scene found")
		diagnostic_log.append("SCENE_MISSING")
	
	# Check for loading screen nodes
	var root = get_tree().root
	var loading_nodes = root.find_children("*Loading*", "", true, false)
	if loading_nodes.size() > 0:
		print("  ⏳ Loading screen nodes found: %d" % loading_nodes.size())
		for node in loading_nodes:
			print("    - %s" % node.name)
		diagnostic_log.append("LOADING_SCREEN_ACTIVE")
	else:
		print("  🎯 No loading screen nodes found")
		diagnostic_log.append("NO_LOADING_SCREEN")

func check_ui_systems():
	"""Check UI system status"""
	print("\n🎨 UI SYSTEMS CHECK:")
	
	# Check GlobalMapNameUI
	var global_map_ui = get_node_or_null("/root/GlobalMapNameUI")
	if global_map_ui:
		print("  ✅ GlobalMapNameUI - Available")
		if global_map_ui.has_method("get_current_map_name"):
			print("    🔧 Methods available")
		diagnostic_log.append("UI_GLOBAL_MAP_OK")
	else:
		print("  ❌ GlobalMapNameUI - Missing")
		diagnostic_log.append("UI_GLOBAL_MAP_MISSING")
	
	# Check for UI test scripts
	var ui_test_nodes = get_tree().get_nodes_in_group("ui_test")
	if ui_test_nodes.size() > 0:
		print("  ⚠️ UI Test nodes active: %d" % ui_test_nodes.size())
		diagnostic_log.append("UI_TEST_ACTIVE")
	else:
		print("  ✅ No UI test interference")
		diagnostic_log.append("UI_TEST_CLEAN")

func check_performance_metrics():
	"""Check performance metrics"""
	print("\n⚡ PERFORMANCE METRICS:")
	
	var current_time = Time.get_ticks_msec() / 1000.0
	var elapsed_time = current_time - start_time
	
	print("  ⏱️ Diagnostic runtime: %.2f seconds" % elapsed_time)
	print("  🧠 Memory usage: %d MB" % (OS.get_static_memory_usage_by_type().size() / 1024 / 1024))
	print("  🎯 FPS: %d" % Engine.get_frames_per_second())
	
	if elapsed_time > 5.0:
		print("  ⚠️ Slow startup detected")
		diagnostic_log.append("SLOW_STARTUP")
	else:
		print("  ✅ Startup time acceptable")
		diagnostic_log.append("STARTUP_OK")

func check_resource_loading():
	"""Check resource loading status"""
	print("\n📦 RESOURCE LOADING CHECK:")
	
	var critical_resources = [
		"res://Home/scenes/Startmenu.tscn",
		"res://ui/scripts/loading_screen.gd",
		"res://ui/scripts/global_map_name_ui.gd",
		"res://ui/scripts/enhanced_theme_manager.gd"
	]
	
	var loaded_count = 0
	var total_count = critical_resources.size()
	
	for resource_path in critical_resources:
		if ResourceLoader.exists(resource_path):
			loaded_count += 1
			print("  ✅ %s" % resource_path.get_file())
		else:
			print("  ❌ %s - Missing" % resource_path.get_file())
			diagnostic_log.append("RESOURCE_MISSING: " + resource_path)
	
	print("  📊 Resource Status: %d/%d available" % [loaded_count, total_count])

func print_diagnostic_summary():
	"""Print diagnostic summary"""
	print("\n" + "="*50)
	print("📊 DIAGNOSTIC SUMMARY")
	print("="*50)
	
	var issues = []
	var warnings = []
	var ok_count = 0
	
	for entry in diagnostic_log:
		if "MISSING" in entry or "SLOW" in entry:
			issues.append(entry)
		elif "ACTIVE" in entry and "TEST" in entry:
			warnings.append(entry)
		elif "OK" in entry:
			ok_count += 1
	
	print("✅ Systems OK: %d" % ok_count)
	print("⚠️ Warnings: %d" % warnings.size())
	print("❌ Issues: %d" % issues.size())
	
	if issues.size() > 0:
		print("\n❌ CRITICAL ISSUES:")
		for issue in issues:
			print("  - %s" % issue)
	
	if warnings.size() > 0:
		print("\n⚠️ WARNINGS:")
		for warning in warnings:
			print("  - %s" % warning)
	
	# Provide recommendations
	print("\n🎯 RECOMMENDATIONS:")
	if issues.size() == 0 and warnings.size() == 0:
		print("  🟢 System appears healthy")
	elif issues.size() > 0:
		print("  🔴 Critical issues detected - investigate missing systems")
	else:
		print("  🟡 Minor issues detected - monitor performance")
	
	print("\n✅ Startup diagnostic completed!")

# Manual diagnostic functions
func run_quick_diagnostic():
	"""Run a quick diagnostic check"""
	print("🚀 Running quick startup diagnostic...")
	check_autoload_systems()
	check_scene_state()
	print("✅ Quick diagnostic completed!")

func monitor_loading_screen():
	"""Monitor loading screen for issues"""
	print("👁️ Monitoring loading screen...")
	
	var monitor_duration = 15.0  # Monitor for 15 seconds
	var check_interval = 1.0     # Check every second
	var checks = int(monitor_duration / check_interval)
	
	for i in range(checks):
		await get_tree().create_timer(check_interval).timeout
		
		var loading_nodes = get_tree().root.find_children("*Loading*", "", true, false)
		var current_scene = get_tree().current_scene
		
		print("  [%02d] Loading nodes: %d, Scene: %s" % [
			i + 1, 
			loading_nodes.size(), 
			current_scene.name if current_scene else "None"
		])
		
		# If loading screen disappears, stop monitoring
		if loading_nodes.size() == 0:
			print("✅ Loading screen completed normally")
			return
	
	print("⚠️ Loading screen still active after %d seconds" % monitor_duration)

# Public API
func diagnose_startup_freeze():
	"""Main function to diagnose startup freeze"""
	print("🔍 Starting comprehensive startup freeze diagnosis...")
	
	# Run main diagnostic
	await run_startup_diagnostic()
	
	# Monitor loading screen
	await monitor_loading_screen()
	
	print("🎯 Startup freeze diagnosis completed!")
