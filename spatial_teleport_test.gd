# SpatialTeleportTest.gd - Test script for spatial consistency teleport system
extends Node

# 🧪 SPATIAL TELEPORT TESTING SYSTEM
# This script validates that the spatial teleportation system works correctly

var spatial_system: SpatialTeleportSystem
var test_results: Array = []

func _ready():
	print("🧪 === SPATIAL TELEPORT TESTING STARTED ===")
	spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	# Run all tests
	call_deferred("run_all_tests")

func run_all_tests():
	print("\n🔍 Running spatial consistency tests...")
	
	test_spatial_mappings()
	test_gate_positions()
	test_reciprocal_connections()
	test_spawn_logic()
	test_doi_tre_specific()
	
	print_test_results()

func test_spatial_mappings():
	"""Test that all spatial mappings are valid"""
	print("\n📍 Testing spatial position mappings...")
	
	var expected_mappings = [
		"lang_van_lang_left_exit",
		"lang_van_lang_right_exit", 
		"lang_van_lang_north_exit",
		"dong_dau_left_exit",
		"dong_dau_right_exit",
		"doi_tre_right_exit"
	]
	
	for gate_id in expected_mappings:
		var spawn_pos = spatial_system.get_spawn_position(gate_id)
		if spawn_pos != Vector2.ZERO:
			add_test_result("✅ %s has valid spawn position: %s" % [gate_id, spawn_pos])
		else:
			add_test_result("❌ %s missing spawn position" % gate_id)

func test_gate_positions():
	"""Test that all gate positions are defined"""
	print("\n🚪 Testing gate positions...")
	
	var expected_gates = [
		"lang_van_lang_left_exit",
		"dong_dau_left_exit", 
		"dong_dau_right_exit",
		"doi_tre_right_exit"
	]
	
	for gate_id in expected_gates:
		var gate_pos = spatial_system.get_gate_position(gate_id)
		if gate_pos != Vector2.ZERO:
			add_test_result("✅ %s has valid gate position: %s" % [gate_id, gate_pos])
		else:
			add_test_result("❌ %s missing gate position" % gate_id)

func test_reciprocal_connections():
	"""Test that connections are reciprocal"""
	print("\n🔄 Testing reciprocal connections...")
	
	var connection_pairs = [
		["lang_van_lang_left_exit", "dong_dau_right_exit"],
		["dong_dau_left_exit", "doi_tre_right_exit"]
	]
	
	for pair in connection_pairs:
		var gate_a = pair[0]
		var gate_b = pair[1]
		
		var pos_a = spatial_system.get_spawn_position(gate_a)
		var pos_b = spatial_system.get_spawn_position(gate_b)
		
		if pos_a != Vector2.ZERO and pos_b != Vector2.ZERO:
			add_test_result("✅ Reciprocal connection: %s ↔ %s" % [gate_a, gate_b])
		else:
			add_test_result("❌ Broken reciprocal connection: %s ↔ %s" % [gate_a, gate_b])

func test_spawn_logic():
	"""Test spatial spawn logic"""
	print("\n🎯 Testing spawn logic...")
	
	# Test LEFT exit spawns at RIGHT entrance
	var left_exit_spawn = spatial_system.get_spawn_position("lang_van_lang_left_exit")
	if left_exit_spawn.x < 0:  # Should spawn on left side of destination
		add_test_result("✅ LEFT exit spawns correctly on LEFT side of destination")
	else:
		add_test_result("❌ LEFT exit spawn logic incorrect")
	
	# Test RIGHT exit spawns at LEFT entrance  
	var right_exit_spawn = spatial_system.get_spawn_position("dong_dau_right_exit")
	if right_exit_spawn.x > 0:  # Should spawn on right side of destination
		add_test_result("✅ RIGHT exit spawns correctly on RIGHT side of destination")
	else:
		add_test_result("❌ RIGHT exit spawn logic incorrect")

func test_doi_tre_specific():
	"""Test Doi Tre specific implementation"""
	print("\n🎋 Testing Doi Tre specific implementation...")
	
	# Test Doi Tre right exit
	var doi_tre_gate = "doi_tre_right_exit"
	var spawn_pos = spatial_system.get_spawn_position(doi_tre_gate)
	var gate_pos = spatial_system.get_gate_position(doi_tre_gate)
	
	if spawn_pos != Vector2.ZERO:
		add_test_result("✅ Doi Tre right exit has spawn position: %s" % spawn_pos)
		
		# Should spawn on LEFT side of Dong Dau (negative X)
		if spawn_pos.x < 0:
			add_test_result("✅ Doi Tre → Dong Dau spawns on LEFT side (correct spatial logic)")
		else:
			add_test_result("❌ Doi Tre → Dong Dau spawn position incorrect")
	else:
		add_test_result("❌ Doi Tre right exit missing spawn position")
	
	if gate_pos != Vector2.ZERO:
		add_test_result("✅ Doi Tre right exit has gate position: %s" % gate_pos)
	else:
		add_test_result("❌ Doi Tre right exit missing gate position")

func add_test_result(result: String):
	"""Add a test result to the results array"""
	test_results.append(result)
	print("  " + result)

func print_test_results():
	"""Print summary of all test results"""
	print("\n📊 === TEST RESULTS SUMMARY ===")
	
	var passed = 0
	var failed = 0
	
	for result in test_results:
		if result.begins_with("✅"):
			passed += 1
		elif result.begins_with("❌"):
			failed += 1
	
	print("✅ Tests Passed: %d" % passed)
	print("❌ Tests Failed: %d" % failed)
	print("📊 Total Tests: %d" % test_results.size())
	
	if failed == 0:
		print("🎉 ALL TESTS PASSED! Spatial consistency system is working correctly.")
	else:
		print("⚠️ Some tests failed. Please review the spatial consistency implementation.")
	
	print("\n🗺️ SPATIAL WORLD LAYOUT VERIFICATION:")
	print("Doi Tre (LEFT) ←→ Dong Dau (CENTER) ←→ Lang Van Lang (HUB)")
	print("Expected flow: Doi Tre RIGHT exit → Dong Dau LEFT entrance")
	print("Expected flow: Dong Dau RIGHT exit → Lang Van Lang LEFT entrance")

# 🎮 INPUT TESTING (for manual testing in game)
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running spatial tests...")
		test_results.clear()
		run_all_tests()
	
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n📋 Printing spatial mappings...")
		spatial_system.print_all_mappings()
		spatial_system.print_gate_positions()

# 🛠️ UTILITY FUNCTIONS FOR INTEGRATION
func validate_teleport_gate_file(file_path: String) -> bool:
	"""Validate that a teleport gate file uses the new spatial naming"""
	if not FileAccess.file_exists(file_path):
		print("❌ File not found: %s" % file_path)
		return false
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	var content = file.get_as_text()
	file.close()
	
	# Check for new spatial naming patterns
	var has_spatial_naming = false
	var spatial_patterns = ["_left_exit", "_right_exit", "_north_exit", "_south_exit"]
	
	for pattern in spatial_patterns:
		if pattern in content:
			has_spatial_naming = true
			break
	
	if has_spatial_naming:
		print("✅ %s uses spatial naming" % file_path.get_file())
		return true
	else:
		print("❌ %s needs spatial naming update" % file_path.get_file())
		return false

func test_all_teleport_files():
	"""Test all teleport gate files for spatial consistency"""
	print("\n📁 Testing all teleport gate files...")
	
	var files_to_test = [
		"res://maps/doi_tre/scenes/TeleportGate_DoiTre.tscn",
		"res://maps/dong_dau/scenes/TeleportGate_DongDau_DoiTre.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DongDau.tscn",
		"res://maps/dong_dau/scenes/TeleportGate_DongDau_LangVanLang.tscn"
	]
	
	var valid_files = 0
	for file_path in files_to_test:
		if validate_teleport_gate_file(file_path):
			valid_files += 1
	
	print("📊 Valid files: %d/%d" % [valid_files, files_to_test.size()])
