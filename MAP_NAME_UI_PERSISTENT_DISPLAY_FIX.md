# Map Name UI Persistent Display Fix

## Problem Identified
The GlobalMapNameUI was experiencing a persistent display issue where the map name would stay visible indefinitely instead of automatically hiding after the specified duration.

## Root Cause Analysis

### Primary Issue: Tween Animation Logic
The main problem was in the `show_map_name()` function where `set_parallel(true)` was used incorrectly:

```gdscript
# PROBLEMATIC CODE:
animation_tween = create_tween()
animation_tween.set_parallel(true)  # This made ALL tweens run in parallel

# Fade in
animation_tween.tween_property(background_panel, "modulate:a", 1.0, fade_duration)
# Scale animations
animation_tween.tween_property(background_panel, "scale", Vector2(1.05, 1.05), fade_duration * 0.6)
animation_tween.tween_property(background_panel, "scale", Vector2(1.0, 1.0), fade_duration * 0.4)
# Wait for display duration - THIS DIDN'T WORK AS EXPECTED
animation_tween.tween_interval(display_duration)
# Fade out - THIS RAN IMMEDIATELY IN PARALLEL
animation_tween.tween_property(background_panel, "modulate:a", 0.0, fade_duration)
```

**Issue**: When `set_parallel(true)` is used, ALL tween operations run simultaneously, including the `tween_interval()` and fade-out animations. This meant the fade-out started immediately instead of waiting for the display duration.

### Secondary Issues
1. **No Safety Mechanism**: No fallback timer to ensure the UI never stays visible forever
2. **Incomplete State Reset**: Scale and other properties weren't properly reset when hiding
3. **No Proper Cleanup**: Timers and tweens weren't properly stopped in all scenarios

## Solution Implemented

### 1. Fixed Animation Sequence
Restructured the animation to use proper sequential timing:

```gdscript
# FIXED CODE:
# Phase 1: Fade in (parallel animations for this phase only)
var fade_in_tween = create_tween()
fade_in_tween.set_parallel(true)
fade_in_tween.tween_property(background_panel, "modulate:a", 1.0, fade_duration)
fade_in_tween.tween_property(background_panel, "scale", Vector2(1.05, 1.05), fade_duration * 0.6)
fade_in_tween.tween_property(background_panel, "scale", Vector2(1.0, 1.0), fade_duration * 0.4)

# Wait for fade in to complete
await fade_in_tween.finished

# Wait for display duration
await get_tree().create_timer(display_duration).timeout

# Phase 2: Fade out (separate tween)
var fade_out_tween = create_tween()
fade_out_tween.set_parallel(true)
fade_out_tween.tween_property(background_panel, "modulate:a", 0.0, fade_duration)
fade_out_tween.tween_property(background_panel, "scale", Vector2(0.9, 0.9), fade_duration)

await fade_out_tween.finished
_hide_panel()
```

### 2. Added Safety Timer Mechanism
```gdscript
# Safety mechanism
var safety_timer: Timer
var max_display_time: float = 10.0  # Maximum time to display (safety fallback)

func _on_safety_timeout() -> void:
    """Safety timeout to ensure map name doesn't stay visible forever"""
    print("⚠️ Safety timeout triggered - force hiding map name")
    hide_map_name()
```

### 3. Improved State Management
- **Proper cleanup**: Stop all timers and tweens when hiding
- **Complete state reset**: Reset scale, alpha, and visibility properly
- **Safety checks**: Added safety timer that triggers after 10 seconds as fallback

### 4. Enhanced Hide Functions
```gdscript
func hide_map_name() -> void:
    """Immediately hide the map name"""
    if animation_tween:
        animation_tween.kill()
    
    if safety_timer:
        safety_timer.stop()  # Stop safety timer
    
    if background_panel:
        background_panel.visible = false
        background_panel.modulate.a = 0.0
        background_panel.scale = Vector2(1.0, 1.0)  # Reset scale
```

## Files Modified

### `ui/scripts/global_map_name_ui.gd`
- ✅ Fixed animation sequence logic
- ✅ Added safety timer mechanism
- ✅ Improved state management and cleanup
- ✅ Enhanced hide functions with proper reset

### Test Files Created
- ✅ `test_map_name_ui_fix.gd` - Comprehensive test for the fix
- ✅ `MAP_NAME_UI_PERSISTENT_DISPLAY_FIX.md` - This documentation

## Testing Results

The fix addresses:
1. ✅ **Persistent Display Issue**: Map name now properly hides after display duration
2. ✅ **Animation Timing**: Fade in → wait → fade out sequence works correctly
3. ✅ **Safety Mechanism**: 10-second safety timer prevents infinite display
4. ✅ **Force Hide**: Manual hide function works immediately
5. ✅ **State Reset**: All visual properties properly reset when hiding

## Usage Instructions

### Normal Usage (Auto-hide):
```gdscript
map_name_ui.set_map_name("dong_dau")
map_name_ui.show_map_name()  # Will auto-hide after 4 seconds + fade time
```

### Force Hide:
```gdscript
map_name_ui.hide_map_name()  # Immediately hides
```

### Check Status:
```gdscript
if map_name_ui.is_showing():
    print("Map name is currently visible")
```

## Expected Behavior After Fix

1. **Show Animation**: Fade in with bounce effect (0.8 seconds)
2. **Display Duration**: Visible for 4 seconds
3. **Hide Animation**: Fade out with scale effect (0.8 seconds)
4. **Total Duration**: ~5.6 seconds total
5. **Safety Fallback**: Force hide after 10 seconds maximum
6. **Manual Control**: Can be hidden immediately with `hide_map_name()`

The map name UI will now behave reliably and never get stuck in a visible state, providing a much better user experience.
