extends Panel

@onready var title_label = $TitleLabel
@onready var description_label = $DescriptionLabel

const LOCKED_TEXT_COLOR = Color(1, 0.3, 0.3)  # Màu đỏ nhạt cho text khóa
const NORMAL_TEXT_COLOR = Color(1, 1, 1)      # Màu trắng cho text bình thường

func show_info(title: String, description: String):
	if not title_label or not description_label:
		return
		
	title_label.text = title
	description_label.text = description
	
	# Nếu là thông báo khóa, đổi màu text
	if description.contains("bị khóa"):
		description_label.add_theme_color_override("font_color", LOCKED_TEXT_COLOR)
	else:
		description_label.add_theme_color_override("font_color", NORMAL_TEXT_COLOR)
	
	visible = true

func hide_info():
	visible = false
