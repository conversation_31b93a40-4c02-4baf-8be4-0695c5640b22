extends Node2D

# ----- Properties -----
var start_button: Button
var tween: Tween
var original_scale: Vector2
var original_color: Color = Color.WHITE
var hover_color: Color = Color(1, 0.8, 0) # Yellow color
var is_hovering: bool = false

# ----- Lifecycle Methods -----
func _ready() -> void:
    start_button = get_node("StartButton")
    if not is_instance_valid(start_button):
        return
        
    original_scale = start_button.scale
    
    # Remove button background to look like a Label
    start_button.add_theme_stylebox_override("normal", StyleBoxEmpty.new())
    start_button.add_theme_stylebox_override("hover", StyleBoxEmpty.new())
    start_button.add_theme_stylebox_override("pressed", StyleBoxEmpty.new())
    
    # Start blinking effect
    start_blinking()
    
    # Connect signals
    start_button.mouse_entered.connect(on_mouse_enter)
    start_button.mouse_exited.connect(on_mouse_exit)
    start_button.pressed.connect(on_start_pressed)

# ----- Animation Methods -----
func start_blinking() -> void:
    if not is_instance_valid(start_button):
        return
        
    if tween != null and tween.is_running():
        tween.kill() # Remove old tween if exists
    
    tween = create_tween()
    tween.set_loops(0) # Use 0 for infinite loops instead of -1
    
    # Text blinking effect (not the whole button)
    tween.tween_property(start_button, "modulate:a", 0.5, 0.5)\
        .set_trans(Tween.TRANS_SINE)
    tween.tween_property(start_button, "modulate:a", 1.0, 0.5)\
        .set_trans(Tween.TRANS_SINE)

# ----- Event Handlers -----
func on_mouse_enter() -> void:
    if not is_instance_valid(start_button):
        return
        
    is_hovering = true
    if tween != null and tween.is_running():
        tween.kill() # Stop blinking when hovering
    
    start_button.add_theme_color_override("font_color", hover_color) # Change to yellow
    start_button.modulate = Color(1, 1, 1, 1) # Reset modulate

func on_mouse_exit() -> void:
    if not is_instance_valid(start_button):
        return
        
    is_hovering = false
    start_blinking() # Return to blinking effect
    start_button.add_theme_color_override("font_color", original_color) # Text returns to white
    start_button.scale = original_scale # Return to original size

func on_start_pressed() -> void:
    if not is_instance_valid(start_button):
        return
        
    start_button.scale = original_scale * 1.2 # Enlarge when pressed
    
    # Wait before changing scene
    await get_tree().create_timer(0.1).timeout
    
    if tween != null and tween.is_running():
        tween.kill() # Stop tween before changing scene
    
    # Use SceneManager instead of direct scene change
    SceneManager.goto_scene("res://Home/scenes/Map_.tscn")
