# DebugTeleportSystem.gd - Comprehensive teleport system debugging
extends Node

var spatial_system: SpatialTeleportSystem

func _ready():
	print("🐛 === COMPREHENSIVE TELEPORT SYSTEM DEBUG ===")
	spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	call_deferred("run_comprehensive_debug")

func run_comprehensive_debug():
	print("\n🔍 DEBUGGING TELEPORT SYSTEM...")
	
	debug_scene_manager()
	debug_spatial_system()
	debug_teleport_gates()
	debug_map_controllers()
	provide_solutions()

func debug_scene_manager():
	print("\n📋 === SCENE MANAGER DEBUG ===")
	if SceneManager:
		print("✅ SceneManager available")
		print("   - Has spawn position: %s" % SceneManager.has_next_spawn_position())
		if SceneManager.has_next_spawn_position():
			print("   - Current spawn position: %s" % SceneManager.get_next_spawn_position())
		print("   - Has goto_scene method: %s" % SceneManager.has_method("goto_scene"))
		print("   - Has set_next_spawn_position method: %s" % SceneManager.has_method("set_next_spawn_position"))
	else:
		print("❌ SceneManager not available")

func debug_spatial_system():
	print("\n🗺️ === SPATIAL SYSTEM DEBUG ===")
	
	# Test all Suoi Thieng gates
	var suoi_thieng_gates = ["suoi_thieng_north_exit", "suoi_thieng2_north_exit"]
	for gate_id in suoi_thieng_gates:
		var spawn_pos = spatial_system.get_spawn_position(gate_id)
		var gate_pos = spatial_system.get_gate_position(gate_id)
		print("🚪 %s:" % gate_id)
		print("   - Spawn position: %s" % spawn_pos)
		print("   - Gate position: %s" % gate_pos)
		print("   - Valid: %s" % ("✅" if spawn_pos != Vector2.ZERO else "❌"))
	
	# Test Lang Van Lang gates
	var lang_van_lang_gates = ["lang_van_lang_south_exit", "lang_van_lang_left_exit", "lang_van_lang_right_exit"]
	for gate_id in lang_van_lang_gates:
		var spawn_pos = spatial_system.get_spawn_position(gate_id)
		print("🚪 %s: spawn at %s" % [gate_id, spawn_pos])

func debug_teleport_gates():
	print("\n🚪 === TELEPORT GATES DEBUG ===")
	
	# Find all teleport gates in current scene
	var teleport_gates = get_tree().get_nodes_in_group("teleport_gates")
	print("Found %d teleport gates in current scene" % teleport_gates.size())
	
	for gate in teleport_gates:
		if gate.has_method("get") and gate.get("gate_id"):
			print("🔍 Gate: %s" % gate.name)
			print("   - Gate ID: %s" % gate.gate_id)
			print("   - Target scene: %s" % gate.target_scene)
			print("   - Target position: %s" % gate.target_position)
			print("   - Script: %s" % gate.get_script())

func debug_map_controllers():
	print("\n🗺️ === MAP CONTROLLERS DEBUG ===")
	
	var map_controllers = get_tree().get_nodes_in_group("map_controllers")
	print("Found %d map controllers" % map_controllers.size())
	
	for controller in map_controllers:
		print("🎮 Controller: %s" % controller.name)
		if controller.has_method("_check_and_setup_teleport_spawn"):
			print("   - Has teleport spawn setup: ✅")
		else:
			print("   - Has teleport spawn setup: ❌")

func provide_solutions():
	print("\n💡 === SOLUTIONS FOR HANG AN SPAWN ISSUE ===")
	print("1. ✅ Ensure SceneManager.set_next_spawn_position() is called")
	print("2. ✅ Verify teleport_gate.gd uses SceneManager")
	print("3. ✅ Check map controllers call get_and_clear_spawn_position()")
	print("4. 🔄 Test specific teleport flow:")
	
	# Simulate teleport flow
	print("\n🧪 SIMULATING TELEPORT FLOW:")
	simulate_teleport_flow()

func simulate_teleport_flow():
	print("Step 1: Player interacts with Lang Van Lang → Hang An gate")
	var hang_an_spawn = spatial_system.get_spawn_position("lang_van_lang_right_exit")
	print("   - Expected spawn position: %s" % hang_an_spawn)
	
	print("Step 2: TeleportGate calls SceneManager.set_next_spawn_position()")
	if SceneManager:
		SceneManager.set_next_spawn_position(hang_an_spawn)
		print("   - ✅ Spawn position set in SceneManager")
	
	print("Step 3: Scene changes to Hang An")
	print("Step 4: Hang An map controller should call get_and_clear_spawn_position()")
	if SceneManager and SceneManager.has_next_spawn_position():
		var retrieved_pos = SceneManager.get_and_clear_spawn_position()
		print("   - ✅ Retrieved spawn position: %s" % retrieved_pos)
		print("   - ✅ Player should be moved to this position")
	else:
		print("   - ❌ No spawn position available for map controller")

func test_specific_gates():
	print("\n🧪 === TESTING SPECIFIC GATES ===")
	
	# Test Suoi Thieng gates
	print("Testing Suoi Thieng gates:")
	test_gate_teleport("suoi_thieng_north_exit", "res://maps/lang_van_lang/scenes/lang_van_lang.tscn")
	test_gate_teleport("suoi_thieng2_north_exit", "res://maps/lang_van_lang/scenes/lang_van_lang.tscn")
	
	# Test Lang Van Lang gates
	print("Testing Lang Van Lang gates:")
	test_gate_teleport("lang_van_lang_right_exit", "res://maps/hang_an/scenes/hang_an.tscn")

func test_gate_teleport(gate_id: String, target_scene: String):
	var spawn_pos = spatial_system.get_spawn_position(gate_id)
	print("🚪 %s → %s" % [gate_id, target_scene.get_file().get_basename()])
	print("   - Spawn position: %s" % spawn_pos)
	
	if spawn_pos != Vector2.ZERO:
		print("   - Status: ✅ Ready for teleport")
		if SceneManager:
			SceneManager.set_next_spawn_position(spawn_pos)
			print("   - SceneManager updated: ✅")
	else:
		print("   - Status: ❌ Missing spawn position")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running comprehensive debug...")
		run_comprehensive_debug()
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n🧪 Testing specific gates...")
		test_specific_gates()
	elif event.is_action_pressed("ui_select"):  # Space key
		print("\n🔄 Simulating teleport flow...")
		simulate_teleport_flow()
