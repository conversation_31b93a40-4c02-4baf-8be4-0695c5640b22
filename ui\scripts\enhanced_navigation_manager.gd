# Enhanced Navigation Manager - <PERSON><PERSON><PERSON><PERSON> <PERSON>ý điều hướng và accessibility cho UI
extends Node

# Navigation properties
var current_focus_index: int = 0
var focusable_controls: Array[Control] = []
var navigation_enabled: bool = true
var wrap_navigation: bool = true

# Audio feedback
var hover_sound: AudioStreamPlayer = null
var click_sound: AudioStreamPlayer = null
var navigation_sound: AudioStreamPlayer = null

# Visual feedback
var focus_indicator: Control = null
var focus_tween: Tween = null

# Accessibility settings
var high_contrast_mode: bool = false
var large_text_mode: bool = false
var reduced_motion: bool = false
var screen_reader_mode: bool = false

# Input settings
var keyboard_navigation: bool = true
var gamepad_navigation: bool = true
var mouse_navigation: bool = true

signal focus_changed(control: Control)
signal navigation_activated(control: Control)

func _ready():
	# Setup audio players
	_setup_audio_feedback()
	
	# Setup focus indicator
	_setup_focus_indicator()
	
	# Connect to input events
	set_process_input(true)
	
	print("🎮 Enhanced Navigation Manager initialized")

func _setup_audio_feedback():
	"""Setup audio feedback for navigation"""
	hover_sound = AudioStreamPlayer.new()
	hover_sound.name = "HoverSound"
	hover_sound.volume_db = -10
	add_child(hover_sound)
	
	click_sound = AudioStreamPlayer.new()
	click_sound.name = "ClickSound"
	click_sound.volume_db = -8
	add_child(click_sound)
	
	navigation_sound = AudioStreamPlayer.new()
	navigation_sound.name = "NavigationSound"
	navigation_sound.volume_db = -12
	add_child(navigation_sound)
	
	print("🔊 Audio feedback setup completed")

func _setup_focus_indicator():
	"""Setup visual focus indicator"""
	focus_indicator = Control.new()
	focus_indicator.name = "FocusIndicator"
	focus_indicator.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Create visual indicator (border)
	var border = NinePatchRect.new()
	border.name = "FocusBorder"
	
	# Create a simple border style
	var style = StyleBoxFlat.new()
	style.bg_color = Color.TRANSPARENT
	style.border_width_left = 3
	style.border_width_top = 3
	style.border_width_right = 3
	style.border_width_bottom = 3
	style.border_color = Color(1, 0.8, 0.3, 0.8)  # Golden border
	style.corner_radius_top_left = 8
	style.corner_radius_top_right = 8
	style.corner_radius_bottom_left = 8
	style.corner_radius_bottom_right = 8
	
	border.add_theme_stylebox_override("panel", style)
	focus_indicator.add_child(border)
	
	# Initially hidden
	focus_indicator.visible = false
	
	print("👁️ Focus indicator setup completed")

func _input(event):
	"""Handle navigation input"""
	if not navigation_enabled:
		return
	
	# Keyboard navigation
	if keyboard_navigation and event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_TAB:
				if event.shift_pressed:
					navigate_previous()
				else:
					navigate_next()
				get_viewport().set_input_as_handled()
			KEY_UP:
				navigate_up()
				get_viewport().set_input_as_handled()
			KEY_DOWN:
				navigate_down()
				get_viewport().set_input_as_handled()
			KEY_LEFT:
				navigate_left()
				get_viewport().set_input_as_handled()
			KEY_RIGHT:
				navigate_right()
				get_viewport().set_input_as_handled()
			KEY_ENTER, KEY_SPACE:
				activate_current()
				get_viewport().set_input_as_handled()
			KEY_ESCAPE:
				handle_back_navigation()
				get_viewport().set_input_as_handled()
	
	# Gamepad navigation
	if gamepad_navigation and event is InputEventJoypadButton and event.pressed:
		match event.button_index:
			JOY_BUTTON_DPAD_UP:
				navigate_up()
			JOY_BUTTON_DPAD_DOWN:
				navigate_down()
			JOY_BUTTON_DPAD_LEFT:
				navigate_left()
			JOY_BUTTON_DPAD_RIGHT:
				navigate_right()
			JOY_BUTTON_A:
				activate_current()
			JOY_BUTTON_B:
				handle_back_navigation()

# Navigation functions
func register_focusable_controls(controls: Array[Control]):
	"""Register controls that can receive focus"""
	focusable_controls.clear()
	
	for control in controls:
		if is_instance_valid(control) and control.visible:
			focusable_controls.append(control)
			
			# Connect mouse events for hybrid navigation
			if mouse_navigation:
				if not control.mouse_entered.is_connected(_on_control_mouse_entered):
					control.mouse_entered.connect(_on_control_mouse_entered.bind(control))
	
	if focusable_controls.size() > 0:
		current_focus_index = 0
		_update_focus()
	
	print("🎯 Registered %d focusable controls" % focusable_controls.size())

func navigate_next():
	"""Navigate to next control"""
	if focusable_controls.size() == 0:
		return
	
	current_focus_index += 1
	if wrap_navigation and current_focus_index >= focusable_controls.size():
		current_focus_index = 0
	elif current_focus_index >= focusable_controls.size():
		current_focus_index = focusable_controls.size() - 1
	
	_update_focus()
	_play_navigation_sound()

func navigate_previous():
	"""Navigate to previous control"""
	if focusable_controls.size() == 0:
		return
	
	current_focus_index -= 1
	if wrap_navigation and current_focus_index < 0:
		current_focus_index = focusable_controls.size() - 1
	elif current_focus_index < 0:
		current_focus_index = 0
	
	_update_focus()
	_play_navigation_sound()

func navigate_up():
	"""Navigate up (find control above current)"""
	_navigate_directional(Vector2.UP)

func navigate_down():
	"""Navigate down (find control below current)"""
	_navigate_directional(Vector2.DOWN)

func navigate_left():
	"""Navigate left (find control to the left)"""
	_navigate_directional(Vector2.LEFT)

func navigate_right():
	"""Navigate right (find control to the right)"""
	_navigate_directional(Vector2.RIGHT)

func _navigate_directional(direction: Vector2):
	"""Navigate in a specific direction"""
	if focusable_controls.size() == 0:
		return
	
	var current_control = focusable_controls[current_focus_index]
	var current_pos = current_control.global_position + current_control.size * 0.5
	
	var best_control_index = -1
	var best_distance = INF
	
	for i in range(focusable_controls.size()):
		if i == current_focus_index:
			continue
		
		var control = focusable_controls[i]
		var control_pos = control.global_position + control.size * 0.5
		var diff = control_pos - current_pos
		
		# Check if control is in the right direction
		var dot_product = diff.normalized().dot(direction)
		if dot_product > 0.5:  # At least 60 degrees in the right direction
			var distance = diff.length()
			if distance < best_distance:
				best_distance = distance
				best_control_index = i
	
	if best_control_index != -1:
		current_focus_index = best_control_index
		_update_focus()
		_play_navigation_sound()

func activate_current():
	"""Activate the currently focused control"""
	if focusable_controls.size() == 0 or current_focus_index >= focusable_controls.size():
		return
	
	var current_control = focusable_controls[current_focus_index]
	
	if current_control is Button:
		var button = current_control as Button
		button.pressed.emit()
		_play_click_sound()
	
	navigation_activated.emit(current_control)
	print("🎯 Activated control: %s" % current_control.name)

func handle_back_navigation():
	"""Handle back/escape navigation"""
	# This can be overridden by specific menus
	print("🔙 Back navigation requested")

func _update_focus():
	"""Update visual focus indicator"""
	if focusable_controls.size() == 0 or current_focus_index >= focusable_controls.size():
		if focus_indicator:
			focus_indicator.visible = false
		return
	
	var current_control = focusable_controls[current_focus_index]
	
	# Position focus indicator
	if focus_indicator and current_control:
		var parent = current_control.get_parent()
		if parent and focus_indicator.get_parent() != parent:
			if focus_indicator.get_parent():
				focus_indicator.get_parent().remove_child(focus_indicator)
			parent.add_child(focus_indicator)
		
		focus_indicator.position = current_control.position - Vector2(5, 5)
		focus_indicator.size = current_control.size + Vector2(10, 10)
		focus_indicator.visible = true
		
		# Animate focus indicator
		if focus_tween:
			focus_tween.kill()
		
		if not reduced_motion:
			focus_tween = create_tween()
			focus_tween.set_loops()
			focus_tween.tween_property(focus_indicator, "modulate:a", 0.6, 0.5)
			focus_tween.tween_property(focus_indicator, "modulate:a", 1.0, 0.5)
	
	focus_changed.emit(current_control)
	print("🎯 Focus updated to: %s" % current_control.name)

func _on_control_mouse_entered(control: Control):
	"""Handle mouse entering a control"""
	if not mouse_navigation:
		return
	
	var index = focusable_controls.find(control)
	if index != -1:
		current_focus_index = index
		_update_focus()
		_play_hover_sound()

# Audio feedback functions
func _play_hover_sound():
	if hover_sound and not reduced_motion:
		hover_sound.play()

func _play_click_sound():
	if click_sound:
		click_sound.play()

func _play_navigation_sound():
	if navigation_sound and not reduced_motion:
		navigation_sound.play()

# Accessibility functions
func set_high_contrast_mode(enabled: bool):
	"""Enable/disable high contrast mode"""
	high_contrast_mode = enabled
	# This would update all UI elements with high contrast colors
	print("🎨 High contrast mode: %s" % ("enabled" if enabled else "disabled"))

func set_large_text_mode(enabled: bool):
	"""Enable/disable large text mode"""
	large_text_mode = enabled
	# This would increase font sizes across the UI
	print("📝 Large text mode: %s" % ("enabled" if enabled else "disabled"))

func set_reduced_motion(enabled: bool):
	"""Enable/disable reduced motion"""
	reduced_motion = enabled
	print("🎬 Reduced motion: %s" % ("enabled" if enabled else "disabled"))

# Utility functions
func get_current_focused_control() -> Control:
	"""Get the currently focused control"""
	if focusable_controls.size() > 0 and current_focus_index < focusable_controls.size():
		return focusable_controls[current_focus_index]
	return null

func set_focus_to_control(control: Control):
	"""Set focus to a specific control"""
	var index = focusable_controls.find(control)
	if index != -1:
		current_focus_index = index
		_update_focus()

func clear_focus():
	"""Clear current focus"""
	if focus_indicator:
		focus_indicator.visible = false
	current_focus_index = -1
