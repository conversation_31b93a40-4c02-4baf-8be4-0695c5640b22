# HangAnMapController.gd - Controller cho map Hang Ăn
extends Node2D
class_name HangAnMapController

# ----- Signals -----
signal map_loaded
signal teleport_gate_activated(gate_id: String)

# ----- Node References -----
@onready var player: Player = null
@onready var teleport_gates: Array[TeleportGate] = []

# ----- Map Settings -----
var map_id: String = "hang_an"
var map_name: String = "Hang Ăn"

func _ready() -> void:
	# Add to map controllers group for debugging
	add_to_group("map_controllers")
	
	print("🏔️ Hang An Map Controller initialized")
	call_deferred("_setup_map")

func _setup_map() -> void:
	# Tìm player trong scene
	_find_player()

	# Check if player needs to be repositioned (for teleport)
	_check_and_setup_teleport_spawn()

	# Tìm và setup các cổng dịch chuyển
	_setup_teleport_gates()

	# Hiển thị tên bản đồ
	_show_map_name_ui()

	# Emit signal map đã load xong
	map_loaded.emit()
	print("✅ Hang An map setup completed")

func _show_map_name_ui() -> void:
	if GlobalMapNameUI:
		GlobalMapNameUI.set_map_name(map_name)
		GlobalMapNameUI.show_map_name()
		print("🗺️ Updated global map name UI: %s" % map_name)
	else:
		print("⚠️ GlobalMapNameUI not available")

func _find_player() -> void:
	# Tìm player node
	player = get_tree().get_first_node_in_group("player")
	if not player:
		# Thử tìm theo đường dẫn
		player = get_node_or_null("Player")
	
	if player:
		print("👤 Player found in Hang An map: %s" % player.name)
		print("Player position: ", player.global_position)
		
		# Auto-fix player position if came from teleport
		_auto_fix_teleport_position()
	else:
		print("⚠️ WARNING: Player not found in Hang An map")

func _check_and_setup_teleport_spawn() -> void:
	# Check if player needs to be repositioned (for teleport)
	_auto_fix_teleport_position()

func _auto_fix_teleport_position() -> void:
	"""Tự động sửa vị trí player nếu đến từ teleport"""
	if not player:
		return

	# Check if SceneManager has spawn position set
	if SceneManager and SceneManager.has_next_spawn_position():
		var target_pos = SceneManager.get_and_clear_spawn_position()
		print("🎯 Hang An: Auto-fixing player position from %s to %s" % [player.global_position, target_pos])

		# Set player position with validation
		if target_pos != Vector2.ZERO:
			player.global_position = target_pos
			print("✅ Hang An: Player repositioned successfully to: %s" % player.global_position)

			# Ensure camera updates
			var camera = get_viewport().get_camera_2d()
			if camera:
				camera.force_update_scroll()
		else:
			print("⚠️ Hang An: Invalid spawn position, using default")
			_set_default_spawn_position()
	else:
		print("📍 Hang An: No teleport spawn position set, keeping default position")

func _set_default_spawn_position() -> void:
	"""Set player to default safe position for hang_an map"""
	var safe_position = Vector2(-2069, 484)  # Default hang_an position
	player.global_position = safe_position
	player.velocity = Vector2.ZERO  # Stop any falling motion
	print("🏠 Hang An: Player set to default position: %s" % safe_position)

	# Validate position after setting
	call_deferred("_validate_player_position")

func _validate_player_position() -> void:
	"""Validate player is not falling from sky"""
	if not player:
		return

	var pos = player.global_position

	# Check if player is too high (likely falling from sky)
	if pos.y < -1500:
		print("⚠️ Hang An: Player seems to be falling from sky at: %s" % pos)
		_set_safe_ground_position()
		return

	# Check if player is at the problematic scene default position
	if pos.distance_to(Vector2(-4365, 736)) < 100:
		print("⚠️ Hang An: Player at problematic scene position, fixing...")
		_set_safe_ground_position()
		return

	# Wait a few frames and check if player is falling rapidly
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().process_frame

	if player and player.velocity.y > 800:
		print("⚠️ Hang An: Player falling rapidly (velocity: %s), fixing position" % player.velocity)
		_set_safe_ground_position()

func _set_safe_ground_position() -> void:
	"""Emergency function to set player to confirmed safe ground position"""
	if not player:
		return

	var emergency_safe_position = Vector2(-2069, 484)  # Confirmed ground position
	player.global_position = emergency_safe_position
	player.velocity = Vector2.ZERO

	# Force camera update
	var camera = get_viewport().get_camera_2d()
	if camera:
		camera.force_update_scroll()

	print("🚨 Hang An: Emergency repositioning to safe ground: %s" % emergency_safe_position)

func _setup_teleport_gates() -> void:
	# Tìm tất cả cổng dịch chuyển trong scene
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	
	for gate in gates:
		if gate is TeleportGate:
			teleport_gates.append(gate)
			_connect_gate_signals(gate)
			print("Connected teleport gate: ", gate.gate_id)

func _connect_gate_signals(gate: TeleportGate) -> void:
	if not gate:
		return
	
	# Kết nối signals của cổng
	if not gate.player_entered_gate.is_connected(_on_player_entered_gate):
		gate.player_entered_gate.connect(_on_player_entered_gate)
	
	if not gate.player_exited_gate.is_connected(_on_player_exited_gate):
		gate.player_exited_gate.connect(_on_player_exited_gate)

func _on_player_entered_gate(gate: TeleportGate) -> void:
	print("Player entered teleport gate: ", gate.gate_id)
	emit_signal("teleport_gate_activated", gate.gate_id)

func _on_player_exited_gate(gate: TeleportGate) -> void:
	print("Player exited teleport gate: ", gate.gate_id)
	print("Player exited teleport gate: ", gate.gate_id)
