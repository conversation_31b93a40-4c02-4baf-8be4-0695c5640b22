# RunState.gd
extends PlayerState
class_name RunState

var speed_threshold: float = 450

func get_state_name():
	return "run"

func enter(owner):
	if owner.is_attacking:
		return
	print("Player entered Run State")
	speed_threshold = owner.speed
	owner.play_animation("running")

func exit(owner):
	print("Player exited Run State")

func handle_input(owner, event):
	if owner.is_attacking:
		return
	# When a jump input occurs in the run state:
	if event.is_action_pressed("jump") and owner.is_on_floor():
		owner.change_state(JumpState.new())

func update(owner, delta):
	var direction = Input.get_axis("move_left", "move_right")
	
	# Transition back to idle state if no directional input
	if direction == 0:
		owner.change_state(IdleState.new())
