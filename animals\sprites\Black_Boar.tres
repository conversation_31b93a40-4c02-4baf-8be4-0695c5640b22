[gd_resource type="SpriteFrames" load_steps=25 format=3 uid="uid://bu01oodc00m4g"]

[ext_resource type="Texture2D" uid="uid://cuji1y47at0vk" path="res://assets/images/characters/mobs/Boar/Hit-Vanish/Hit-Sheet-Black.png" id="1_p5h57"]
[ext_resource type="Texture2D" uid="uid://df1uda07c6c4w" path="res://assets/images/characters/mobs/Boar/Idle/Idle-Sheet-export-Back.png" id="2_2d85w"]
[ext_resource type="Texture2D" uid="uid://cc8cxrpf4tbdf" path="res://assets/images/characters/mobs/Boar/Run/Run-Sheet-Black.png" id="3_5wrji"]
[ext_resource type="Texture2D" uid="uid://wkkpdeq1tvyi" path="res://assets/images/characters/mobs/Boar/Walk/Walk-Base-SheetBlack.png" id="4_laio1"]

[sub_resource type="AtlasTexture" id="AtlasTexture_xo421"]
atlas = ExtResource("1_p5h57")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_168um"]
atlas = ExtResource("1_p5h57")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_edo0t"]
atlas = ExtResource("1_p5h57")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ns3b2"]
atlas = ExtResource("1_p5h57")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_o451i"]
atlas = ExtResource("2_2d85w")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_qlsq4"]
atlas = ExtResource("2_2d85w")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_xv44a"]
atlas = ExtResource("2_2d85w")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_kq7vf"]
atlas = ExtResource("2_2d85w")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_u5c6v"]
atlas = ExtResource("3_5wrji")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6gtxe"]
atlas = ExtResource("3_5wrji")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_lcadv"]
atlas = ExtResource("3_5wrji")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_tkjco"]
atlas = ExtResource("3_5wrji")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_hq46s"]
atlas = ExtResource("3_5wrji")
region = Rect2(192, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_53gl6"]
atlas = ExtResource("3_5wrji")
region = Rect2(240, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ugpq3"]
atlas = ExtResource("4_laio1")
region = Rect2(0, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ygce0"]
atlas = ExtResource("4_laio1")
region = Rect2(48, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_hnm4s"]
atlas = ExtResource("4_laio1")
region = Rect2(96, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_h2a8r"]
atlas = ExtResource("4_laio1")
region = Rect2(144, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_hwtwu"]
atlas = ExtResource("4_laio1")
region = Rect2(192, 0, 48, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8828d"]
atlas = ExtResource("4_laio1")
region = Rect2(240, 0, 48, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_xo421")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_168um")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_edo0t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ns3b2")
}],
"loop": false,
"name": &"hit_vanish",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_o451i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qlsq4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xv44a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kq7vf")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_u5c6v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6gtxe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lcadv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tkjco")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hq46s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_53gl6")
}],
"loop": true,
"name": &"run",
"speed": 12.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ugpq3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ygce0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnm4s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h2a8r")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hwtwu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8828d")
}],
"loop": true,
"name": &"walk",
"speed": 10.0
}]
