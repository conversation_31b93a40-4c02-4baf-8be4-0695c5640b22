extends Node2D

# ----- Lifecycle Methods -----
func _ready() -> void:
	# Get slider nodes
	var master_volume_slider := get_node("MasterVolume") as HSlider
	var bgm_volume_slider := get_node("BGMVolume") as HSlider
	
	# Customize sizes
	customize_slider(master_volume_slider)
	customize_slider(bgm_volume_slider)

# ----- UI Customization Methods -----
func customize_slider(slider: HSlider) -> void:
	# Increase height (make slider larger)
	slider.custom_minimum_size = Vector2(250, 50) # 50 is the height
	
	# Get default theme
	var theme_obj = slider.theme if slider.theme else Theme.new()
	
	# Create StyleBoxFlat for track
	var track_style = StyleBoxFlat.new()
	track_style.bg_color = Color(0.3, 0.3, 0.3) # Dark gray
	track_style.content_margin_top = 15 # Make track thicker
	track_style.content_margin_bottom = 15
	
	# Create StyleBoxFlat for grabber (slider handle)
	var grabber_style = StyleBoxFlat.new()
	grabber_style.bg_color = Color(1, 0.8, 0) # Yellow
	grabber_style.set_corner_radius_all(20) # Make handle rounder
	grabber_style.content_margin_left = 10 # Make handle larger
	grabber_style.content_margin_right = 10
	grabber_style.content_margin_top = 10
	grabber_style.content_margin_bottom = 10
	
	# Apply theme
	theme_obj.set_stylebox("grabber", "HSlider", grabber_style)
	theme_obj.set_stylebox("slider", "HSlider", track_style)
	slider.theme = theme_obj 