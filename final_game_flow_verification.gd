# Final Game Flow Verification - Complete system test
extends Node

var test_results: Array = []
var current_test: String = ""

func _ready():
	print("🎮 Final Game Flow Verification initialized")
	print("Press F5 to run complete verification")

func run_complete_verification():
	"""Run complete game flow verification"""
	print("\n" + "="*60)
	print("🚀 RUNNING COMPLETE GAME FLOW VERIFICATION")
	print("="*60)
	
	test_results.clear()
	
	# Test 1: Loading Screen System
	await test_loading_screen_system()
	
	# Test 2: Scene Manager
	await test_scene_manager()
	
	# Test 3: Teleportation System
	await test_teleportation_system()
	
	# Test 4: Emergency Systems
	await test_emergency_systems()
	
	# Test 5: Main Menu Navigation
	await test_main_menu_navigation()
	
	# Print final results
	print_final_results()

func test_loading_screen_system():
	"""Test loading screen system"""
	current_test = "Loading Screen System"
	print("\n🔍 Testing Loading Screen System...")
	
	try:
		# Test loading screen instantiation
		var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
		if not loading_scene:
			record_result(false, "Loading screen scene not found")
			return
		
		var loading_instance = loading_scene.instantiate()
		get_tree().root.add_child(loading_instance)
		
		# Wait for initialization
		await get_tree().create_timer(0.5).timeout
		
		# Test enhanced methods
		var has_enhanced_methods = (
			loading_instance.has_method("set_target_scene") and
			loading_instance.has_method("set_auto_transition") and
			loading_instance.has_method("force_completion") and
			loading_instance.has_method("update_progress_enhanced")
		)
		
		if not has_enhanced_methods:
			record_result(false, "Enhanced methods not available")
			loading_instance.queue_free()
			return
		
		# Configure loading screen
		loading_instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
		loading_instance.set_auto_transition(true)
		
		# Test progress update
		loading_instance.update_progress_enhanced(100.0, "Test completion")
		
		# Wait for auto-transition
		var transition_success = false
		for i in range(50):  # Wait up to 5 seconds
			await get_tree().create_timer(0.1).timeout
			if not is_instance_valid(loading_instance) or not loading_instance.get_parent():
				transition_success = true
				break
		
		if transition_success:
			record_result(true, "Auto-transition successful")
		else:
			record_result(false, "Auto-transition failed")
			if is_instance_valid(loading_instance):
				loading_instance.queue_free()
		
	except:
		record_result(false, "Exception during loading screen test")

func test_scene_manager():
	"""Test scene manager functionality"""
	current_test = "Scene Manager"
	print("\n🔍 Testing Scene Manager...")
	
	try:
		if not SceneManager:
			record_result(false, "SceneManager not available")
			return
		
		# Test SceneManager methods
		var has_required_methods = (
			SceneManager.has_method("goto_scene") and
			SceneManager.has_method("find_loading_screen") and
			SceneManager.has_method("find_loading_progress_bar")
		)
		
		if not has_required_methods:
			record_result(false, "Required SceneManager methods missing")
			return
		
		record_result(true, "SceneManager fully functional")
		
	except:
		record_result(false, "Exception during SceneManager test")

func test_teleportation_system():
	"""Test teleportation system"""
	current_test = "Teleportation System"
	print("\n🔍 Testing Teleportation System...")
	
	try:
		# Check if teleport gate script exists
		if not ResourceLoader.exists("res://maps/scripts/teleport_gate.gd"):
			record_result(false, "Teleport gate script not found")
			return
		
		# Check key map scenes
		var key_scenes = [
			"res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
			"res://maps/rung_nuong/scenes/rung_nuong.tscn",
			"res://maps/dong_dau/scenes/dong_dau.tscn"
		]
		
		var missing_scenes = []
		for scene_path in key_scenes:
			if not ResourceLoader.exists(scene_path):
				missing_scenes.append(scene_path)
		
		if missing_scenes.size() > 0:
			record_result(false, "Missing key scenes: " + str(missing_scenes))
			return
		
		record_result(true, "All key teleportation scenes available")
		
	except:
		record_result(false, "Exception during teleportation test")

func test_emergency_systems():
	"""Test emergency systems"""
	current_test = "Emergency Systems"
	print("\n🔍 Testing Emergency Systems...")
	
	try:
		# Test LoadingScreenManager autoload
		if not LoadingScreenManager:
			record_result(false, "LoadingScreenManager autoload not available")
			return
		
		# Test emergency fix methods
		var has_emergency_methods = (
			LoadingScreenManager.has_method("force_fix_all_loading_screens") and
			LoadingScreenManager.has_method("emergency_goto_main_menu") and
			LoadingScreenManager.has_method("debug_all_loading_screens")
		)
		
		if not has_emergency_methods:
			record_result(false, "Emergency methods not available")
			return
		
		record_result(true, "Emergency systems fully operational")
		
	except:
		record_result(false, "Exception during emergency systems test")

func test_main_menu_navigation():
	"""Test main menu navigation"""
	current_test = "Main Menu Navigation"
	print("\n🔍 Testing Main Menu Navigation...")
	
	try:
		# Check if main menu scene exists
		if not ResourceLoader.exists("res://Home/scenes/Startmenu.tscn"):
			record_result(false, "Main menu scene not found")
			return
		
		# Check if main menu script exists
		if not ResourceLoader.exists("res://Home/Scripts/Functioning/MainMenu.gd"):
			record_result(false, "Main menu script not found")
			return
		
		record_result(true, "Main menu navigation ready")
		
	except:
		record_result(false, "Exception during main menu test")

func record_result(success: bool, details: String):
	"""Record test result"""
	test_results.append({
		"test": current_test,
		"success": success,
		"details": details
	})
	
	var status = "✅ PASS" if success else "❌ FAIL"
	print("%s - %s: %s" % [status, current_test, details])

func print_final_results():
	"""Print final verification results"""
	print("\n" + "="*60)
	print("📊 FINAL GAME FLOW VERIFICATION RESULTS")
	print("="*60)
	
	var passed = 0
	var total = test_results.size()
	
	for result in test_results:
		var status = "✅ PASS" if result.success else "❌ FAIL"
		print("%s - %s: %s" % [status, result.test, result.details])
		if result.success:
			passed += 1
	
	print("\n📈 SUMMARY: %d/%d tests passed (%.1f%%)" % [passed, total, (float(passed)/total)*100])
	
	if passed == total:
		print("🎉 ALL SYSTEMS OPERATIONAL! Game is ready for play.")
		print("🚀 Loading screens will work properly")
		print("⚡ Teleportation will be 1.5x faster")
		print("🛡️ Emergency fixes are available if needed")
	else:
		print("⚠️ Some systems need attention. Check failed tests above.")
		print("🔧 Use emergency fixes if loading screens get stuck:")
		print("   - Press Ctrl+Alt+F to force fix loading screens")
		print("   - Press Ctrl+Alt+M to go to main menu")

# Input handling
func _input(event):
	if event is InputEventKey and event.pressed:
		# F5 = Run complete verification
		if event.keycode == KEY_F5:
			run_complete_verification()
		
		# F6 = Quick emergency fix test
		elif event.keycode == KEY_F6:
			test_emergency_fix()
		
		# F7 = Test loading screen only
		elif event.keycode == KEY_F7:
			test_loading_screen_system()

func test_emergency_fix():
	"""Quick test of emergency fix"""
	print("\n🚨 Testing Emergency Fix...")
	
	if LoadingScreenManager:
		LoadingScreenManager.debug_all_loading_screens()
		print("✅ Emergency systems ready")
	else:
		print("❌ LoadingScreenManager not available")

# Quick access functions
func quick_goto_main_menu():
	"""Quick function to go to main menu"""
	print("🏠 Going to main menu...")
	get_tree().change_scene_to_file("res://Home/scenes/Startmenu.tscn")

func quick_emergency_fix():
	"""Quick emergency fix"""
	print("🚨 Running emergency fix...")
	if LoadingScreenManager:
		LoadingScreenManager.force_fix_all_loading_screens()
	else:
		print("❌ LoadingScreenManager not available")

func show_help():
	"""Show help information"""
	print("\n" + "="*50)
	print("🎮 FINAL GAME FLOW VERIFICATION - HELP")
	print("="*50)
	print("Keyboard Shortcuts:")
	print("  F5 - Run complete verification")
	print("  F6 - Test emergency systems")
	print("  F7 - Test loading screen only")
	print("")
	print("Emergency Fixes:")
	print("  Ctrl+Alt+F - Force fix all loading screens")
	print("  Ctrl+Alt+M - Emergency goto main menu")
	print("  Ctrl+Alt+D - Debug loading screens")
	print("")
	print("Console Commands:")
	print("  quick_goto_main_menu() - Go to main menu")
	print("  quick_emergency_fix() - Run emergency fix")
	print("  show_help() - Show this help")
	print("="*50)
