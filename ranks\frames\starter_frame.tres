[gd_resource type="SpriteFrames" load_steps=14 format=3 uid="uid://6hkxiu2q3y7r"]

[sub_resource type="AtlasTexture" id="AtlasTexture_8vpug"]
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ea2fc"]
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_c5fyo"]
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_pbvhy"]
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_bhkrh"]
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ce0ng"]
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ljas2"]
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ni4w3"]
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2lcjy"]
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_loilx"]
region = Rect2(576, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_joje3"]
region = Rect2(640, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_aun1g"]
region = Rect2(704, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vp0br"]
region = Rect2(768, 0, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_8vpug")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ea2fc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c5fyo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pbvhy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bhkrh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ce0ng")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ljas2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ni4w3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2lcjy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_loilx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_joje3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aun1g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vp0br")
}],
"loop": true,
"name": &"burst",
"speed": 10.0
}]
