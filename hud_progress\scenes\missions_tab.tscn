[gd_scene load_steps=17 format=3 uid="uid://dictpnem0m1vy"]

[ext_resource type="Texture2D" uid="uid://dtxqt86vsmww0" path="res://hud_progress/images/BackPack.png" id="1_44h5l"]
[ext_resource type="Script" uid="uid://cjlk22sacm6fp" path="res://hud_progress/scripts/missions_tab.gd" id="1_w8j4w"]
[ext_resource type="Texture2D" uid="uid://bu7yt8thgnu8q" path="res://hud_progress/images/close 1.png" id="2_lhv0m"]
[ext_resource type="Texture2D" uid="uid://bsqm02j0ywkts" path="res://hud_progress/images/Map_Button.png" id="3_ig65y"]
[ext_resource type="Texture2D" uid="uid://darqm32h0cpj7" path="res://hud_progress/images/Group 1501.png" id="4_4jcgq"]
[ext_resource type="Texture2D" uid="uid://ko5svgdg634e" path="res://hud_progress/images/Group 1493.png" id="5_afaxi"]
[ext_resource type="Texture2D" uid="uid://dl7ucdnnpjdrg" path="res://hud_progress/images/Group 1494.png" id="6_hfe8v"]
[ext_resource type="Texture2D" uid="uid://chhvfvg7fvw03" path="res://hud_progress/images/Group 1505.png" id="7_wcwgi"]
[ext_resource type="Texture2D" uid="uid://byep2fst4irrb" path="res://hud_progress/images/Group 1495.png" id="8_qn8na"]
[ext_resource type="Texture2D" uid="uid://bed2f8xpe4wrq" path="res://hud_progress/images/Group 1504.png" id="9_hvpm1"]
[ext_resource type="Texture2D" uid="uid://cx0nn3qcow14s" path="res://hud_progress/images/Group 1496.png" id="10_fnqyi"]
[ext_resource type="Texture2D" uid="uid://cb4pip81phr0y" path="res://hud_progress/images/Group 1503.png" id="11_su0ja"]
[ext_resource type="Texture2D" uid="uid://l1fae18c08k3" path="res://hud_progress/images/mp_border.png" id="13_wy7kd"]
[ext_resource type="Texture2D" uid="uid://dlrs7wwi50o5e" path="res://hud_progress/images/mp_color.png" id="14_uv831"]
[ext_resource type="Texture2D" uid="uid://cmek6fyluh4ul" path="res://hud_progress/images/HP_border.png" id="15_yl40q"]
[ext_resource type="Texture2D" uid="uid://dmbk6gi0o1nn1" path="res://hud_progress/images/hp_color.png" id="16_k5u7y"]

[node name="Missions_Tab" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(1e-05, 1e-05)
script = ExtResource("1_w8j4w")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Panel" type="Panel" parent="CanvasLayer"]
offset_right = 40.0
offset_bottom = 40.0

[node name="BackPack" type="Sprite2D" parent="CanvasLayer/Panel"]
position = Vector2(240, 360)
texture = ExtResource("1_44h5l")

[node name="Close_Button" type="TextureButton" parent="CanvasLayer"]
offset_left = 456.0
offset_top = 17.0
offset_right = 501.0
offset_bottom = 62.0
texture_normal = ExtResource("2_lhv0m")

[node name="Map_Button" type="TextureButton" parent="CanvasLayer"]
offset_left = 185.0
offset_top = 610.0
offset_right = 296.0
offset_bottom = 673.0
texture_normal = ExtResource("3_ig65y")

[node name="All_Tab_Button" type="HBoxContainer" parent="CanvasLayer"]
offset_left = 16.0
offset_top = 123.0
offset_right = 464.0
offset_bottom = 186.0

[node name="Mission_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("5_afaxi")
texture_disabled = ExtResource("4_4jcgq")

[node name="Inventory_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("6_hfe8v")
texture_pressed = ExtResource("7_wcwgi")
texture_disabled = ExtResource("6_hfe8v")

[node name="Skills_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("8_qn8na")
texture_pressed = ExtResource("9_hvpm1")
texture_disabled = ExtResource("8_qn8na")

[node name="Etc_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("10_fnqyi")
texture_pressed = ExtResource("11_su0ja")
texture_disabled = ExtResource("10_fnqyi")

[node name="ManaProgressBar" type="TextureProgressBar" parent="CanvasLayer"]
offset_left = 171.0
offset_top = 47.0
offset_right = 373.0
offset_bottom = 70.0
value = 100.0
texture_under = ExtResource("13_wy7kd")
texture_progress = ExtResource("14_uv831")
texture_progress_offset = Vector2(1, 1)

[node name="HealthProgressBar" type="TextureProgressBar" parent="CanvasLayer"]
offset_left = 170.0
offset_top = 10.0
offset_right = 727.0
offset_bottom = 55.0
scale = Vector2(0.5, 0.5)
value = 50.0
texture_under = ExtResource("15_yl40q")
texture_progress = ExtResource("16_k5u7y")
texture_progress_offset = Vector2(3, 2)
