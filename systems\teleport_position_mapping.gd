# Teleport Position Mapping System - Quản lý vị trí spawn ch<PERSON><PERSON> xác cho teleport
extends Node

# Ma trận mapping vị trí spawn cho mỗi teleport gate
# ✅ UPDATED: Synchronized with teleport gate scene files for accuracy
var position_mappings: Dictionary = {
	# Lang Van Lang -> Other Maps (spawn gần cổng tương ứng trong map đích)
	"lang_van_lang_to_rung_nuong": Vector2(750, -1200),      # From TeleportGate_LangVanLang_RungNuong.tscn
	"lang_van_lang_to_dong_dau": Vector2(-2122, -296),       # Near lang_van_lang gate in dong_dau.tscn
	"lang_van_lang_to_hang_an": Vector2(300, -1900),         # From TeleportGate_LangVanLang_HangAn.tscn
	"lang_van_lang_to_suoi_thieng": Vector2(-2069, 484),     # Default suoi_thieng position (no specific gate)
	"lang_van_lang_to_doi_tre": Vector2(-2292, -538),        # From TeleportGate_LangVanLang_DoiTre.tscn

	# Rung Nuong -> Other Maps (spawn gần cổng tương ứng trong map đích)
	"rung_nuong_to_lang_van_lang": Vector2(2000, -1900),     # From TeleportGate_RungNuong_LangVanLang.tscn
	"rung_nuong_to_dong_dau": Vector2(5200, -350),           # ✅ FIXED: From TeleportGate_RungNuong.tscn (near rung_nuong gate)
	"rung_nuong_to_hang_an": Vector2(300, -1900),            # From TeleportGate_RungNuong_HangAn.tscn
	"rung_nuong_to_suoi_thieng": Vector2(-2000, 200),        # From TeleportGate_RungNuong_SuoiThieng.tscn
	"rung_nuong_to_doi_tre": Vector2(-2292, -538),           # Default doi_tre position (no specific gate)
	
	# Dong Dau -> Other Maps (spawn gần cổng tương ứng trong map đích)
	"dong_dau_to_lang_van_lang": Vector2(300, -1900),        # target_position from TeleportGate_DongDau_LangVanLang
	"dong_dau_to_rung_nuong": Vector2(-200, -1300),          # ✅ FIXED: From TeleportGate_DongDau.tscn (near dong_dau gate)
	"dong_dau_to_doi_tre": Vector2(-2292, -538),             # From TeleportGate_DongDau_DoiTre.tscn
	"dong_dau_to_hang_an": Vector2(-2069, 484),              # Default hang_an position (no specific gate found)
	"dong_dau_to_suoi_thieng": Vector2(-2069, 484),          # Default suoi_thieng position (no specific gate found)
	
	# Hang An -> Other Maps (spawn gần cổng tương ứng trong map đích)
	"hang_an_to_lang_van_lang": Vector2(3700, -1900),       # target_position from TeleportGate_HangAn_LangVanLang
	"hang_an_to_rung_nuong": Vector2(2000, -1200),          # target_position from TeleportGate_HangAn
	"hang_an_to_dong_dau": Vector2(-1421, -429),            # Default dong_dau position (no specific gate found)
	"hang_an_to_suoi_thieng": Vector2(-2069, 484),          # Default suoi_thieng position (no specific gate found)
	"hang_an_to_doi_tre": Vector2(-2292, -538),             # Default doi_tre position (no specific gate found)
	
	# Suoi Thieng -> Other Maps (spawn gần cổng tương ứng trong map đích)
	"suoi_thieng_to_lang_van_lang": Vector2(2500, -1900),   # Position from lang_van_lang teleport config
	"suoi_thieng_to_rung_nuong": Vector2(753, -1225),       # Default rung_nuong position (no specific gate found)
	"suoi_thieng_to_dong_dau": Vector2(-1421, -429),        # Default dong_dau position (no specific gate found)
	"suoi_thieng_to_hang_an": Vector2(-2069, 484),          # Default hang_an position (no specific gate found)
	"suoi_thieng_to_doi_tre": Vector2(-2292, -538),         # Default doi_tre position (no specific gate found)
	
	# Doi Tre -> Other Maps (spawn gần cổng tương ứng trong map đích)
	"doi_tre_to_lang_van_lang": Vector2(1100, -1900),       # Position from lang_van_lang teleport config
	"doi_tre_to_rung_nuong": Vector2(753, -1225),           # Default rung_nuong position (no specific gate found)
	"doi_tre_to_dong_dau": Vector2(-1400, -400),            # ✅ FIXED: From TeleportGate_DoiTre.tscn
	"doi_tre_to_hang_an": Vector2(-2069, 484),              # Default hang_an position (no specific gate found)
	"doi_tre_to_suoi_thieng": Vector2(-2069, 484)           # Default suoi_thieng position (no specific gate found)
}

func _ready():
	print("🎯 TeleportPositionMapping system initialized")
	print("📊 Total position mappings: %d" % position_mappings.size())
	_validate_all_mappings()

func _validate_all_mappings():
	"""Kiểm tra tất cả mappings để đảm bảo tính đối xứng"""
	print("\n🔍 Validating teleport position mappings...")
	var maps = ["lang_van_lang", "rung_nuong", "dong_dau", "hang_an", "suoi_thieng", "doi_tre"]
	var missing_mappings = []
	
	for from_map in maps:
		for to_map in maps:
			if from_map != to_map:
				var mapping_key = from_map + "_to_" + to_map
				if not position_mappings.has(mapping_key):
					missing_mappings.append(mapping_key)
	
	if missing_mappings.size() > 0:
		print("⚠️ Missing mappings found:")
		for missing in missing_mappings:
			print("   - %s" % missing)
	else:
		print("✅ All teleport mappings are complete!")
	
	print("📈 Validation complete: %d mappings checked\n" % (maps.size() * (maps.size() - 1)))

func get_accurate_spawn_position(from_map: String, to_map: String) -> Vector2:
	"""Lấy vị trí spawn chính xác cho teleport từ map này sang map khác"""
	var mapping_key = from_map + "_to_" + to_map
	
	if position_mappings.has(mapping_key):
		var position = position_mappings[mapping_key]
		print("🎯 Accurate spawn position for %s: %s" % [mapping_key, position])
		return position
	else:
		print("⚠️ No mapping found for %s, using default" % mapping_key)
		return get_default_spawn_position(to_map)

func get_default_spawn_position(map_name: String) -> Vector2:
	"""Vị trí spawn mặc định cho từng map"""
	var default_positions = {
		"lang_van_lang": Vector2(300, -1900),
		"rung_nuong": Vector2(753, -1225),
		"dong_dau": Vector2(-1421, -429),
		"hang_an": Vector2(-2069, 484),
		"suoi_thieng": Vector2(-2069, 484),
		"doi_tre": Vector2(-2292, -538)
	}
	
	if default_positions.has(map_name):
		return default_positions[map_name]
	else:
		print("⚠️ No default position for map: %s" % map_name)
		return Vector2(0, 0)

func validate_position(position: Vector2) -> bool:
	"""Kiểm tra vị trí có hợp lệ không"""
	return position != Vector2.ZERO

func add_custom_mapping(from_map: String, to_map: String, position: Vector2):
	"""Thêm mapping tùy chỉnh"""
	var key = from_map + "_to_" + to_map
	position_mappings[key] = position
	print("📍 Added custom mapping: %s -> %s" % [key, position])

func list_all_mappings():
	"""Debug: Liệt kê tất cả mappings"""
	print("\n🗺️ All Position Mappings:")
	for key in position_mappings.keys():
		print("   %s: %s" % [key, position_mappings[key]])
	print("📊 Total: %d mappings\n" % position_mappings.size())

func debug_route(from_map: String, to_map: String):
	"""Debug thông tin cho một route cụ thể"""
	var mapping_key = from_map + "_to_" + to_map
	print("\n🔍 DEBUG ROUTE: %s → %s" % [from_map, to_map])
	print("   Mapping key: %s" % mapping_key)
	
	if position_mappings.has(mapping_key):
		print("   ✅ Position: %s" % position_mappings[mapping_key])
	else:
		print("   ❌ No mapping found!")
		var default_pos = get_default_spawn_position(to_map)
		print("   🏠 Default position: %s" % default_pos)
	print("")

func get_all_routes_from_map(map_name: String) -> Dictionary:
	"""Lấy tất cả routes từ một map cụ thể"""
	var routes = {}
	var prefix = map_name + "_to_"
	
	for key in position_mappings.keys():
		if key.begins_with(prefix):
			var target_map = key.replace(prefix, "")
			routes[target_map] = position_mappings[key]
	
	return routes

func get_all_routes_to_map(map_name: String) -> Dictionary:
	"""Lấy tất cả routes đến một map cụ thể"""
	var routes = {}
	var suffix = "_to_" + map_name
	
	for key in position_mappings.keys():
		if key.ends_with(suffix):
			var source_map = key.replace(suffix, "")
			routes[source_map] = position_mappings[key]
	
	return routes
