# Loading System Test Script
# Test cho toàn bộ hệ thống loading của map và scene transitions

extends Node

func _ready():
	print("🧪 === LOADING SYSTEM TEST STARTED ===")
	call_deferred("run_all_tests")

func run_all_tests():
	await get_tree().process_frame
	
	print("\n1️⃣ Testing SceneManager...")
	test_scene_manager()
	
	print("\n2️⃣ Testing Loading Screen Assets...")
	test_loading_screen_assets()
	
	print("\n3️⃣ Testing Teleport Gate Loading...")
	test_teleport_gate_loading()
	
	print("\n4️⃣ Testing Map Controller Loading...")
	test_map_controller_loading()
	
	print("\n5️⃣ Testing Overall Loading Flow...")
	test_overall_loading_flow()
	
	print("\n🧪 === LOADING SYSTEM TEST COMPLETED ===")

func test_scene_manager():
	"""Test SceneManager functionality"""
	print("📋 Checking SceneManager autoload...")
	
	if SceneManager:
		print("✅ SceneManager autoload exists")
		
		# Check required methods
		var required_methods = ["goto_scene", "goto_first_scene", "find_loading_screen", "find_loading_progress_bar"]
		for method in required_methods:
			if SceneManager.has_method(method):
				print("✅ SceneManager.%s() method exists" % method)
			else:
				print("❌ SceneManager.%s() method MISSING" % method)
		
		# Check signals
		if SceneManager.has_signal("scene_loaded"):
			print("✅ SceneManager.scene_loaded signal exists")
		else:
			print("❌ SceneManager.scene_loaded signal MISSING")
			
	else:
		print("❌ SceneManager autoload NOT FOUND")

func test_loading_screen_assets():
	"""Test loading screen assets and scenes"""
	print("📋 Checking loading screen assets...")
	
	# Check loading screen scene
	var loading_screen_path = "res://ui/scenes/loading_screen.tscn"
	if ResourceLoader.exists(loading_screen_path):
		print("✅ Loading screen scene exists: %s" % loading_screen_path)
		
		# Try to load it
		var loading_screen = load(loading_screen_path)
		if loading_screen:
			print("✅ Loading screen scene loads successfully")
		else:
			print("❌ Loading screen scene FAILED to load")
	else:
		print("❌ Loading screen scene NOT FOUND: %s" % loading_screen_path)
	
	# Check loading screen background image
	var bg_image_path = "res://assets/images/background/screens/loading_screen.png"
	if ResourceLoader.exists(bg_image_path):
		print("✅ Loading screen background image exists: %s" % bg_image_path)
	else:
		print("❌ Loading screen background image NOT FOUND: %s" % bg_image_path)
	
	# Check loading screen script
	var loading_script_path = "res://ui/scripts/loading_screen.gd"
	if ResourceLoader.exists(loading_script_path):
		print("✅ Loading screen script exists: %s" % loading_script_path)
	else:
		print("❌ Loading screen script NOT FOUND: %s" % loading_script_path)

func test_teleport_gate_loading():
	"""Test teleport gate loading functionality"""
	print("📋 Checking teleport gate loading...")
	
	# Check teleport gate script
	var teleport_script_path = "res://maps/scripts/teleport_gate.gd"
	if ResourceLoader.exists(teleport_script_path):
		print("✅ Teleport gate script exists: %s" % teleport_script_path)
		
		# Load script and check for loading methods
		var script = load(teleport_script_path)
		if script:
			print("✅ Teleport gate script loads successfully")
			# Note: Can't easily check script methods without instantiating
		else:
			print("❌ Teleport gate script FAILED to load")
	else:
		print("❌ Teleport gate script NOT FOUND: %s" % teleport_script_path)
	
	# Check individual teleport gate scenes
	var gate_scenes = [
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DoiTre.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DongDau.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_HangAn.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_RungNuong.tscn",
		"res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_SuoiThieng.tscn"
	]
	
	for gate_scene in gate_scenes:
		if ResourceLoader.exists(gate_scene):
			print("✅ Gate scene exists: %s" % gate_scene.get_file())
		else:
			print("❌ Gate scene NOT FOUND: %s" % gate_scene.get_file())

func test_map_controller_loading():
	"""Test map controller loading functionality"""
	print("📋 Checking map controller loading...")
	
	# Check lang van lang map controller
	var controller_path = "res://maps/lang_van_lang/scripts/lang_van_lang_map_controller.gd"
	if ResourceLoader.exists(controller_path):
		print("✅ Lang Van Lang map controller exists: %s" % controller_path)
	else:
		print("❌ Lang Van Lang map controller NOT FOUND: %s" % controller_path)
	
	# Check teleport system script
	var teleport_system_path = "res://maps/lang_van_lang/scripts/lang_van_lang_teleport_system.gd"
	if ResourceLoader.exists(teleport_system_path):
		print("✅ Lang Van Lang teleport system exists: %s" % teleport_system_path)
	else:
		print("❌ Lang Van Lang teleport system NOT FOUND: %s" % teleport_system_path)

func test_overall_loading_flow():
	"""Test the overall loading flow"""
	print("📋 Checking overall loading flow...")
	
	# Check main map scene
	var main_map_path = "res://maps/lang_van_lang/scenes/lang_van_lang.tscn"
	if ResourceLoader.exists(main_map_path):
		print("✅ Main map scene exists: %s" % main_map_path)
		
		# Try to load the scene
		var main_scene = load(main_map_path)
		if main_scene:
			print("✅ Main map scene loads successfully")
		else:
			print("❌ Main map scene FAILED to load")
	else:
		print("❌ Main map scene NOT FOUND: %s" % main_map_path)
	
	# Check target scenes for teleportation
	var target_scenes = [
		"res://maps/doi_tre/scenes/doi_tre.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn", 
		"res://maps/hang_an/scenes/hang_an.tscn",
		"res://maps/rung_nuong/scenes/rung_nuong.tscn",
		"res://maps/suoi_thieng/scenes/suoi_thieng.tscn"
	]
	
	for scene_path in target_scenes:
		if ResourceLoader.exists(scene_path):
			print("✅ Target scene exists: %s" % scene_path.get_file())
		else:
			print("❌ Target scene NOT FOUND: %s" % scene_path.get_file())

func test_loading_screen_performance():
	"""Test loading screen performance"""
	print("📋 Testing loading screen performance...")
	
	if SceneManager and SceneManager.has_method("goto_scene"):
		print("✅ SceneManager ready for performance test")
		print("🔄 Note: Actual performance test requires running the game")
	else:
		print("❌ SceneManager not available for performance test")
