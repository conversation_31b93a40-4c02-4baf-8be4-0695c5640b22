extends Node
class_name StateMachine

@export var initial_state: State

var current_state: State
var states: Dictionary = {}

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	# Thê<PERSON> null check tr<PERSON><PERSON><PERSON> khi get_children()
	if self and is_instance_valid(self):
		for child in get_children():
			if child is State:
				states[child.name.to_lower()] = child
				child.Transitioned.connect(on_child_transition)
	
	if initial_state:
		initial_state.enter()
		current_state = initial_state

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	if current_state:
		current_state.update(delta)
	
func _physics_process(delta: float) -> void:
	if current_state:
		current_state.physic_update(delta)

func on_child_transition(state, new_state_name):
	if state != current_state:
		return
		
	var new_state = states.find_key(new_state_name.tolower())
	if !new_state:
		return
		
	if current_state:
		current_state.exit()
	
	new_state.enter()
	
	current_state = new_state
	
