[gd_scene load_steps=8 format=3 uid="uid://dk45l678rqkr1"]

[ext_resource type="Script" path="res://enemy/scripts/enemy.gd" id="1_bvxyc"]
[ext_resource type="SpriteFrames" uid="uid://dunuud7tbqfb5" path="res://enemy/skins/boss.tres" id="2_xqlhn"]
[ext_resource type="Script" path="res://enemy/scripts/class_manager.gd" id="3_4mpbc"]
[ext_resource type="PackedScene" uid="uid://irr1k0ncg2qk" path="res://enemy/scenes/HealthBar.tscn" id="4_jp3rx"]
[ext_resource type="Shape2D" uid="uid://dfauwmepagapu" path="res://enemy/hitboxes/boss_hitbox.tres" id="4_y1cog"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_0km8q"]
radius = 7.0
height = 22.0

[sub_resource type="CircleShape2D" id="CircleShape2D_qqhsc"]
radius = 179.547

[node name="Enemy" type="CharacterBody2D"]
scale = Vector2(2, 2)
collision_layer = 2
collision_mask = 413
script = ExtResource("1_bvxyc")
attack_range = 72.0
attack_cooldown = 2.0

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = ExtResource("2_xqlhn")
animation = &"hurt"
autoplay = "idle"
frame = 3
frame_progress = 1.0

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_0km8q")

[node name="ClassManager" type="Node" parent="."]
script = ExtResource("3_4mpbc")

[node name="DetectZone" type="Area2D" parent="." groups=["enemy_group"]]
collision_layer = 2
collision_mask = 129

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectZone"]
shape = SubResource("CircleShape2D_qqhsc")

[node name="Hitbox" type="Area2D" parent="."]
collision_layer = 8
collision_mask = 129

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
position = Vector2(22.25, -1.75)
shape = ExtResource("4_y1cog")
disabled = true

[node name="HealthBar" parent="." instance=ExtResource("4_jp3rx")]
offset_left = -8.0
offset_top = -13.0
offset_right = -8.0
offset_bottom = -13.0
scale = Vector2(0.15, 0.15)
