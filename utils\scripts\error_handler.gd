# ErrorHandler.gd - Centralized Error Handling System
extends Node

# Error levels
enum ErrorLevel {
	DEBUG,
	INFO,
	WARNING,
	ERROR,
	CRITICAL
}

# Error categories
enum ErrorCategory {
	SYSTEM,
	SCENE_LOADING,
	RESOURCE_LOADING,
	NETWORK,
	INPUT,
	GAMEPLAY,
	UI,
	TELEPORT,
	SAVE_LOAD
}

# Error tracking
var error_log: Array[Dictionary] = []
var max_log_entries: int = 1000
var error_counts: Dictionary = {}

# Settings
var log_to_file: bool = true
var show_debug_overlay: bool = false
var crash_on_critical: bool = false

# Signals
signal error_occurred(error_data: Dictionary)
signal critical_error_occurred(error_data: Dictionary)

func _ready():
	print("🛡️ ErrorHandler initialized")
	
	# Connect to <PERSON><PERSON>'s built-in error signals if available
	if Engine.has_signal("error_occurred"):
		Engine.error_occurred.connect(_on_engine_error)

func log_error(level: ErrorLevel, category: ErrorCategory, message: String, context: Dictionary = {}) -> void:
	"""Log an error with detailed information"""
	var timestamp = Time.get_unix_time_from_system()
	var error_data = {
		"timestamp": timestamp,
		"level": ErrorLevel.keys()[level],
		"category": ErrorCategory.keys()[category],
		"message": message,
		"context": context,
		"stack_trace": get_stack() if level >= ErrorLevel.ERROR else []
	}
	
	# Add to log
	error_log.append(error_data)
	
	# Maintain log size
	if error_log.size() > max_log_entries:
		error_log.pop_front()
	
	# Update error counts
	var key = "%s_%s" % [ErrorLevel.keys()[level], ErrorCategory.keys()[category]]
	error_counts[key] = error_counts.get(key, 0) + 1
	
	# Print to console with formatting
	_print_formatted_error(error_data)
	
	# Log to file if enabled
	if log_to_file:
		_log_to_file(error_data)
	
	# Emit signals
	error_occurred.emit(error_data)
	
	if level == ErrorLevel.CRITICAL:
		critical_error_occurred.emit(error_data)
		if crash_on_critical:
			_handle_critical_error(error_data)

func _print_formatted_error(error_data: Dictionary) -> void:
	"""Print formatted error to console"""
	var level_icons = {
		"DEBUG": "🔍",
		"INFO": "ℹ️",
		"WARNING": "⚠️",
		"ERROR": "❌",
		"CRITICAL": "🚨"
	}
	
	var category_icons = {
		"SYSTEM": "⚙️",
		"SCENE_LOADING": "🎬",
		"RESOURCE_LOADING": "📦",
		"NETWORK": "🌐",
		"INPUT": "⌨️",
		"GAMEPLAY": "🎮",
		"UI": "🖼️",
		"TELEPORT": "🌀",
		"SAVE_LOAD": "💾"
	}
	
	var icon = level_icons.get(error_data.level, "❓")
	var cat_icon = category_icons.get(error_data.category, "📋")
	
	print("%s %s [%s] %s" % [icon, cat_icon, error_data.category, error_data.message])
	
	if error_data.context.size() > 0:
		print("   Context: %s" % error_data.context)

func _log_to_file(error_data: Dictionary) -> void:
	"""Log error to file"""
	var log_file_path = "user://error_log.txt"
	var file = FileAccess.open(log_file_path, FileAccess.WRITE)
	if file:
		var timestamp_str = Time.get_datetime_string_from_unix_time(error_data.timestamp)
		var log_line = "[%s] %s %s: %s\n" % [
			timestamp_str,
			error_data.level,
			error_data.category,
			error_data.message
		]
		file.store_string(log_line)
		file.close()

func _handle_critical_error(error_data: Dictionary) -> void:
	"""Handle critical errors"""
	print("🚨 CRITICAL ERROR DETECTED - INITIATING EMERGENCY PROCEDURES")
	
	# Save emergency state
	_save_emergency_state()
	
	# Show error dialog
	_show_critical_error_dialog(error_data)
	
	if crash_on_critical:
		get_tree().quit(1)

func _save_emergency_state() -> void:
	"""Save emergency game state before potential crash"""
	var emergency_data = {
		"timestamp": Time.get_unix_time_from_system(),
		"current_scene": get_tree().current_scene.scene_file_path if get_tree().current_scene else "unknown",
		"player_position": Vector2.ZERO,
		"error_counts": error_counts
	}
	
	# Try to get player position
	var player = get_tree().get_first_node_in_group("player")
	if player:
		emergency_data.player_position = player.global_position
	
	var file = FileAccess.open("user://emergency_state.json", FileAccess.WRITE)
	if file:
		file.store_string(JSON.stringify(emergency_data))
		file.close()
		print("💾 Emergency state saved")

func _show_critical_error_dialog(error_data: Dictionary) -> void:
	"""Show critical error dialog to user"""
	var dialog = AcceptDialog.new()
	dialog.title = "Critical Error"
	dialog.dialog_text = "A critical error has occurred:\n\n%s\n\nThe game will attempt to recover." % error_data.message
	get_tree().root.add_child(dialog)
	dialog.popup_centered()

func _on_engine_error(error_message: String) -> void:
	"""Handle Godot engine errors"""
	log_error(ErrorLevel.ERROR, ErrorCategory.SYSTEM, "Engine Error: %s" % error_message)

# Convenience methods for common error types
func log_debug(category: ErrorCategory, message: String, context: Dictionary = {}) -> void:
	log_error(ErrorLevel.DEBUG, category, message, context)

func log_info(category: ErrorCategory, message: String, context: Dictionary = {}) -> void:
	log_error(ErrorLevel.INFO, category, message, context)

func log_warning(category: ErrorCategory, message: String, context: Dictionary = {}) -> void:
	log_error(ErrorLevel.WARNING, category, message, context)

func log_error_simple(category: ErrorCategory, message: String, context: Dictionary = {}) -> void:
	log_error(ErrorLevel.ERROR, category, message, context)

func log_critical(category: ErrorCategory, message: String, context: Dictionary = {}) -> void:
	log_error(ErrorLevel.CRITICAL, category, message, context)

# Scene loading helpers
func safe_load_scene(path: String) -> PackedScene:
	"""Safely load a scene with error handling"""
	if not ResourceLoader.exists(path):
		log_error_simple(ErrorCategory.SCENE_LOADING, "Scene file does not exist", {"path": path})
		return null
	
	var scene = load(path)
	if not scene:
		log_error_simple(ErrorCategory.SCENE_LOADING, "Failed to load scene", {"path": path})
		return null
	
	log_debug(ErrorCategory.SCENE_LOADING, "Scene loaded successfully", {"path": path})
	return scene

func safe_instantiate_scene(scene: PackedScene, context: String = "") -> Node:
	"""Safely instantiate a scene with error handling"""
	if not scene:
		log_error_simple(ErrorCategory.SCENE_LOADING, "Cannot instantiate null scene", {"context": context})
		return null
	
	var instance = scene.instantiate()
	if not instance:
		log_error_simple(ErrorCategory.SCENE_LOADING, "Failed to instantiate scene", {"context": context})
		return null
	
	log_debug(ErrorCategory.SCENE_LOADING, "Scene instantiated successfully", {"context": context})
	return instance

# Resource loading helpers
func safe_load_resource(path: String, expected_type: String = "") -> Resource:
	"""Safely load a resource with error handling"""
	if not ResourceLoader.exists(path):
		log_error_simple(ErrorCategory.RESOURCE_LOADING, "Resource file does not exist", {"path": path, "expected_type": expected_type})
		return null
	
	var resource = load(path)
	if not resource:
		log_error_simple(ErrorCategory.RESOURCE_LOADING, "Failed to load resource", {"path": path, "expected_type": expected_type})
		return null
	
	if expected_type != "" and not resource.get_class() == expected_type:
		log_warning(ErrorCategory.RESOURCE_LOADING, "Resource type mismatch", {
			"path": path,
			"expected": expected_type,
			"actual": resource.get_class()
		})
	
	log_debug(ErrorCategory.RESOURCE_LOADING, "Resource loaded successfully", {"path": path, "type": resource.get_class()})
	return resource

# System validation
func validate_autoload_system(system_name: String) -> bool:
	"""Validate that an autoload system is properly loaded"""
	var system = get_node_or_null("/root/" + system_name)
	if not system:
		log_error_simple(ErrorCategory.SYSTEM, "Autoload system not found", {"system": system_name})
		return false
	
	log_debug(ErrorCategory.SYSTEM, "Autoload system validated", {"system": system_name})
	return true

# Debug and reporting
func get_error_summary() -> Dictionary:
	"""Get summary of errors"""
	return {
		"total_errors": error_log.size(),
		"error_counts": error_counts,
		"recent_errors": error_log.slice(-10) if error_log.size() > 10 else error_log
	}

func clear_error_log() -> void:
	"""Clear the error log"""
	error_log.clear()
	error_counts.clear()
	log_info(ErrorCategory.SYSTEM, "Error log cleared")

func export_error_log() -> String:
	"""Export error log as JSON string"""
	return JSON.stringify({
		"export_time": Time.get_unix_time_from_system(),
		"errors": error_log,
		"summary": get_error_summary()
	})

func _exit_tree():
	print("🛡️ ErrorHandler shutting down")
	if log_to_file and error_log.size() > 0:
		_log_to_file({
			"timestamp": Time.get_unix_time_from_system(),
			"level": "INFO",
			"category": "SYSTEM",
			"message": "ErrorHandler shutdown - %d errors logged" % error_log.size(),
			"context": get_error_summary()
		})
