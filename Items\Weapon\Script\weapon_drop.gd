# Script cho vũ khí rơi ra (weapon_drop.gd)
extends RigidBody2D

@export var blink_start_offset: float = 2
@export var blink_interval: float = 0.15
@export var weapon_type: String = "Sword"  # "Sword", "Spear", "arrow", "Armor", "Pant"
@export var spawn_time: float = 10.0  # Thời gian tồn tại trước khi biến mất (lâu hơn potion)

@onready var arrow: Polygon2D = $ArrowIndicator
@onready var icon: Sprite2D = $Icon
@onready var shape: CollisionShape2D = $CollisionShape2D
@onready var despawn_timer: Timer = $DespawnTimer
@onready var click_area: Area2D = $ClickArea
@onready var pickup_area: Area2D = $PickupArea

# Biến để theo dõi trạng thái nhấp nháy
var is_blinking: bool = false
var blink_timer: float = 0.0

# Preload các texture cho vũ khí
var weapon_textures = {
	"Sword": preload("res://assets/images/item/Weapon/kiem.png"),
	"Spear": preload("res://assets/images/item/Weapon/thuong.png"),
	"arrow": preload("res://assets/images/item/Weapon/cung.png"),
	"Armor": preload("res://assets/images/item/Weapon/ao.png"),
	"Pant": preload("res://assets/images/item/Weapon/giay.png")  # Sử dụng giày thay vì quần nếu không có
}

func _ready() -> void:
	# Thiết lập texture dựa trên loại vũ khí
	if weapon_type in weapon_textures:
		icon.texture = weapon_textures[weapon_type]
	else:
		# Fallback texture nếu không tìm thấy
		icon.texture = weapon_textures["Sword"]
		print("WARNING: Unknown weapon type: " + weapon_type + ", using Sword texture")

	# Thiết lập timer để despawn
	if despawn_timer:
		despawn_timer.wait_time = spawn_time
		despawn_timer.one_shot = true
		if not despawn_timer.is_connected("timeout", Callable(self, "_on_despawn_timer_timeout")):
			despawn_timer.timeout.connect(_on_despawn_timer_timeout)
		despawn_timer.start()
	else:
		printerr("Node 'DespawnTimer' not found as a child of ", name, ". Item will not despawn automatically.")

	# Ẩn mũi tên chỉ dẫn ban đầu
	if arrow:
		arrow.visible = false
	else:
		printerr("Node 'ArrowIndicator' không tìm thấy!")

	# Thiết lập kết nối signal cho click
	if click_area:
		click_area.input_pickable = true
		if not click_area.is_connected("input_event", Callable(self, "_on_item_clicked")):
			click_area.input_event.connect(_on_item_clicked)
	else:
		printerr("Node 'ClickArea' not found!")

	# Thiết lập kết nối signal cho vùng tự động nhặt
	if pickup_area:
		if not pickup_area.is_connected("body_entered", Callable(self, "_on_pickup_area_body_entered")):
			pickup_area.body_entered.connect(_on_pickup_area_body_entered)
	else:
		printerr("Node 'PickupArea' not found!")

func _on_despawn_timer_timeout() -> void:
	# Xóa vũ khí khi hết thời gian
	queue_free()

func _physics_process(delta: float) -> void:
	# Logic nhấp nháy khi sắp biến mất
	if not is_blinking and despawn_timer and despawn_timer.time_left > 0 and despawn_timer.time_left <= blink_start_offset:
		is_blinking = true
		blink_timer = 0.0

	if is_blinking:
		blink_timer += delta
		if blink_timer >= blink_interval:
			if icon:
				icon.visible = not icon.visible
			blink_timer -= blink_interval

func _on_item_clicked(_viewport: Viewport, event: InputEvent, _shape_idx: int) -> void:
	# Xử lý khi người chơi click vào vũ khí
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.is_pressed():
		# Hiển thị mũi tên chỉ dẫn
		if arrow:
			arrow.visible = true

		# Tìm player trong scene
		var player = get_tree().get_first_node_in_group("player")
		if player:
			# Kiểm tra khoảng cách với player
			var dist = global_position.distance_to(player.global_position)
			if dist < 50:  # Nếu player đủ gần
				_collect_weapon()

		# Ngăn sự kiện lan truyền
		get_viewport().set_input_as_handled()

# Xử lý khi người chơi đi vào vùng tự động nhặt
func _on_pickup_area_body_entered(body: Node) -> void:
	# Kiểm tra xem body có phải là player không
	if body.is_in_group("player"):
		# Hiệu ứng nhặt vũ khí
		if arrow:
			arrow.visible = true

		# Nhặt vũ khí
		_collect_weapon()

func _collect_weapon() -> void:
	# Tìm player để thêm vũ khí vào inventory
	var player = get_tree().get_first_node_in_group("player")
	if player and player.has_method("add_weapon_to_inventory"):
		player.add_weapon_to_inventory(weapon_type)
		print("Collected " + weapon_type + " weapon")
	else:
		# Fallback: lưu vào một hệ thống inventory toàn cục
		var inventory_manager = get_node_or_null("/root/InventoryManager")
		if inventory_manager and inventory_manager.has_method("add_weapon"):
			inventory_manager.add_weapon(weapon_type)
			print("Collected " + weapon_type + " weapon (via InventoryManager)")
		else:
			print("WARNING: Could not find player or InventoryManager to add weapon")

	# Hiệu ứng khi nhặt vũ khí
	_play_pickup_effect()

	# Xóa vũ khí sau khi nhặt
	queue_free()

# Hiệu ứng khi nhặt vũ khí
func _play_pickup_effect() -> void:
	# Tạo hiệu ứng hình ảnh (có thể thêm particle effect ở đây)
	# Ví dụ: tạo một tween để vũ khí bay lên và mờ dần
	var tween = create_tween()
	tween.tween_property(self, "position", position + Vector2(0, -30), 0.3)
	tween.parallel().tween_property(self, "modulate", Color(1, 1, 1, 0), 0.3)

	# Phát âm thanh (nếu có)
	# Ví dụ: AudioStreamPlayer.play()
