[gd_scene load_steps=10 format=3 uid="uid://bkn5seficjirh"]

[ext_resource type="Script" path="res://player/scripts/player.gd" id="1_guawr"]
[ext_resource type="SpriteFrames" uid="uid://dq8aesv2rdpw3" path="res://classes/frames/swordman_frame.tres" id="2_vc51r"]
[ext_resource type="SpriteFrames" uid="uid://b68brhb20y53t" path="res://ranks/frames/beginner_frame.tres" id="3_hhkyq"]
[ext_resource type="Script" path="res://player/scripts/camera_2d.gd" id="4_yif1l"]
[ext_resource type="PackedScene" uid="uid://wa4tskbblp4a" path="res://hud_progress/scenes/hud.tscn" id="5_24aco"]
[ext_resource type="Script" path="res://abilities/scripts/ability_manager.gd" id="5_d0xsd"]
[ext_resource type="Script" path="res://hud_progress/scripts/etc_tab.gd" id="6_p6hhs"]
[ext_resource type="PackedScene" uid="uid://hk3rx47bobty" path="res://hud_progress/scenes/skills_slots.tscn" id="7_2pmfh"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_5rubn"]
radius = 6.0
height = 22.0

[node name="Player" type="CharacterBody2D" groups=["ally_group", "player"]]
collision_mask = 30
script = ExtResource("1_guawr")

[node name="DevineMission" type="AnimatedSprite2D" parent="."]
sprite_frames = ExtResource("2_vc51r")
animation = &"slash_4"

[node name="AuraHolder" type="AnimatedSprite2D" parent="."]
position = Vector2(6, -9)
sprite_frames = ExtResource("3_hhkyq")
animation = &"burst"
speed_scale = 1.5

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_5rubn")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(2, 2)
limit_smoothed = true
position_smoothing_speed = 1.0
editor_draw_limits = true
script = ExtResource("4_yif1l")

[node name="AbilityManager" type="Node" parent="."]
script = ExtResource("5_d0xsd")

[node name="CanvasLayer2" type="CanvasLayer" parent="."]
visible = false
script = ExtResource("6_p6hhs")

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = 0

[node name="HUD" parent="CanvasLayer" instance=ExtResource("5_24aco")]
offset_right = 0.0
offset_bottom = 0.0
scale = Vector2(0.75, 0.75)
script = null

[node name="SkillsSlots" parent="CanvasLayer" instance=ExtResource("7_2pmfh")]
anchors_preset = 4
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 0.5
offset_top = -138.0
offset_bottom = -138.0
grow_horizontal = 1
scale = Vector2(0.75, 0.75)
