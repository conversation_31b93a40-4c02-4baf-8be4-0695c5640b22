# SpatialTeleportSystem.gd - New Spatial Consistency Teleport System
extends Node
class_name SpatialTeleportSystem

# 🗺️ SPATIAL CONSISTENCY POSITION MAPPINGS
# This system ensures players spawn at logically corresponding positions
# based on spatial relationships between maps

# 🎯 SPATIAL GATE MAPPINGS (using new directional naming)
var spatial_position_mappings: Dictionary = {
	# <PERSON> (Hub) -> Other Maps
	"lang_van_lang_left_exit": Vector2(-2000, -400),         # Spawn at RIGHT entrance of Dong Dau
	"lang_van_lang_right_exit": Vector2(-2000, -1700),       # Spawn at LEFT entrance of Hang An
	"lang_van_lang_north_exit": Vector2(750, -1200),         # Spawn at SOUTH entrance of Rung Nuong
	"lang_van_lang_south_exit": Vector2(-2000, 200),         # Spawn at NORTH entrance of Suoi Thieng
	
	# Dong Dau -> Other Maps
	"dong_dau_left_exit": Vector2(1400, -400),               # Spawn at RIGHT entrance of Doi Tre
	"dong_dau_right_exit": Vector2(300, -2000),              # Spawn at LEFT entrance of Lang Van Lang
	"dong_dau_north_exit": Vector2(750, -1225),              # Spawn at SOUTH entrance of Rung Nuong (FIXED: ground level)
	
	# Doi Tre -> Other Maps
	"doi_tre_right_exit": Vector2(-2400, -400),              # Spawn at LEFT entrance of Dong Dau
	
	# Rung Nuong -> Other Maps
	"rung_nuong_left_exit": Vector2(5200, -350),             # Spawn at RIGHT entrance of Dong Dau
	"rung_nuong_right_exit": Vector2(-2000, 400),            # Spawn at LEFT entrance of Hang An (FIXED: ground level)
	"rung_nuong_south_exit": Vector2(2000, -2000),           # Spawn at NORTH entrance of Lang Van Lang
	
	# Hang An -> Other Maps
	"hang_an_left_exit": Vector2(1800, -1225),               # Spawn at RIGHT entrance of Rung Nuong (FIXED: ground level)
	"hang_an_south_exit": Vector2(3700, -2000),              # Spawn at RIGHT entrance of Lang Van Lang
	
	# Suoi Thieng -> Other Maps
	"suoi_thieng_north_exit": Vector2(2500, -2000),          # Spawn at SOUTH entrance of Lang Van Lang
	"suoi_thieng2_north_exit": Vector2(2500, -2000)          # Second gate, same destination
}

# 🎯 GATE POSITION MAPPINGS (where gates are located within each map)
var gate_positions: Dictionary = {
	# Lang Van Lang (Hub) gates
	"lang_van_lang_left_exit": Vector2(255, -2069),          # LEFT side of Lang Van Lang
	"lang_van_lang_right_exit": Vector2(3739, -2055),        # RIGHT side of Lang Van Lang
	"lang_van_lang_north_exit": Vector2(2000, -2100),        # NORTH side of Lang Van Lang
	"lang_van_lang_south_exit": Vector2(2500, -1900),        # SOUTH side of Lang Van Lang
	
	# Dong Dau gates
	"dong_dau_left_exit": Vector2(-2500, -435),              # LEFT side of Dong Dau
	"dong_dau_right_exit": Vector2(-2122, -296),             # RIGHT side of Dong Dau
	"dong_dau_north_exit": Vector2(1400, -1500),             # NORTH side of Dong Dau
	
	# Doi Tre gates
	"doi_tre_right_exit": Vector2(-2798, -229),              # RIGHT side of Doi Tre (going east)
	
	# Rung Nuong gates
	"rung_nuong_left_exit": Vector2(-584, -1350),            # LEFT side of Rung Nuong
	"rung_nuong_right_exit": Vector2(2003, -1343),           # RIGHT side of Rung Nuong
	"rung_nuong_south_exit": Vector2(1500, -1300),           # SOUTH side of Rung Nuong
	
	# Hang An gates
	"hang_an_left_exit": Vector2(-1000, -1700),              # LEFT side of Hang An
	"hang_an_south_exit": Vector2(0, -1500),                 # SOUTH side of Hang An
	
	# Suoi Thieng gates
	"suoi_thieng_north_exit": Vector2(-3328, 471),           # NORTH side of Suoi Thieng (gate 1)
	"suoi_thieng2_north_exit": Vector2(456, 531)             # NORTH side of Suoi Thieng (gate 2)
}

# 🔄 SPATIAL CONSISTENCY FUNCTIONS
func get_spawn_position(gate_id: String) -> Vector2:
	"""Get the spawn position for a specific gate ID"""
	if spatial_position_mappings.has(gate_id):
		return spatial_position_mappings[gate_id]
	else:
		print("⚠️ No spatial mapping found for gate: %s" % gate_id)
		return Vector2.ZERO

func get_gate_position(gate_id: String) -> Vector2:
	"""Get the position where a gate should be placed in its map"""
	if gate_positions.has(gate_id):
		return gate_positions[gate_id]
	else:
		print("⚠️ No gate position found for: %s" % gate_id)
		return Vector2.ZERO

func validate_spatial_consistency() -> bool:
	"""Validate that all spatial connections are reciprocal"""
	var valid = true
	
	# Check each gate has a corresponding return gate
	var connections = {
		"lang_van_lang_left_exit": "dong_dau_right_exit",
		"dong_dau_right_exit": "lang_van_lang_left_exit",
		"dong_dau_left_exit": "doi_tre_right_exit",
		"doi_tre_right_exit": "dong_dau_left_exit",
		"lang_van_lang_right_exit": "hang_an_south_exit",
		"hang_an_south_exit": "lang_van_lang_right_exit",
		"lang_van_lang_north_exit": "rung_nuong_south_exit",
		"rung_nuong_south_exit": "lang_van_lang_north_exit",
		"rung_nuong_left_exit": "dong_dau_north_exit",
		"dong_dau_north_exit": "rung_nuong_left_exit",
		"rung_nuong_right_exit": "hang_an_left_exit",
		"hang_an_left_exit": "rung_nuong_right_exit"
	}
	
	for gate_id in connections:
		var reciprocal_gate = connections[gate_id]
		if not spatial_position_mappings.has(gate_id):
			print("❌ Missing gate mapping: %s" % gate_id)
			valid = false
		if not spatial_position_mappings.has(reciprocal_gate):
			print("❌ Missing reciprocal gate mapping: %s" % reciprocal_gate)
			valid = false
	
	return valid

func get_direction_from_gate_id(gate_id: String) -> String:
	"""Extract direction from gate ID"""
	if "_left_" in gate_id:
		return "left"
	elif "_right_" in gate_id:
		return "right"
	elif "_north_" in gate_id:
		return "north"
	elif "_south_" in gate_id:
		return "south"
	else:
		return "unknown"

func get_map_from_gate_id(gate_id: String) -> String:
	"""Extract map name from gate ID"""
	var parts = gate_id.split("_")
	if parts.size() >= 2:
		return parts[0] + "_" + parts[1]  # e.g., "lang_van_lang" from "lang_van_lang_left_exit"
	return "unknown"

# 📊 DEBUG FUNCTIONS
func print_all_mappings():
	"""Print all spatial mappings for debugging"""
	print("🗺️ SPATIAL POSITION MAPPINGS:")
	for gate_id in spatial_position_mappings:
		var pos = spatial_position_mappings[gate_id]
		var direction = get_direction_from_gate_id(gate_id)
		var map = get_map_from_gate_id(gate_id)
		print("  %s (%s %s) → spawn at %s" % [gate_id, map, direction, pos])

func print_gate_positions():
	"""Print all gate positions for debugging"""
	print("🚪 GATE POSITIONS:")
	for gate_id in gate_positions:
		var pos = gate_positions[gate_id]
		var direction = get_direction_from_gate_id(gate_id)
		var map = get_map_from_gate_id(gate_id)
		print("  %s (%s %s) at position %s" % [gate_id, map, direction, pos])

# 🎯 INTEGRATION FUNCTIONS
func update_teleport_gate(gate: TeleportGate, gate_id: String):
	"""Update a teleport gate with spatial consistency data"""
	if not gate:
		print("❌ Invalid gate provided")
		return
	
	# Update gate ID
	gate.gate_id = gate_id
	
	# Update spawn position
	var spawn_pos = get_spawn_position(gate_id)
	if spawn_pos != Vector2.ZERO:
		gate.target_position = spawn_pos
		print("✅ Updated %s spawn position to %s" % [gate_id, spawn_pos])
	
	# Update gate position if needed
	var gate_pos = get_gate_position(gate_id)
	if gate_pos != Vector2.ZERO:
		gate.position = gate_pos
		print("✅ Updated %s gate position to %s" % [gate_id, gate_pos])

func _ready():
	print("🌀 SpatialTeleportSystem initialized")
	print("📊 Total spatial mappings: %d" % spatial_position_mappings.size())
	print("🚪 Total gate positions: %d" % gate_positions.size())
	
	# Validate consistency
	if validate_spatial_consistency():
		print("✅ Spatial consistency validation passed")
	else:
		print("❌ Spatial consistency validation failed")
