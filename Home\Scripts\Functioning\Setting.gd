extends Node2D

# ----- Properties -----
var volume_button: Button
var notification_button: Button
var instruct_button: Button
var original_scale: Vector2 = Vector2(1, 1)
var default_color: Color = Color.WHITE
var hover_color: Color = Color(1, 1, 0) # Yellow color (RGB: 1,1,0)

# ----- Lifecycle Methods -----
func _ready() -> void:
	volume_button = get_node("VBoxContainer/Volume")
	notification_button = get_node("VBoxContainer/Notification")
	instruct_button = get_node("VBoxContainer/Instruct")
	
	setup_button(volume_button, "res://Home/scenes/Voice.tscn")
	setup_button(notification_button, "res://Home/scenes/ThongBao.tscn")
	setup_button(instruct_button, "res://Home/scenes/Instruct.tscn")


# ----- Button Setup Methods -----
func setup_button(button: Button, scene_path: String) -> void:
	button.mouse_entered.connect(func(): on_mouse_enter(button))
	button.mouse_exited.connect(func(): on_mouse_exit(button))
	button.pressed.connect(func(): on_button_pressed(button, scene_path))

# ----- Event Handlers -----
func on_mouse_enter(button: Button) -> void:
	button.modulate = hover_color # Change color to yellow

func on_mouse_exit(button: Button) -> void:
	button.modulate = default_color # Return to white color
	button.scale = original_scale # Reset size

func on_button_pressed(button: Button, scene_path: String) -> void:
	button.scale = original_scale * 1.2 # Enlarge
	get_tree().change_scene_to_file(scene_path)

func on_exit_pressed() -> void:
	get_tree().quit() 
