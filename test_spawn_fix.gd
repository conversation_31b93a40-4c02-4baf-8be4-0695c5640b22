# TestSpawnFix.gd - Test the spawn position fixes
extends Node

func _ready():
	print("🧪 === TESTING SPAWN POSITION FIXES ===")
	call_deferred("test_all_fixes")

func test_all_fixes():
	print("\n🔍 Testing all spawn position fixes...")
	
	# Test 1: Validate spatial teleport system
	test_spatial_system_positions()
	
	# Test 2: Test teleport gate targets
	test_teleport_gate_targets()
	
	# Test 3: Simulate teleportation scenarios
	simulate_teleportation_scenarios()
	
	# Test 4: Validate map controller safety
	test_map_controller_safety()
	
	generate_test_report()

func test_spatial_system_positions():
	print("\n🗺️ TESTING SPATIAL SYSTEM POSITIONS...")
	
	var spatial_system = SpatialTeleportSystem.new()
	add_child(spatial_system)
	
	# Test critical routes that were problematic
	var critical_routes = [
		"dong_dau_north_exit",      # To rung_nuong
		"rung_nuong_right_exit",    # To hang_an
		"hang_an_left_exit"         # To rung_nuong
	]
	
	for route in critical_routes:
		var spawn_pos = spatial_system.get_spawn_position(route)
		var is_safe = _is_position_safe(spawn_pos)
		var status = "✅ SAFE" if is_safe else "❌ UNSAFE"
		
		print("   %s: %s %s" % [route, spawn_pos, status])
		
		if not is_safe:
			print("     ⚠️ Position may cause player to fall from sky!")
	
	spatial_system.queue_free()

func test_teleport_gate_targets():
	print("\n🚪 TESTING TELEPORT GATE TARGETS...")
	
	# Test teleport gates in the scene
	var teleport_gates = get_tree().get_nodes_in_group("teleport_gates")
	print("Found %d teleport gates to test" % teleport_gates.size())
	
	for gate in teleport_gates:
		if gate.has_method("get") and gate.get("target_position"):
			var target_pos = gate.target_position
			var gate_id = gate.gate_id if gate.has_method("get") and gate.get("gate_id") else "unknown"
			var is_safe = _is_position_safe(target_pos)
			var status = "✅ SAFE" if is_safe else "❌ UNSAFE"
			
			print("   %s: %s %s" % [gate_id, target_pos, status])
			
			if not is_safe:
				print("     ⚠️ Target position may cause falling!")

func simulate_teleportation_scenarios():
	print("\n🎮 SIMULATING TELEPORTATION SCENARIOS...")
	
	# Simulate problematic teleportation routes
	var scenarios = [
		{
			"name": "Lang Van Lang → Rung Nuong",
			"spawn_pos": Vector2(1200, -1225),
			"expected": "Player should spawn on ground in Rung Nuong"
		},
		{
			"name": "Dong Dau → Rung Nuong", 
			"spawn_pos": Vector2(700, -1225),
			"expected": "Player should spawn on ground in Rung Nuong"
		},
		{
			"name": "Rung Nuong → Hang An",
			"spawn_pos": Vector2(-2000, 400),
			"expected": "Player should spawn on ground in Hang An"
		},
		{
			"name": "Hang An → Rung Nuong",
			"spawn_pos": Vector2(1800, -1225),
			"expected": "Player should spawn on ground in Rung Nuong"
		}
	]
	
	for scenario in scenarios:
		var pos = scenario.spawn_pos
		var is_safe = _is_position_safe(pos)
		var status = "✅ SAFE" if is_safe else "❌ UNSAFE"
		
		print("   %s:" % scenario.name)
		print("     Position: %s %s" % [pos, status])
		print("     Expected: %s" % scenario.expected)
		
		if not is_safe:
			print("     ⚠️ WARNING: May cause player to fall from sky!")

func test_map_controller_safety():
	print("\n🎮 TESTING MAP CONTROLLER SAFETY...")
	
	# Test default positions used by map controllers
	var controller_defaults = {
		"rung_nuong": Vector2(753, -1225),
		"hang_an": Vector2(-2069, 484)
	}
	
	for map_name in controller_defaults:
		var pos = controller_defaults[map_name]
		var is_safe = _is_position_safe(pos)
		var status = "✅ SAFE" if is_safe else "❌ UNSAFE"
		
		print("   %s default: %s %s" % [map_name, pos, status])
		
		if not is_safe:
			print("     ⚠️ Default position may be problematic!")

func _is_position_safe(pos: Vector2) -> bool:
	"""Check if a position is safe (not too high in the air)"""
	# Positions with Y < -1500 are likely too high and may cause falling
	if pos.y < -1500:
		return false
	
	# Positions too far horizontally may be outside map bounds
	if abs(pos.x) > 6000:
		return false
	
	return true

func generate_test_report():
	print("\n📊 === SPAWN POSITION FIX TEST REPORT ===")
	
	print("🔍 **Tests Performed:**")
	print("   1. ✅ Spatial teleport system position validation")
	print("   2. ✅ Teleport gate target position validation")
	print("   3. ✅ Critical teleportation scenario simulation")
	print("   4. ✅ Map controller default position validation")
	
	print("\n🎯 **Key Fixes Applied:**")
	print("   1. ✅ Updated dong_dau_north_exit: Vector2(750, -1225)")
	print("   2. ✅ Updated rung_nuong_right_exit: Vector2(-2000, 400)")
	print("   3. ✅ Updated hang_an_left_exit: Vector2(1800, -1225)")
	print("   4. ✅ Added player position validation to map controllers")
	print("   5. ✅ Added emergency repositioning functions")
	
	print("\n⚠️ **Remaining Manual Actions:**")
	print("   1. Update hang_an.tscn: Change player position from Vector2(-4365, 736) to Vector2(-2069, 484)")
	print("   2. Test actual teleportation in-game to verify fixes work")
	print("   3. Monitor console logs for any remaining falling issues")
	
	print("\n🎮 **Testing Instructions:**")
	print("   1. Load the game and go to Lang Van Lang")
	print("   2. Use teleport gate to go to Rung Nuong")
	print("   3. Verify player spawns on ground (not falling from sky)")
	print("   4. From Rung Nuong, teleport to Hang An")
	print("   5. Verify player spawns on ground in Hang An")
	print("   6. Test reverse routes (Hang An → Rung Nuong)")
	
	print("\n✅ **Expected Results:**")
	print("   - Player should never fall from sky after teleportation")
	print("   - Player should always spawn on solid ground")
	print("   - Console should show 'Player repositioned successfully' messages")
	print("   - No more 'Player falling rapidly' warnings")
	
	print("\n🚨 **If Issues Persist:**")
	print("   - Check console logs for validation warnings")
	print("   - Verify map controllers are calling _validate_player_position()")
	print("   - Ensure teleport gates use updated target_position values")
	print("   - Consider adjusting spawn positions if ground collision is missing")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running spawn position tests...")
		test_all_fixes()
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n📊 Generating test report...")
		generate_test_report()
	elif event.is_action_pressed("ui_select"):  # Space key
		print("\n🎮 Simulating teleportation scenarios...")
		simulate_teleportation_scenarios()
