[gd_resource type="Resource" script_class="EnemyDefinition" load_steps=3 format=3 uid="uid://1sb7t1b5b0aq"]

[ext_resource type="Script" path="res://enemy/scripts/enemy_definition.gd" id="1_pfx5w"]
[ext_resource type="SpriteFrames" uid="uid://dunuud7tbqfb5" path="res://enemy/skins/boss.tres" id="2_p1shx"]

[resource]
script = ExtResource("1_pfx5w")
name = "boss"
health = 800.0
damage = 40.0
speed = 4.0
agility = 15.0
detection_range = 300.0
xp_value = 150.0
sprite_frames = ExtResource("2_p1shx")
scale = 2.0
cooldown = 0.5
