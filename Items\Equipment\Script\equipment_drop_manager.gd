# Script quản lý việc r<PERSON>i trang bị (equipment_drop_manager.gd)
extends Node

# Preload scene cho equipment drop
var equipment_drop_scene = preload("res://Items/Equipment/scene/equipment_drop.tscn")

# Tỉ lệ rơi trang bị (%)
@export var armor_drop_rate: float = 6.0      # 6% c<PERSON> hội rơi áo giáp
@export var pants_drop_rate: float = 5.0      # 5% cơ hội rơi quần giáp
@export var boots_drop_rate: float = 4.5      # 4.5% c<PERSON> hội rơi giày
@export var helmet_drop_rate: float = 4.0     # 4% c<PERSON> hội rơi mũ (future)
@export var gloves_drop_rate: float = 3.0     # 3% c<PERSON> hội rơi găng tay (future)

# Tỉ lệ rơi trang bị dựa trên loại kẻ địch
var enemy_drop_rate_multiplier = {
	"sword": 1.0,   # Tỉ lệ cơ bản
	"spear": 1.1,   # Tăng 10%
	"bow": 1.3,     # Tăng 30%
	"boss": 3.0     # Tăng 200% (rất cao)
}

# Mapping loại kẻ địch với trang bị ưu tiên
var enemy_preferred_equipment = {
	"sword": ["Armor", "Boots"],      # Kẻ địch kiếm thường rơi áo giáp và giày
	"spear": ["Pant", "Boots"],       # Kẻ địch thương thường rơi quần giáp và giày
	"bow": ["Armor", "Pant"],         # Kẻ địch cung rơi áo giáp và quần giáp
	"boss": ["Armor", "Pant", "Boots"]  # Boss có thể rơi tất cả trang bị
}

# Hàm tạo trang bị rơi ra tại vị trí của kẻ địch
func spawn_equipment_drop(enemy_position: Vector2, enemy_type: String) -> void:
	# Tính toán tỉ lệ rơi dựa trên loại kẻ địch
	var multiplier = enemy_drop_rate_multiplier.get(enemy_type, 1.0)
	
	# Lấy danh sách trang bị ưu tiên cho loại kẻ địch này
	var preferred_equipment = enemy_preferred_equipment.get(enemy_type, ["Armor"])
	
	# Tính tỉ lệ rơi cho từng loại trang bị
	var adjusted_rates = {
		"Armor": armor_drop_rate * multiplier,
		"Pant": pants_drop_rate * multiplier,
		"Boots": boots_drop_rate * multiplier,
		"Helmet": helmet_drop_rate * multiplier,
		"Gloves": gloves_drop_rate * multiplier
	}
	
	# Tăng tỉ lệ cho trang bị ưu tiên
	for equipment in preferred_equipment:
		if equipment in adjusted_rates:
			adjusted_rates[equipment] *= 1.8  # Tăng 80% cho trang bị ưu tiên
	
	# Tạo số ngẫu nhiên để quyết định có rơi trang bị không
	var random_value = randf() * 100  # Số ngẫu nhiên từ 0 đến 100
	var cumulative_rate = 0.0
	
	# Kiểm tra từng loại trang bị
	for equipment_type in adjusted_rates.keys():
		cumulative_rate += adjusted_rates[equipment_type]
		if random_value < cumulative_rate:
			_create_equipment_drop(enemy_position, equipment_type)
			break
	
	# Nếu là boss, có cơ hội rơi thêm trang bị
	if enemy_type == "boss":
		# Boss có 60% cơ hội rơi thêm 1 trang bị nữa
		if randf() < 0.6:
			var bonus_equipment = preferred_equipment[randi() % preferred_equipment.size()]
			_create_equipment_drop(enemy_position + Vector2(randf_range(-25, 25), 0), bonus_equipment)

# Hàm tạo trang bị cụ thể
func _create_equipment_drop(position: Vector2, equipment_type: String) -> void:
	# Tạo instance của scene equipment_drop
	var equipment_instance = equipment_drop_scene.instantiate()
	
	# Thiết lập loại trang bị
	equipment_instance.equipment_type = equipment_type
	
	# Thiết lập vị trí
	equipment_instance.global_position = position
	
	# Thêm vào scene
	var current_scene = get_tree().current_scene
	if current_scene:
		current_scene.add_child(equipment_instance)
	else:
		get_tree().root.add_child(equipment_instance)
	
	# Thêm lực ngẫu nhiên để trang bị "bắn" ra
	equipment_instance.apply_impulse(Vector2(randf_range(-70, 70), randf_range(-130, -70)))
	
	print("Spawned " + equipment_type + " equipment at " + str(position))
