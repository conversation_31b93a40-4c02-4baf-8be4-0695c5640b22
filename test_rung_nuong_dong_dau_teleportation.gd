# Test script for rung_nuong → dong_dau teleportation fixes
extends Node

var test_results = {}

func _ready():
	print("🧪 Starting rung_nuong → dong_dau teleportation tests...")
	call_deferred("run_all_tests")

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				run_all_tests()
			KEY_F2:
				test_rung_nuong_to_dong_dau_manual()
			KEY_F3:
				test_dong_dau_to_rung_nuong_manual()
			KEY_F4:
				debug_spawn_positions()
			KEY_F5:
				test_map_bounds_validation()

func run_all_tests():
	print("\n🔍 COMPREHENSIVE RUNG_NUONG ↔ DONG_DAU TELEPORTATION TESTS")
	print("=" * 60)
	
	# Test 1: Check teleport gate configurations
	test_teleport_gate_configs()
	
	# Test 2: Validate spawn positions
	test_spawn_positions()
	
	# Test 3: Check map bounds
	test_map_bounds()
	
	# Test 4: Verify scene files
	test_scene_files()
	
	print_test_summary()

func test_teleport_gate_configs():
	print("\n📋 Testing teleport gate configurations...")
	
	# Test rung_nuong → dong_dau gate
	var rung_nuong_gate_path = "res://maps/rung_nuong/scenes/TeleportGate_RungNuong.tscn"
	test_results["rung_nuong_gate_exists"] = ResourceLoader.exists(rung_nuong_gate_path)
	print("✅ RungNuong→DongDau gate exists: %s" % test_results["rung_nuong_gate_exists"])
	
	# Test dong_dau → rung_nuong gate
	var dong_dau_gate_path = "res://maps/dong_dau/scenes/TeleportGate_DongDau.tscn"
	test_results["dong_dau_gate_exists"] = ResourceLoader.exists(dong_dau_gate_path)
	print("✅ DongDau→RungNuong gate exists: %s" % test_results["dong_dau_gate_exists"])
	
	# Test target scenes
	test_results["dong_dau_scene_exists"] = ResourceLoader.exists("res://maps/dong_dau/scenes/dong_dau.tscn")
	test_results["rung_nuong_scene_exists"] = ResourceLoader.exists("res://maps/rung_nuong/scenes/rung_nuong.tscn")
	print("✅ DongDau scene exists: %s" % test_results["dong_dau_scene_exists"])
	print("✅ RungNuong scene exists: %s" % test_results["rung_nuong_scene_exists"])

func test_spawn_positions():
	print("\n📍 Testing spawn positions...")
	
	# Expected spawn positions after fixes
	var rung_to_dong_spawn = Vector2(5200, -350)  # Fixed position
	var dong_to_rung_spawn = Vector2(-500, -1300)  # Fixed position
	
	print("🎯 RungNuong→DongDau spawn: %s" % rung_to_dong_spawn)
	print("🎯 DongDau→RungNuong spawn: %s" % dong_to_rung_spawn)
	
	# Check if positions are reasonable (not at origin)
	test_results["rung_to_dong_valid"] = rung_to_dong_spawn != Vector2.ZERO
	test_results["dong_to_rung_valid"] = dong_to_rung_spawn != Vector2.ZERO
	
	print("✅ RungNuong→DongDau position valid: %s" % test_results["rung_to_dong_valid"])
	print("✅ DongDau→RungNuong position valid: %s" % test_results["dong_to_rung_valid"])

func test_map_bounds():
	print("\n🗺️ Testing map bounds validation...")
	
	# DongDau map bounds
	var dong_dau_bounds = {
		"left": -2535,
		"right": 5400,
		"top": -1600,
		"bottom": -125
	}
	
	# RungNuong map bounds  
	var rung_nuong_bounds = {
		"left": -259,
		"right": 2443,
		"top": -2500,
		"bottom": 500
	}
	
	# Test spawn positions within bounds
	var rung_to_dong_spawn = Vector2(5200, -350)
	var dong_to_rung_spawn = Vector2(-500, -1300)
	
	# Check dong_dau spawn bounds
	var dong_within_bounds = (
		rung_to_dong_spawn.x >= dong_dau_bounds.left and 
		rung_to_dong_spawn.x <= dong_dau_bounds.right and
		rung_to_dong_spawn.y >= dong_dau_bounds.top and 
		rung_to_dong_spawn.y <= dong_dau_bounds.bottom
	)
	
	# Check rung_nuong spawn bounds
	var rung_within_bounds = (
		dong_to_rung_spawn.x >= rung_nuong_bounds.left and 
		dong_to_rung_spawn.x <= rung_nuong_bounds.right and
		dong_to_rung_spawn.y >= rung_nuong_bounds.top and 
		dong_to_rung_spawn.y <= rung_nuong_bounds.bottom
	)
	
	test_results["dong_spawn_within_bounds"] = dong_within_bounds
	test_results["rung_spawn_within_bounds"] = rung_within_bounds
	
	print("✅ DongDau spawn within bounds: %s" % dong_within_bounds)
	print("✅ RungNuong spawn within bounds: %s" % rung_within_bounds)
	
	if not dong_within_bounds:
		print("❌ CRITICAL: DongDau spawn position outside bounds!")
		print("   Spawn: %s" % rung_to_dong_spawn)
		print("   Bounds: left=%d, right=%d, top=%d, bottom=%d" % [dong_dau_bounds.left, dong_dau_bounds.right, dong_dau_bounds.top, dong_dau_bounds.bottom])
	
	if not rung_within_bounds:
		print("❌ CRITICAL: RungNuong spawn position outside bounds!")
		print("   Spawn: %s" % dong_to_rung_spawn)
		print("   Bounds: left=%d, right=%d, top=%d, bottom=%d" % [rung_nuong_bounds.left, rung_nuong_bounds.right, rung_nuong_bounds.top, rung_nuong_bounds.bottom])

func test_scene_files():
	print("\n📁 Testing scene file integrity...")
	
	var scenes_to_test = [
		"res://maps/rung_nuong/scenes/rung_nuong.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn"
	]
	
	for scene_path in scenes_to_test:
		var exists = ResourceLoader.exists(scene_path)
		var scene_name = scene_path.get_file().get_basename()
		test_results[scene_name + "_exists"] = exists
		print("✅ %s exists: %s" % [scene_name, exists])

func test_rung_nuong_to_dong_dau_manual():
	print("\n🧪 MANUAL TEST: RungNuong → DongDau")
	
	if SceneManager:
		print("📍 Setting spawn position: Vector2(5200, -350)")
		SceneManager.set_next_spawn_position(Vector2(5200, -350))
		
		print("🔄 Teleporting to dong_dau...")
		SceneManager.goto_scene("res://maps/dong_dau/scenes/dong_dau.tscn")
	else:
		print("❌ SceneManager not available")

func test_dong_dau_to_rung_nuong_manual():
	print("\n🧪 MANUAL TEST: DongDau → RungNuong")
	
	if SceneManager:
		print("📍 Setting spawn position: Vector2(-500, -1300)")
		SceneManager.set_next_spawn_position(Vector2(-500, -1300))
		
		print("🔄 Teleporting to rung_nuong...")
		SceneManager.goto_scene("res://maps/rung_nuong/scenes/rung_nuong.tscn")
	else:
		print("❌ SceneManager not available")

func debug_spawn_positions():
	print("\n🔍 DEBUG: Teleport Gate Positions")
	
	# RungNuong teleport gate positions
	print("🌲 RungNuong Map:")
	print("   dong_dau gate position: Vector2(-584, -1350)")
	print("   Target spawn in dong_dau: Vector2(5200, -350)")
	
	# DongDau teleport gate positions  
	print("🏛️ DongDau Map:")
	print("   rung_nuong gate position: Vector2(5328, -320)")
	print("   Target spawn in rung_nuong: Vector2(-500, -1300)")
	
	# Calculate distances
	var gate_distance = Vector2(-584, -1350).distance_to(Vector2(5328, -320))
	print("📏 Distance between gates: %.1f units" % gate_distance)

func test_map_bounds_validation():
	print("\n🧪 MANUAL TEST: Map Bounds Validation")
	
	print("📊 DongDau Map Bounds:")
	print("   Left: -2535, Right: 5400")
	print("   Top: -1600, Bottom: -125")
	print("   Spawn: Vector2(5200, -350)")
	print("   ✅ Within X bounds: -2535 ≤ 5200 ≤ 5400")
	print("   ✅ Within Y bounds: -1600 ≤ -350 ≤ -125")
	
	print("\n📊 RungNuong Map Bounds:")
	print("   Left: -259, Right: 2443")
	print("   Top: -2500, Bottom: 500")
	print("   Spawn: Vector2(-500, -1300)")
	print("   ❌ X bounds check: -259 ≤ -500 ≤ 2443 (OUTSIDE LEFT BOUND)")
	print("   ✅ Within Y bounds: -2500 ≤ -1300 ≤ 500")
	
	print("\n⚠️ WARNING: RungNuong spawn X position needs adjustment!")

func print_test_summary():
	print("\n📊 TEST SUMMARY")
	print("=" * 30)
	
	var passed = 0
	var total = 0
	
	for key in test_results:
		total += 1
		if test_results[key]:
			passed += 1
			print("✅ %s: PASS" % key)
		else:
			print("❌ %s: FAIL" % key)
	
	print("\n🎯 Results: %d/%d tests passed (%.1f%%)" % [passed, total, (float(passed)/total) * 100])
	
	if passed == total:
		print("🎉 ALL TESTS PASSED! Teleportation should work correctly.")
	else:
		print("⚠️ Some tests failed. Check the issues above.")
	
	print("\n🎮 CONTROLS:")
	print("F1 - Run all tests")
	print("F2 - Test RungNuong→DongDau teleport")
	print("F3 - Test DongDau→RungNuong teleport") 
	print("F4 - Debug spawn positions")
	print("F5 - Test map bounds validation")
