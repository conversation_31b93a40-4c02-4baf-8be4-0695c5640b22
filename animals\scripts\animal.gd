extends CharacterBody2D
class_name Animal

	
var walk_direction: int = 0  # -1 = left, 1 = right
var walk_change_timer: float = 0.0
var walk_change_interval: float = 2.0  # change direction every 2 seconds

var fly_direction: Vector2 = Vector2.ZERO
var fly_change_timer: float = 0.0
var fly_change_interval: float = 0.5  # change direction every 2 seconds

@export var isgoing: bool = false
@export var isflying: bool = false
@export var type: String = "boar"
@export var health = 100
@export var damage = 30
@export var attack_cd = 1.5
@export var speed = 300.0
@export var jump_agility = -400.0

@onready var animatedSprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var collisionShape: CollisionShape2D = $CollisionShape2D
@onready var animalClassManager: AnimalManager = $AnimalManager

func _ready() -> void:
	animalClassManager.adapt_to_class(self)
	
	animatedSprite.autoplay = "idle"
	animatedSprite.play("idle")

func _physics_process(delta: float) -> void:
	if !isgoing:
		return
	
	if isflying:
		flying_physics(delta)
	else:
		if not is_on_floor():
			velocity += get_gravity() * delta
		walking_physics(delta)

	move_and_slide()

func flying_physics(delta: float) -> void:
	fly_change_timer -= delta
	if fly_change_timer <= 0.0:
		# pick a new random direction
		fly_direction = Vector2(
			randf_range(-1.0, 1.0), 
			randf_range(-0.5, 0.5)
		).normalized()
		
		fly_change_timer = fly_change_interval  # reset the timer

	# Move in the current flying direction
	var flying_speed = speed * 0.5
	velocity = fly_direction * flying_speed
	#animatedSprite.play("fly")
	
	move_and_slide()

func walking_physics(delta: float) -> void:
	walk_change_timer -= delta
	if walk_change_timer <= 0.0:
		# Randomly pick left, right, or idle
		var choice = randi() % 3
		if choice == 0:
			walk_direction = -1  # walk left
		elif choice == 1:
			walk_direction = 1   # walk right
		else:
			walk_direction = 0   # idle

		# Reset timer for next direction change
		walk_change_timer = walk_change_interval

	# Set velocity based on walking direction
	velocity.x = walk_direction * speed

	# Flip sprite if necessary
	if walk_direction != 0:
		animatedSprite.flip_h = walk_direction < 0

	# Play walk or idle animation
	if walk_direction == 0:
		if type == "snail":
			animatedSprite.play("hide")
		else:
			animatedSprite.play("idle")
	else:
		animatedSprite.play("walk")

	move_and_slide()
