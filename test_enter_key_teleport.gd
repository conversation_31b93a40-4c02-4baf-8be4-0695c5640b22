# Test script for Enter key teleportation functionality
# Place this script on a Node in any scene to test teleport functionality
extends Node

var test_results = []

func _ready():
	print("🧪 === ENTER KEY TELEPORT TEST SUITE ===")
	call_deferred("run_all_tests")

func run_all_tests():
	print("\n🔍 Starting comprehensive teleport tests...")
	
	# Test 1: Input mapping verification
	test_input_mapping()
	
	# Test 2: Teleport gate configuration verification
	test_teleport_gate_configs()
	
	# Test 3: Position mapping system verification
	test_position_mapping_system()
	
	# Test 4: SceneManager integration verification
	test_scene_manager_integration()
	
	# Print final results
	print_test_results()

func test_input_mapping():
	print("\n📋 TEST 1: Input Mapping Verification")
	
	# Check if teleport_interact action exists
	if InputMap.has_action("teleport_interact"):
		print("   ✅ teleport_interact action exists")
		
		var events = InputMap.action_get_events("teleport_interact")
		var has_enter_key = false
		var has_old_m_key = false
		
		for event in events:
			if event is InputEventKey:
				if event.keycode == KEY_ENTER:
					has_enter_key = true
				elif event.keycode == KEY_M:
					has_old_m_key = true
		
		if has_enter_key:
			print("   ✅ Enter key is correctly mapped to teleport_interact")
			test_results.append("✅ Enter key mapping: PASS")
		else:
			print("   ❌ Enter key is NOT mapped to teleport_interact")
			test_results.append("❌ Enter key mapping: FAIL")
		
		if has_old_m_key:
			print("   ⚠️ WARNING: M key is still mapped (should be removed)")
			test_results.append("⚠️ M key cleanup: INCOMPLETE")
		else:
			print("   ✅ M key correctly removed from mapping")
			test_results.append("✅ M key cleanup: PASS")
	else:
		print("   ❌ teleport_interact action does not exist")
		test_results.append("❌ Input action: FAIL")

func test_teleport_gate_configs():
	print("\n📋 TEST 2: Teleport Gate Configuration Verification")
	
	# Find all teleport gates in the scene
	var gates = get_tree().get_nodes_in_group("teleport_gates")
	print("   Found %d teleport gates in current scene" % gates.size())
	
	if gates.size() == 0:
		print("   ⚠️ No teleport gates found in current scene")
		test_results.append("⚠️ Gate detection: NO GATES IN SCENE")
		return
	
	var valid_gates = 0
	for gate in gates:
		if gate.has_method("_activate_teleport") and gate.interaction_key == "teleport_interact":
			valid_gates += 1
			print("   ✅ Gate '%s' properly configured" % gate.gate_id)
		else:
			print("   ❌ Gate '%s' has configuration issues" % gate.name)
	
	if valid_gates == gates.size():
		print("   ✅ All gates properly configured")
		test_results.append("✅ Gate configuration: PASS")
	else:
		print("   ❌ %d/%d gates have configuration issues" % [gates.size() - valid_gates, gates.size()])
		test_results.append("❌ Gate configuration: PARTIAL FAIL")

func test_position_mapping_system():
	print("\n📋 TEST 3: Position Mapping System Verification")
	
	# Check if TeleportPositionMapping exists
	var mapping_system = get_node_or_null("/root/TeleportPositionMapping")
	if mapping_system:
		print("   ✅ TeleportPositionMapping system found")
		
		# Test some key mappings
		var test_mappings = [
			"lang_van_lang_to_dong_dau",
			"dong_dau_to_lang_van_lang",
			"rung_nuong_to_hang_an",
			"hang_an_to_rung_nuong"
		]
		
		var valid_mappings = 0
		for mapping in test_mappings:
			if mapping_system.position_mappings.has(mapping):
				var pos = mapping_system.position_mappings[mapping]
				if pos != Vector2.ZERO:
					print("   ✅ Mapping '%s': %s" % [mapping, pos])
					valid_mappings += 1
				else:
					print("   ⚠️ Mapping '%s' has zero position" % mapping)
			else:
				print("   ❌ Missing mapping: %s" % mapping)
		
		if valid_mappings == test_mappings.size():
			print("   ✅ All test mappings valid")
			test_results.append("✅ Position mappings: PASS")
		else:
			print("   ⚠️ %d/%d mappings valid" % [valid_mappings, test_mappings.size()])
			test_results.append("⚠️ Position mappings: PARTIAL")
	else:
		print("   ❌ TeleportPositionMapping system not found")
		test_results.append("❌ Position mapping system: NOT FOUND")

func test_scene_manager_integration():
	print("\n📋 TEST 4: SceneManager Integration Verification")
	
	if SceneManager:
		print("   ✅ SceneManager found")
		
		# Test spawn position methods
		var has_set_method = SceneManager.has_method("set_next_spawn_position")
		var has_get_method = SceneManager.has_method("get_next_spawn_position")
		var has_goto_method = SceneManager.has_method("goto_scene")
		
		if has_set_method and has_get_method and has_goto_method:
			print("   ✅ All required SceneManager methods available")
			
			# Test spawn position functionality
			var test_pos = Vector2(1000, -1000)
			SceneManager.set_next_spawn_position(test_pos)
			
			if SceneManager.has_next_spawn_position():
				var retrieved_pos = SceneManager.get_next_spawn_position()
				if retrieved_pos == test_pos:
					print("   ✅ Spawn position system working correctly")
					test_results.append("✅ SceneManager integration: PASS")
				else:
					print("   ❌ Spawn position mismatch: set %s, got %s" % [test_pos, retrieved_pos])
					test_results.append("❌ SceneManager spawn: FAIL")
			else:
				print("   ❌ Spawn position not detected after setting")
				test_results.append("❌ SceneManager spawn detection: FAIL")
			
			# Clean up test
			SceneManager.clear_next_spawn_position()
		else:
			print("   ❌ Missing SceneManager methods")
			test_results.append("❌ SceneManager methods: INCOMPLETE")
	else:
		print("   ❌ SceneManager not found")
		test_results.append("❌ SceneManager: NOT FOUND")

func print_test_results():
	print("\n🏁 === TEST RESULTS SUMMARY ===")
	
	var pass_count = 0
	var fail_count = 0
	var warning_count = 0
	
	for result in test_results:
		print("   %s" % result)
		if result.begins_with("✅"):
			pass_count += 1
		elif result.begins_with("❌"):
			fail_count += 1
		elif result.begins_with("⚠️"):
			warning_count += 1
	
	print("\n📊 STATISTICS:")
	print("   ✅ Passed: %d" % pass_count)
	print("   ❌ Failed: %d" % fail_count)
	print("   ⚠️ Warnings: %d" % warning_count)
	
	if fail_count == 0:
		if warning_count == 0:
			print("\n🎉 ALL TESTS PASSED! Enter key teleportation is ready!")
		else:
			print("\n✅ Tests passed with warnings. System should work correctly.")
	else:
		print("\n⚠️ Some tests failed. Please review the issues above.")
	
	print("\n🎮 MANUAL TEST INSTRUCTIONS:")
	print("1. Walk near any teleport gate")
	print("2. Press ENTER key (not M key) to activate teleportation")
	print("3. Verify you spawn near the destination gate (not at map center)")
	print("4. Test multiple consecutive teleportations")
	print("================================")

func _input(event):
	# Quick test trigger
	if event is InputEventKey and event.pressed and event.keycode == KEY_F12:
		print("\n🔄 Re-running tests...")
		test_results.clear()
		call_deferred("run_all_tests")
