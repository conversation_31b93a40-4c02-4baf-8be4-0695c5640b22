[gd_scene load_steps=3 format=3 uid="uid://c8yvnxvnqvdxs"]

[ext_resource type="Script" path="res://systems/quest/enemy_wave_spawner.gd" id="1_yvnxs"]
[ext_resource type="PackedScene" uid="uid://dk45l678rqkr1" path="res://enemy/scenes/Enemy.tscn" id="2_yvnxs"]

[node name="EnemyWaveSpawner" type="Node2D"]
script = ExtResource("1_yvnxs")
enemy_scene = ExtResource("2_yvnxs")

[node name="FrontGateSpawnPoints" type="Node2D" parent="."]

[node name="SpawnPoint1" type="Marker2D" parent="FrontGateSpawnPoints"]
position = Vector2(0, -26)

[node name="SpawnPoint2" type="Marker2D" parent="FrontGateSpawnPoints"]
position = Vector2(0, 73)

[node name="SpawnPoint3" type="Marker2D" parent="FrontGateSpawnPoints"]
position = Vector2(1, 148)

[node name="BackGateSpawnPoints" type="Node2D" parent="."]
position = Vector2(3485, 1)

[node name="SpawnPoint1" type="Marker2D" parent="BackGateSpawnPoints"]
position = Vector2(0, -26)

[node name="SpawnPoint2" type="Marker2D" parent="BackGateSpawnPoints"]
position = Vector2(0, 74)

[node name="SpawnPoint3" type="Marker2D" parent="BackGateSpawnPoints"]
position = Vector2(0, 145)
