[gd_scene load_steps=5 format=3 uid="uid://c7ay0dufwtyby"]

[ext_resource type="Texture2D" uid="uid://oax7heho3lva" path="res://assets/images/background/elements/background.png" id="1_w5v1c"]
[ext_resource type="PackedScene" uid="uid://bkn5seficjirh" path="res://player/player.tscn" id="2_4rgpu"]
[ext_resource type="PackedScene" uid="uid://7ohdh0u4q5rd" path="res://allies/scenes/Ally.tscn" id="3_sejc3"]
[ext_resource type="Script" path="res://maps/ally_test_scene.gd" id="4_test"]

[node name="AllyTestScene" type="Node2D"]
script = ExtResource("4_test")

[node name="Background" type="Sprite2D" parent="."]
position = Vector2(576, 324)
scale = Vector2(5, 5)
texture = ExtResource("1_w5v1c")

[node name="Player" parent="." instance=ExtResource("2_4rgpu")]
position = Vector2(576, 324)

[node name="Ally" parent="." node_paths=PackedStringArray("player_target") instance=ExtResource("3_sejc3")]
position = Vector2(700, 324)
player_target = NodePath("../Player")

[node name="Platform" type="StaticBody2D" parent="."]
position = Vector2(576, 400)
collision_layer = 2

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="Platform"]
polygon = PackedVector2Array(-600, 0, 600, 0, 600, 100, -600, 100)

[node name="ColorRect" type="ColorRect" parent="Platform"]
offset_left = -600.0
offset_right = 600.0
offset_bottom = 100.0
color = Color(0.2, 0.2, 0.2, 1)

[node name="HighPlatform" type="StaticBody2D" parent="."]
position = Vector2(300, 250)
collision_layer = 2

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="HighPlatform"]
polygon = PackedVector2Array(-100, 0, 100, 0, 100, 20, -100, 20)

[node name="ColorRect" type="ColorRect" parent="HighPlatform"]
offset_left = -100.0
offset_right = 100.0
offset_bottom = 20.0
color = Color(0.2, 0.2, 0.2, 1)

[node name="Instructions" type="Label" parent="."]
offset_left = 10.0
offset_top = 10.0
offset_right = 500.0
offset_bottom = 100.0
text = "Test ally following behavior:
1. Move away from the ally to see if it follows
2. Move close to the ally to see if it stops
3. Jump on the high platform to see if it jumps to follow"
