extends CharacterBody2D
class_name Player

var is_attacking: bool
var xp: int = 0
var level: int = 1
@export var health: int = 100
@export var max_health: int = 100
@export var mana: float = 50
@export var max_mana: float = 50
@export var speed: float = 200.0
@export var jump_velocity: float = -350.0
@export var damage: float = 10

var rank_title: String = "Game Master"
var devine_mission: String = "Peasant"

# God Mode variables
var is_god_mode: bool = false
var god_mode_aura_color: Color = Color(1.0, 0.8, 0.0, 0.7) # Golden color
var can_fly: bool = false
var gravity_value: float = 980.0 # Giá trị trọng lực mặc định
var fly_speed: float = 500.0 # Tốc độ bay

signal animation_finished(anim_name: String)
signal god_mode_changed(is_active: bool)

@onready var abilityManager: AbilityManager = $AbilityManager
@onready var devineMission: AnimatedSprite2D = $DevineMission
@onready var auraHolder: AnimatedSprite2D = $AuraHolder
@onready var collisionShape2D: CollisionShape2D = $CollisionShape2D # Sửa đường dẫn
@onready var god_mode_label: Label = $CanvasLayer/GodModeLabel if has_node("CanvasLayer/GodModeLabel") else null

#Call UI from another World

var current_state: PlayerState

func _ready() -> void:
	XPManager.set_up_player(self)
	MissionSystem.set_up_player(self)
	auraHolder.visible = false

	devineMission.connect("animation_finished", _on_devineMission_animation_finished)
	current_state = IdleState.new()
	current_state.enter(self)

func _input(event: InputEvent) -> void:
	if current_state:
		current_state.handle_input(self, event)

	if event.is_action_pressed("attack_1"):
		abilityManager.handle_combo_input("Attack 1")

	if event.is_action_pressed("attack_2"):
		abilityManager.handle_combo_input("Attack 2")

	if event.is_action_pressed("attack_3"):
		abilityManager.handle_combo_input("Attack 3")

	if event.is_action_pressed("attack_4"):
		abilityManager.handle_combo_input("Attack 4")

	if event.is_action_released("run") and is_on_floor():
		change_state(WalkState.new())

	if event.is_action_pressed("swicth_misison"):
		MissionSystem.switch_mission()

	if event.is_action_pressed("burst"):
		bursting()

	# God Mode toggle
	if event.is_action_pressed("god_mode"):
		toggle_god_mode()

	# Debug: Test weapon drop (Enter key in god mode)
	if event.is_action_pressed("ui_accept") and is_god_mode:
		_test_weapon_drop()

	# Debug: Check weapon inventory (I key in god mode)
	if event.is_action_pressed("envolve") and is_god_mode:
		_print_weapon_inventory()

func _physics_process(delta: float) -> void:
	# Xử lý chế độ God Mode
	if is_god_mode:
		# Hồi phục liên tục máu và mana
		health = max_health
		mana = max_mana

	# Xử lý chế độ bay trong God Mode
	if can_fly and is_god_mode:
		# Xử lý di chuyển bay tự do
		var fly_direction = Vector2.ZERO

		# Di chuyển ngang
		fly_direction.x = Input.get_axis("move_left", "move_right")

		# Di chuyển dọc (lên/xuống) khi bay
		if Input.is_action_pressed("jump"):
			fly_direction.y = -1  # Bay lên
		elif Input.is_action_pressed("ui_down") or Input.is_action_pressed("move_down"):
			fly_direction.y = 1   # Bay xuống

		# Chuẩn hóa vector để tránh di chuyển nhanh hơn theo đường chéo
		if fly_direction.length() > 0:
			fly_direction = fly_direction.normalized()

		# Áp dụng tốc độ bay
		velocity = fly_direction * fly_speed

		# Không áp dụng trọng lực khi bay
	else:
		# Xử lý trọng lực bình thường
		if not is_on_floor():
			velocity += calculate_gravity() * delta

	# Xử lý di chuyển ngang bình thường
	var direction = Input.get_axis("move_left", "move_right")
	var current_speed = speed

	# Chỉ áp dụng chạy nhanh khi ở trên mặt đất và không bay
	if Input.is_action_pressed("run") and (is_on_floor() or (can_fly and is_god_mode)):
		current_speed *= 1.5
		if not can_fly or not is_god_mode:  # Chỉ chơi animation chạy khi không bay
			play_animation("running")

	# Nếu đang bay, không cần lerp cho di chuyển ngang (đã xử lý ở trên)
	if not (can_fly and is_god_mode):
		var target_velocity_x = direction * current_speed
		var acceleration_factor = 8.0
		velocity.x = lerp(velocity.x, target_velocity_x, acceleration_factor * delta)

		if abs(direction) < 0.1:
			var friction_factor = 6.0
			velocity.x = lerp(velocity.x, 0.0, friction_factor * delta)

	move_and_slide()

	if current_state:
		current_state.update(self, delta)

	# Cập nhật hướng nhìn của nhân vật
	if velocity.x < -0.1:
		devineMission.flip_h = true
	elif velocity.x > 0.1:
		devineMission.flip_h = false

func bursting():
	auraHolder.visible = true
	auraHolder.play("burst")

# Hàm trả về giá trị trọng lực (đổi tên để tránh xung đột với PhysicsBody2D)
func calculate_gravity() -> Vector2:
	if is_god_mode and can_fly:
		# Không có trọng lực khi ở chế độ bay
		return Vector2.ZERO
	else:
		# Trọng lực bình thường
		return Vector2(0, gravity_value)

func change_state(new_state: PlayerState) -> void:
	if current_state:
		current_state.exit(self)
	current_state = new_state
	current_state.enter(self)
	if new_state is IdleState:
		play_animation("idle")
		print("Switched to IdleState, now playing:", devineMission.animation)

func play_animation(animation_name: String):
	if devineMission.animation != animation_name:
		devineMission.play(animation_name)

func rank_up(next_rank: RankDefinition):
	auraHolder.sprite_frames = next_rank.rank_frames
	rank_title = next_rank.rank_name
	mana *= next_rank.stats_multiplier
	max_mana *= next_rank.stats_multiplier
	speed *= next_rank.stats_multiplier
	health *= next_rank.stats_multiplier
	max_health *= next_rank.stats_multiplier
	damage *= next_rank.stats_multiplier
	jump_velocity *= next_rank.stats_multiplier

func adapt_to_mission(mission: ClassDefinition):
	devine_mission = mission.mission
	devineMission.sprite_frames = mission.sprite_fames
	change_state(IdleState.new())
	play_animation("idle")

	health = mission.base_health
	max_health = mission.base_health
	mana = mission.base_mana
	max_mana = mission.base_mana
	damage = mission.base_damage
	jump_velocity = mission.base_agility * -27
	speed = mission.base_speed * 20

	var ab_set = AbilityLibrary.get_mission_ability_set(mission.mission)
	abilityManager.set_ability_set(ab_set)

func _on_devineMission_animation_finished() -> void:
	print("Raw devineMission signal fired!")
	print(current_state.get_state_name())
	change_state(IdleState.new())

# Hàm bật/tắt God Mode
func toggle_god_mode() -> void:
	is_god_mode = !is_god_mode

	# Phát tín hiệu thay đổi trạng thái God Mode
	emit_signal("god_mode_changed", is_god_mode)

	if is_god_mode:
		# Hiệu ứng khi bật God Mode
		print("God Mode: ON")
		auraHolder.visible = true
		auraHolder.modulate = god_mode_aura_color
		auraHolder.play("burst")

		# Bật chế độ bay
		can_fly = true
		# Không cần lưu trữ giá trị trọng lực vì đã có biến gravity_value

		# Tạo Label thông báo nếu chưa có
		if not god_mode_label:
			# Kiểm tra xem CanvasLayer đã tồn tại chưa
			var canvas_layer
			if has_node("CanvasLayer"):
				canvas_layer = $CanvasLayer
			else:
				# Tạo CanvasLayer mới nếu chưa có
				canvas_layer = CanvasLayer.new()
				canvas_layer.name = "CanvasLayer"
				add_child(canvas_layer)

			# Tạo Label
			god_mode_label = Label.new()
			god_mode_label.name = "GodModeLabel"
			god_mode_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
			god_mode_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
			god_mode_label.position = Vector2(0, 50)
			god_mode_label.size = Vector2(1280, 50)
			god_mode_label.add_theme_color_override("font_color", Color(1, 0.8, 0, 1))
			god_mode_label.add_theme_font_size_override("font_size", 24)
			canvas_layer.add_child(god_mode_label)

		# Hiển thị thông báo
		if god_mode_label:
			god_mode_label.text = "CHẾ ĐỘ THẦN THÁNH: BẬT (Bay tự do, Vô hạn máu/mana, Không hồi chiêu)"
			god_mode_label.visible = true

		# Đặt máu và mana lên giá trị tối đa
		health = max_health
		mana = max_mana
	else:
		# Hiệu ứng khi tắt God Mode
		print("God Mode: OFF")
		auraHolder.modulate = Color(1, 1, 1, 1) # Trả về màu bình thường
		auraHolder.visible = false

		# Tắt chế độ bay
		can_fly = false

		# Ẩn thông báo
		if god_mode_label:
			god_mode_label.text = "CHẾ ĐỘ THẦN THÁNH: TẮT"

			# Tạo timer để ẩn thông báo sau 2 giây
			var timer = get_tree().create_timer(2.0)
			timer.timeout.connect(func(): god_mode_label.visible = false)

# Hàm nhận sát thương
func take_damage(amount: float) -> void:
	# Nếu đang trong chế độ God Mode, không nhận sát thương
	if is_god_mode:
		print("God Mode active: No damage taken!")
		# Đảm bảo máu và mana luôn đầy trong God Mode
		health = max_health
		mana = max_mana
		return

	# Xử lý sát thương bình thường
	health -= amount

	# Hiển thị số sát thương
	var damage_manager = get_node_or_null("/root/DamageEffects")
	if damage_manager:
		# Tạo số sát thương với màu vàng cho người chơi
		var is_critical = amount > 20  # Đòn chí mạng nếu sát thương lớn
		damage_manager.create_damage_number(global_position + Vector2(0, -30), amount, is_critical, Color(1.0, 0.8, 0.2))

		# Tạo hiệu ứng hit
		damage_manager.create_hit_effect(global_position)

		# Rung màn hình khi bị đánh
		damage_manager.apply_screen_shake(amount / 10.0, 0.2)

	# Hiệu ứng flash khi bị đánh
	if devineMission:
		devineMission.modulate = Color(2.0, 0.5, 0.5)  # Đỏ sáng
		var tween = create_tween()
		tween.tween_property(devineMission, "modulate", Color(1, 1, 1), 0.3)

	# Kiểm tra nếu máu dưới 0
	if health <= 0:
		health = 0
		print("Player defeated!")
		# Có thể thêm xử lý khi người chơi bị đánh bại ở đây
		if current_state and not is_instance_of(current_state, DeathState) and has_method("change_state"):
			change_state(DeathState.new())

# Hàm thêm vũ khí vào inventory của player
func add_weapon_to_inventory(weapon_type: String) -> void:
	# Tìm InventoryManager toàn cục
	var inventory_manager = get_node_or_null("/root/InventoryManager")
	if inventory_manager and inventory_manager.has_method("add_weapon"):
		inventory_manager.add_weapon(weapon_type)
		print("Player collected " + weapon_type + " weapon")

		# Hiển thị thông báo trên UI (nếu có)
		_show_weapon_collected_notification(weapon_type)
	else:
		print("WARNING: InventoryManager not found, cannot add weapon to inventory")

# Hiển thị thông báo khi nhặt được vũ khí
func _show_weapon_collected_notification(weapon_type: String) -> void:
	# Tạo thông báo đơn giản trong console
	var notification_text = "Collected " + weapon_type + " weapon!"
	print(notification_text)

# Debug function để test weapon drop system
func _test_weapon_drop() -> void:
	print("Testing weapon drop system...")
	var weapon_manager = get_node_or_null("/root/WeaponDropManager")
	if weapon_manager and weapon_manager.has_method("spawn_weapon_drop"):
		# Tạo weapon drop tại vị trí player
		weapon_manager.spawn_weapon_drop(global_position + Vector2(50, 0), "sword")
		print("Spawned test weapon drop")
	else:
		print("WARNING: WeaponDropManager not found for testing")

# Debug function để kiểm tra weapon inventory
func _print_weapon_inventory() -> void:
	print("Checking weapon inventory...")
	var inventory_manager = get_node_or_null("/root/InventoryManager")
	if inventory_manager and inventory_manager.has_method("print_inventory"):
		inventory_manager.print_inventory()
	else:
		print("WARNING: InventoryManager not found")
