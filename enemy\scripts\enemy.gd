extends CharacterBody2D
class_name Enemy

signal enemy_defeated(enemy)

# --- Stats & State -------------------------------------------------------
var health: float = 100.0
var damage: float = 10.0
var xp_value: int = 10

@export var acceleration: float = 8.0
@export var type: String = "sword"
@export var detection_range: float = 2000.0  # Tăng phạm vi phát hiện lên toàn bản đồ
@export var attack_range: float = 20.0  # Tăng phạm vi tấn công
@export var dash_range: float = 100.0  # Tăng phạm vi lao tới
@export var attack_cooldown: float = 1.0
@export var speed: float = 300.0
@export var jump_velocity: float = -1500.0  # Tăng lực nhảy lên rất cao để nhảy lên các vị trí cao hơn

var _current_cooldown: float = 0.0
var _facing_right: bool = true

# State flags
var is_hurt: bool = false
var is_dead: bool = false
var is_dashing: bool = false
var is_jumping: bool = false
var jump_cooldown: float = 0.0
var jump_cooldown_time: float = 0.2  # Giảm thời gian chờ giữa các lần nhảy để nhảy thường xuyên hơn
var jump_detection_height: float = 800.0  # Tăng phạm vi phát hiện độ cao để phát hiện lính đồng minh ở cao hơn
var search_jump_timer: float = 0.0  # Bộ đếm thời gian cho nhảy tìm kiếm
var can_double_jump: bool = true  # Cho phép nhảy đôi để tiếp cận vị trí cao hơn
var can_triple_jump: bool = true  # Cho phép nhảy ba lần để tiếp cận vị trí rất cao

var boss_hitbox: RectangleShape2D = load("res://enemy/hitboxes/boss_hitbox.tres")

# --- Scene Refs ----------------------------------------------------------
@onready var animated_sprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var class_manager: ClassManager = $ClassManager
@onready var detect_zone: Area2D = $DetectZone
@onready var hitbox_area: Area2D = $Hitbox
@onready var hitbox_shape: CollisionShape2D = $Hitbox/CollisionShape2D
@onready var health_bar: HealthBar = $HealthBar

# Targets
var follow_target: CharacterBody2D = null
var attack_target: CharacterBody2D = null

# --- Lifecycle -----------------------------------------------------------
func _ready() -> void:
	# Đảm bảo tất cả các node đã được khởi tạo
	if !animated_sprite:
		animated_sprite = $AnimatedSprite2D

	if !class_manager:
		class_manager = $ClassManager

	if !detect_zone:
		detect_zone = $DetectZone

	if !hitbox_area:
		hitbox_area = $Hitbox

	if !hitbox_shape:
		hitbox_shape = $Hitbox/CollisionShape2D

	if !health_bar:
		health_bar = $HealthBar

	if type == "boss":
		hitbox_shape.shape = boss_hitbox

	health_bar.initialize(self)

	# Cập nhật class trước khi phát animation
	class_manager.update_class(self, type)

	# Phát animation sau khi đã cập nhật class
	if animated_sprite and is_instance_valid(animated_sprite):
		animated_sprite.play("idle")

		var frames = animated_sprite.sprite_frames
		if frames:
			frames.set_animation_loop("hurt", false)
			frames.set_animation_loop("die", false)

		animated_sprite.animation_finished.connect(_on_animation_finished)
	else:
		print("ERROR: Cannot initialize AnimatedSprite2D")

	# Kết nối các tín hiệu
	detect_zone.body_entered.connect(_on_attack_zone_enter)
	detect_zone.body_exited.connect(_on_attack_zone_exit)
	hitbox_area.body_entered.connect(_on_body_hit)

	_current_cooldown = 0.0

	# Thêm hiệu ứng màu sắc ngẫu nhiên để phân biệt kẻ địch
	_apply_visual_distinction()

	# Đảm bảo kẻ địch thuộc nhóm enemy_group
	if not is_in_group("enemy_group"):
		add_to_group("enemy_group")

	# Tìm kiếm đồng minh ngay khi khởi tạo
	call_deferred("_find_initial_target")

func _physics_process(delta: float) -> void:
	if is_hurt or is_dead:
		return

	if is_dashing:
		# During dash, do not handle follow/movement
		move_and_slide()
		return

	_current_cooldown = max(_current_cooldown - delta, 0.0)
	jump_cooldown = max(jump_cooldown - delta, 0.0)
	search_jump_timer = max(search_jump_timer - delta, 0.0)

	# Kiểm tra vị trí để tránh di chuyển ra khỏi map
	_check_map_boundaries()

	# Tìm mục tiêu trong tầm để tấn công trước
	if !attack_target:
		var target_in_range = _find_target_in_range()
		if target_in_range:
			attack_target = target_in_range

	# Nếu không có mục tiêu trong tầm, tìm mục tiêu mới nếu chưa có
	if !attack_target and !follow_target:
		# Tìm đồng minh gần nhất trên toàn bản đồ
		var closest_ally = _find_closest_ally()

		# Tìm người chơi
		var player = get_tree().get_first_node_in_group("player")

		# Luôn ưu tiên tấn công đồng minh trên toàn bản đồ
		var ally_dist = 999999.0
		var player_dist = 999999.0

		if closest_ally:
			ally_dist = global_position.distance_to(closest_ally.global_position)
			attack_target = closest_ally  # Luôn tấn công đồng minh nếu có, bất kể khoảng cách

			# Tăng tốc độ di chuyển khi đồng minh ở xa
			if ally_dist > 300:
				speed = speed * 1.5  # Tăng tốc độ để tiếp cận đồng minh nhanh hơn

			# Nhảy ngay lập tức nếu đồng minh ở trên cao
			if is_on_floor() and jump_cooldown <= 0:
				var height_diff = global_position.y - closest_ally.global_position.y
				var horizontal_diff = abs(global_position.x - closest_ally.global_position.x)

				# Giảm ngưỡng phát hiện độ cao để nhảy thường xuyên hơn
				if height_diff < -jump_detection_height * 0.3:
					# Tính toán lực nhảy dựa trên độ cao
					var jump_power = 2.0  # Tăng lực nhảy mặc định

					# Tăng lực nhảy dựa trên độ cao cần vượt qua
					if height_diff < -jump_detection_height * 1.5:
						jump_power = 2.5  # Nhảy rất cao cho mục tiêu ở rất cao
					elif height_diff < -jump_detection_height:
						jump_power = 2.2  # Nhảy cao cho mục tiêu ở cao

					# Thêm một chút vận tốc ngang để tiếp cận đồng minh tốt hơn
					var horizontal_dir = sign(closest_ally.global_position.x - global_position.x)

					# Điều chỉnh vận tốc ngang dựa trên khoảng cách ngang
					var horizontal_speed_factor = 1.2
					if horizontal_diff > 100:
						horizontal_speed_factor = 1.5  # Tăng tốc nếu đồng minh ở xa

					velocity.x = horizontal_dir * speed * horizontal_speed_factor
					velocity.y = jump_velocity * jump_power
					is_jumping = true
					can_double_jump = true  # Cho phép nhảy đôi
					jump_cooldown = jump_cooldown_time * 0.3  # Giảm thời gian chờ nhiều hơn
					animated_sprite.play("running")
			return

		# Chỉ tấn công người chơi nếu không có đồng minh nào trên toàn bản đồ
		if !closest_ally and player:
			player_dist = global_position.distance_to(player.global_position)
			if player_dist < detection_range * 2.0:  # Mở rộng phạm vi phát hiện người chơi
				follow_target = player

				# Kiểm tra xem người chơi có ở trên cao không
				if is_on_floor() and jump_cooldown <= 0:
					var height_diff = global_position.y - player.global_position.y
					if height_diff < -jump_detection_height:  # Sử dụng cùng ngưỡng với _check_and_jump
						# Tính toán lực nhảy dựa trên độ cao
						var jump_power = 1.0

						# Tăng lực nhảy dựa trên độ cao cần vượt qua
						if height_diff < -jump_detection_height * 1.5:
							jump_power = 1.7  # Nhảy rất cao cho mục tiêu ở rất cao
						elif height_diff < -jump_detection_height:
							jump_power = 1.5  # Nhảy cao cho mục tiêu ở cao

						# Thêm một chút vận tốc ngang để tiếp cận người chơi tốt hơn
						var horizontal_dir = sign(player.global_position.x - global_position.x)
						velocity.x = horizontal_dir * speed * 0.8

						velocity.y = jump_velocity * jump_power
						is_jumping = true
						jump_cooldown = jump_cooldown_time * 0.5  # Giảm thời gian chờ nhiều hơn
						animated_sprite.play("running")

	# Xử lý hành vi dựa trên mục tiêu
	if attack_target:
		_handle_attack_behavior(delta)
	elif follow_target:
		_handle_follow_behavior(delta)
	else:
		# Nếu không có mục tiêu, chủ động tìm kiếm
		_search_for_targets()

	# Áp dụng lực đẩy để tránh chồng chéo với kẻ địch khác
	_apply_separation_force(delta)

	if not is_on_floor():
		velocity.y += _get_gravity_value().y * delta

		# Kiểm tra xem có đồng minh ở trên cao không khi đang ở trên không trung
		if velocity.y > 0 and is_jumping:  # Đang rơi xuống sau khi nhảy
			# Kiểm tra xem có đồng minh ở trên cao không
			var closest_ally = _find_closest_ally()
			if closest_ally:
				var height_diff = global_position.y - closest_ally.global_position.y
				var horizontal_diff = abs(global_position.x - closest_ally.global_position.x)

				# Nếu vẫn còn đồng minh ở cao hơn và chưa đạt tới, thử nhảy lần nữa
				if height_diff < -jump_detection_height * 0.3 and jump_cooldown <= 0:
					var can_jump_again = false
					var jump_power = 1.0

					# Kiểm tra khả năng nhảy tiếp
					if can_double_jump:
						can_jump_again = true
						jump_power = 2.5  # Nhảy rất cao khi đang ở trên không trung
						print("Using double jump to reach ally at height diff: " + str(height_diff))
					elif can_triple_jump:
						can_jump_again = true
						jump_power = 3.5  # Nhảy cực cao cho nhảy lần thứ ba
						print("Using triple jump to reach ally at height diff: " + str(height_diff))

					if can_jump_again:
						# Tăng lực nhảy dựa trên độ cao cần vượt qua
						if height_diff < -jump_detection_height * 1.5:
							jump_power += 1.0  # Tăng thêm lực nhảy cho mục tiêu ở rất cao

						# Thêm một chút vận tốc ngang để tiếp cận đồng minh tốt hơn
						var horizontal_dir = sign(closest_ally.global_position.x - global_position.x)

						# Điều chỉnh vận tốc ngang dựa trên khoảng cách ngang
						var horizontal_speed_factor = 1.5
						if horizontal_diff > 100:
							horizontal_speed_factor = 2.0  # Tăng tốc nếu đồng minh ở xa

						velocity.x = horizontal_dir * speed * horizontal_speed_factor
						velocity.y = jump_velocity * jump_power
						is_jumping = true

						# Cập nhật trạng thái nhảy
						if can_double_jump:
							can_double_jump = false
							print("Double jump used")
						elif can_triple_jump:
							can_triple_jump = false
							print("Triple jump used")

						jump_cooldown = jump_cooldown_time * 0.1  # Thời gian chờ rất ngắn

		# Reset trạng thái nhảy khi chạm đất
		if is_on_floor():
			is_jumping = false
			can_double_jump = true  # Reset khả năng nhảy đôi khi chạm đất
			can_triple_jump = true  # Reset khả năng nhảy ba lần khi chạm đất
			print("Landed on floor, reset jump abilities")

	move_and_slide()

# --- Behaviors -----------------------------------------------------------
func _handle_attack_behavior(delta: float) -> void:
	# Kiểm tra xem mục tiêu có còn tồn tại không
	if not is_instance_valid(attack_target):
		attack_target = null
		# Tìm mục tiêu mới ngay lập tức
		_find_initial_target()
		return

	# Kiểm tra xem mục tiêu có phải là kẻ địch không, nếu là kẻ địch thì bỏ qua
	if attack_target.is_in_group("enemy_group"):
		attack_target = null
		_find_initial_target()
		return

	# Xử lý hành vi tấn công
	var to_target = attack_target.global_position - global_position
	var dist = to_target.length()
	var height_diff = global_position.y - attack_target.global_position.y

	# Luôn di chuyển về phía mục tiêu, bất kể khoảng cách
	var dir = to_target.normalized()

	# Kiểm tra xem có cần nhảy để tiếp cận mục tiêu không
	# Nếu mục tiêu ở trên cao, ưu tiên nhảy ngay lập tức
	if height_diff < -jump_detection_height * 0.3 and is_on_floor() and jump_cooldown <= 0:
		var jump_power = 2.0
		if height_diff < -jump_detection_height * 1.5:
			jump_power = 2.5

		velocity.y = jump_velocity * jump_power
		is_jumping = true
		can_double_jump = true
		jump_cooldown = jump_cooldown_time * 0.3

		# Thêm vận tốc ngang để tiếp cận mục tiêu
		velocity.x = dir.x * speed * 1.5
		_update_facing(dir.x)
		animated_sprite.play("running")
	else:
		# Nếu không cần nhảy ngay, kiểm tra nhảy thông thường
		_check_and_jump(attack_target.global_position)

	# Chỉ tấn công khi đủ gần
	if dist <= attack_range:
		velocity.x = lerp(velocity.x, 0.0, acceleration * delta)

		if _current_cooldown == 0.0:
			_current_cooldown = attack_cooldown * 0.8  # Giảm thời gian hồi chiêu để tấn công nhanh hơn
			hitbox_shape.disabled = false

			# Tăng sát thương khi tấn công đồng minh
			if attack_target.is_in_group("ally_group"):
				damage = damage * 1.2  # Tăng sát thương khi tấn công đồng minh
				# Đảm bảo damage vẫn là float

			# Bắt đầu tấn công
			if type == "boss":
				if dist <= dash_range and randi() % 2 == 0:
					is_dashing = true
					animated_sprite.play("attack_2")
					_dash_forward(attack_target.global_position)
				else:
					animated_sprite.play("attack")
			else:
				animated_sprite.play("attack")
	else:
		# Nếu không đủ gần để tấn công, di chuyển đến mục tiêu
		var chase_speed = speed * 1.8  # Tăng tốc độ đuổi theo
		velocity.x = lerp(velocity.x, dir.x * chase_speed, acceleration * delta * 1.5)  # Tăng gia tốc
		_update_facing(dir.x)
		_play_run()

func _dash_forward(target_position: Vector2) -> void:
	var to_target = target_position - global_position
	var dir = to_target.normalized()
	var dash_speed = speed * 2.0
	velocity = dir * dash_speed

	# Tạo hiệu ứng dash
	_create_dash_effect()

# Tạo hiệu ứng tấn công
func _create_attack_effect(target_position: Vector2) -> void:
	# Tạo hiệu ứng tấn công dựa trên loại kẻ địch
	var effect_color = Color(1.0, 0.3, 0.3, 0.7)  # Màu đỏ mặc định

	match type:
		"sword":
			effect_color = Color(1.0, 0.3, 0.3, 0.7)  # Đỏ
		"spear":
			effect_color = Color(0.3, 1.0, 0.3, 0.7)  # Xanh lá
		"bow":
			effect_color = Color(0.3, 0.3, 1.0, 0.7)  # Xanh dương
		"boss":
			effect_color = Color(1.0, 0.8, 0.2, 0.8)  # Vàng

	# Tạo hiệu ứng tia lửa
	var particles = CPUParticles2D.new()
	particles.position = to_local(target_position)
	particles.emitting = true
	particles.one_shot = true
	particles.explosiveness = 0.8
	particles.amount = 15
	particles.lifetime = 0.5
	particles.direction = Vector2(0, -1)
	particles.spread = 90
	particles.gravity = Vector2(0, 200)
	particles.initial_velocity_min = 50
	particles.initial_velocity_max = 100
	particles.scale_amount_min = 2
	particles.scale_amount_max = 4
	particles.color = effect_color
	add_child(particles)

	# Tự động xóa sau khi phát xong
	await get_tree().create_timer(particles.lifetime * 1.2).timeout
	if is_instance_valid(particles):
		particles.queue_free()

# Tạo hiệu ứng dash
func _create_dash_effect() -> void:
	# Tạo hiệu ứng mờ khi dash
	var trail_effect = CPUParticles2D.new()
	trail_effect.position = Vector2.ZERO
	trail_effect.emitting = true
	trail_effect.one_shot = true
	trail_effect.explosiveness = 0.1
	trail_effect.amount = 10
	trail_effect.lifetime = 0.5
	trail_effect.local_coords = false
	trail_effect.direction = Vector2(-1 if _facing_right else 1, 0)
	trail_effect.spread = 30
	trail_effect.gravity = Vector2.ZERO
	trail_effect.initial_velocity_min = 20
	trail_effect.initial_velocity_max = 40
	trail_effect.scale_amount_min = 5
	trail_effect.scale_amount_max = 10

	# Màu hiệu ứng dựa trên loại kẻ địch
	match type:
		"sword":
			trail_effect.color = Color(1.0, 0.3, 0.3, 0.5)  # Đỏ
		"spear":
			trail_effect.color = Color(0.3, 1.0, 0.3, 0.5)  # Xanh lá
		"bow":
			trail_effect.color = Color(0.3, 0.3, 1.0, 0.5)  # Xanh dương
		"boss":
			trail_effect.color = Color(1.0, 0.8, 0.2, 0.5)  # Vàng
		_:
			trail_effect.color = Color(0.7, 0.7, 0.7, 0.5)  # Xám

	add_child(trail_effect)

	# Tự động xóa sau khi phát xong
	await get_tree().create_timer(trail_effect.lifetime * 1.2).timeout
	if is_instance_valid(trail_effect):
		trail_effect.queue_free()

# Tính toán vị trí tấn công tối ưu xung quanh mục tiêu
func _calculate_optimal_attack_position(target: Node2D) -> Vector2:
	if not is_instance_valid(target):
		return global_position

	# Lấy vị trí của mục tiêu
	var target_pos = target.global_position

	# Lấy danh sách kẻ địch đang tấn công cùng mục tiêu
	var enemies = get_tree().get_nodes_in_group("enemy_group")
	var attacking_enemies = []

	for enemy in enemies:
		if enemy != self and is_instance_valid(enemy) and enemy.has_method("get_attack_target"):
			var enemy_target = enemy.get_attack_target()
			if is_instance_valid(enemy_target) and enemy_target == target:
				attacking_enemies.append(enemy)

	# Nếu không có kẻ địch khác đang tấn công, chọn vị trí ngẫu nhiên xung quanh mục tiêu
	if attacking_enemies.size() == 0:
		# Chọn một góc ngẫu nhiên xung quanh mục tiêu
		var angle = randf() * 2.0 * PI
		var distance = attack_range * 0.8  # Hơi gần hơn phạm vi tấn công
		var offset = Vector2(cos(angle), sin(angle)) * distance
		return target_pos + offset

	# Nếu có kẻ địch khác đang tấn công, tìm vị trí còn trống
	var occupied_angles = []
	for enemy in attacking_enemies:
		var enemy_pos = enemy.global_position
		var to_enemy = enemy_pos - target_pos
		if to_enemy.length() > 0:
			var angle = atan2(to_enemy.y, to_enemy.x)
			occupied_angles.append(angle)

	# Tìm góc trống lớn nhất
	var best_angle = 0
	var max_gap = 0

	if occupied_angles.size() > 0:
		# Sắp xếp các góc
		occupied_angles.sort()

		# Thêm góc đầu tiên vào cuối để tạo vòng tròn
		occupied_angles.append(occupied_angles[0] + 2.0 * PI)

		# Tìm khoảng trống lớn nhất
		for i in range(occupied_angles.size() - 1):
			var gap = occupied_angles[i + 1] - occupied_angles[i]
			if gap > max_gap:
				max_gap = gap
				best_angle = occupied_angles[i] + gap / 2.0
	else:
		# Nếu không có góc nào bị chiếm, chọn góc ngẫu nhiên
		best_angle = randf() * 2.0 * PI

	# Tính toán vị trí từ góc tốt nhất
	var distance = attack_range * 0.8
	var offset = Vector2(cos(best_angle), sin(best_angle)) * distance
	return target_pos + offset

# Hàm trả về mục tiêu tấn công hiện tại
func get_attack_target() -> CharacterBody2D:
	return attack_target

func _handle_follow_behavior(delta: float) -> void:
	# Kiểm tra xem mục tiêu có còn tồn tại không
	if not is_instance_valid(follow_target):
		follow_target = null
		return

	var to_player = follow_target.global_position - global_position
	var dist = to_player.length()

	# Kiểm tra xem có cần nhảy để tiếp cận mục tiêu không
	_check_and_jump(follow_target.global_position)

	# Luôn di chuyển về phía người chơi, bất kể khoảng cách
	var dir = to_player.normalized()
	velocity.x = lerp(velocity.x, dir.x * speed, acceleration * delta)
	_update_facing(dir.x)
	_play_run()

	# Nếu đủ gần, chuyển sang tấn công
	if dist <= attack_range:
		attack_target = follow_target
		follow_target = null

func _enter_idle() -> void:
	velocity.x = lerp(velocity.x, 0.0, acceleration * get_physics_process_delta_time())
	if animated_sprite.animation != "idle":
		animated_sprite.play("idle")

# --- Helpers -------------------------------------------------------------
func _update_facing(dir_x: float) -> void:
	_facing_right = dir_x > 0
	hitbox_area.scale.x = 1 if _facing_right else -1
	animated_sprite.flip_h = not _facing_right

func _play_run() -> void:
	if animated_sprite.animation != "run":
		animated_sprite.play("run")

func _get_gravity_value() -> Vector2:
	return Vector2(0, 980)

# Kiểm tra và thực hiện nhảy nếu cần thiết
func _check_and_jump(target_position: Vector2) -> void:
	# Chỉ nhảy khi đang ở trên mặt đất và hết thời gian chờ nhảy
	if is_on_floor() and jump_cooldown <= 0:
		var height_diff = global_position.y - target_position.y
		var horizontal_diff = abs(global_position.x - target_position.x)

		# Nếu mục tiêu ở cao hơn một khoảng nhất định, nhảy lên
		if height_diff < -jump_detection_height * 0.3:  # Giảm ngưỡng để nhảy thường xuyên hơn
			# Tính toán lực nhảy dựa trên độ cao cần vượt qua
			var jump_power = 1.5  # Tăng lực nhảy mặc định

			# Tăng lực nhảy nếu mục tiêu ở rất cao
			if height_diff < -jump_detection_height * 1.5:
				jump_power = 2.5  # Nhảy rất cao cho mục tiêu ở rất cao
			elif height_diff < -jump_detection_height:
				jump_power = 2.0  # Nhảy cao cho mục tiêu ở cao

			# Thêm một chút vận tốc ngang để tiếp cận mục tiêu tốt hơn
			var horizontal_dir = sign(target_position.x - global_position.x)

			# Điều chỉnh vận tốc ngang dựa trên khoảng cách ngang
			var horizontal_speed_factor = 1.0
			if horizontal_diff > 100:
				horizontal_speed_factor = 1.5  # Tăng tốc nếu mục tiêu ở xa

			velocity.x = horizontal_dir * speed * horizontal_speed_factor
			velocity.y = jump_velocity * jump_power
			is_jumping = true
			can_double_jump = true  # Reset khả năng nhảy đôi khi bắt đầu nhảy
			jump_cooldown = jump_cooldown_time * 0.3  # Giảm thời gian chờ
			animated_sprite.play("run")  # Sử dụng animation phù hợp
			# Nhảy lên để tiếp cận mục tiêu ở trên cao

		# Nếu mục tiêu ở gần nhưng cao hơn một chút, vẫn cố gắng nhảy
		elif height_diff < -jump_detection_height * 0.2 and horizontal_diff < 150.0:
			velocity.y = jump_velocity * 1.2  # Tăng lực nhảy
			is_jumping = true
			can_double_jump = true  # Reset khả năng nhảy đôi khi bắt đầu nhảy
			jump_cooldown = jump_cooldown_time * 0.5
			animated_sprite.play("run")
			# Nhảy nhỏ để tiếp cận mục tiêu ở cao hơn một chút

		# Nếu mục tiêu ở xa và có chướng ngại vật, thử nhảy để vượt qua
		elif horizontal_diff > 50.0 and randf() < 0.3:  # 30% cơ hội nhảy ngẫu nhiên khi đuổi theo mục tiêu xa
			velocity.y = jump_velocity * 1.0  # Tăng lực nhảy
			is_jumping = true
			can_double_jump = true  # Reset khả năng nhảy đôi khi bắt đầu nhảy
			jump_cooldown = jump_cooldown_time * 0.5
			animated_sprite.play("run")  # Sử dụng animation phù hợp
			# Nhảy chiến thuật để vượt qua chướng ngại vật

# Tìm đồng minh gần nhất trên toàn bản đồ
func _find_closest_ally() -> CharacterBody2D:
	var allies = get_tree().get_nodes_in_group("ally_group")
	# Tìm đồng minh trong nhóm

	var closest_ally: CharacterBody2D = null
	var closest_distance = 999999.0  # Không giới hạn phạm vi tìm kiếm - tìm trên toàn bản đồ

	# Biến để theo dõi đồng minh ở vị trí cao hơn
	var highest_ally: CharacterBody2D = null
	var highest_ally_height_diff = 0
	var highest_ally_distance = 999999.0

	for ally in allies:
		if ally is CharacterBody2D and is_instance_valid(ally):
			# Kiểm tra an toàn thuộc tính is_dead
			var is_ally_dead = false
			if ally.has_method("is_dead") or ally.get("is_dead") != null:
				is_ally_dead = ally.is_dead

			if not is_ally_dead:
				var distance = global_position.distance_to(ally.global_position)
				var height_diff = global_position.y - ally.global_position.y

				# Tìm đồng minh gần nhất trên toàn bản đồ
				if distance < closest_distance:
					closest_distance = distance
					closest_ally = ally as CharacterBody2D

				# Tìm đồng minh ở vị trí cao hơn
				if height_diff < 0 and abs(height_diff) > jump_detection_height * 0.3:
					if abs(height_diff) > abs(highest_ally_height_diff) or (abs(height_diff) > jump_detection_height * 0.8 and distance < highest_ally_distance):
						highest_ally_height_diff = height_diff
						highest_ally = ally as CharacterBody2D
						highest_ally_distance = distance

	# Ưu tiên tấn công đồng minh ở vị trí cao hơn nếu có
	if highest_ally:
		print("Found ally at higher position, prioritizing it")
		return highest_ally

	return closest_ally

# Chủ động tìm kiếm mục tiêu trên toàn bản đồ
func _search_for_targets() -> void:
	# Thử tìm đồng minh một cách chủ động
	if search_jump_timer <= 0:
		# Tìm kiếm đồng minh trên toàn bản đồ
		var allies = get_tree().get_nodes_in_group("ally_group")
		if allies.size() > 0:
			# Kiểm tra xem có đồng minh ở trên cao không
			var ally_above = false
			var highest_ally: CharacterBody2D = null
			var search_range = 999999.0  # Không giới hạn phạm vi tìm kiếm - tìm trên toàn bản đồ

			for ally in allies:
				if ally is CharacterBody2D and is_instance_valid(ally) and not (ally.has_method("is_dead") and ally.is_dead() or ally.get("is_dead") == true):
					var height_diff = global_position.y - ally.global_position.y
					if height_diff < -jump_detection_height * 0.3:  # Giảm ngưỡng để phát hiện đồng minh ở cao hơn
						ally_above = true
						highest_ally = ally as CharacterBody2D
						break

			# Nhảy chủ động để tìm kiếm đồng minh
			if is_on_floor() and jump_cooldown <= 0:
				var jump_chance = 0.3  # 30% cơ hội nhảy khi tìm kiếm
				var jump_power = 1.0

				# Tăng cơ hội nhảy và lực nhảy nếu có đồng minh ở trên cao
				if ally_above:
					jump_chance = 0.9  # 90% cơ hội nhảy khi có đồng minh ở trên cao
					jump_power = 1.8  # Nhảy cao hơn rất nhiều

					# Nếu biết vị trí đồng minh cao nhất, di chuyển về phía đó
					if highest_ally:
						var dir_to_ally = sign(highest_ally.global_position.x - global_position.x)
						var horizontal_diff = abs(highest_ally.global_position.x - global_position.x)

						# Điều chỉnh vận tốc ngang dựa trên khoảng cách
						var speed_factor = 1.0
						if horizontal_diff > 100:
							speed_factor = 1.5  # Tăng tốc nếu đồng minh ở xa

						velocity.x = dir_to_ally * speed * speed_factor
						_update_facing(dir_to_ally)

						# Di chuyển về phía đồng minh ở trên cao

				if randf() < jump_chance:
					velocity.y = jump_velocity * jump_power
					is_jumping = true
					jump_cooldown = jump_cooldown_time * 0.4  # Giảm thời gian chờ nhiều hơn khi tìm kiếm
					animated_sprite.play("run")
					# Nhảy để tìm kiếm đồng minh

		# Đặt lại bộ đếm thời gian tìm kiếm
		search_jump_timer = 2.0  # Kiểm tra lại sau 2 giây

	# Di chuyển ngẫu nhiên để tìm kiếm mục tiêu, nhưng tránh đi ra rìa map
	if randf() < 0.03:  # 3% cơ hội mỗi frame để thay đổi hướng
		var random_direction = randf_range(-1.0, 1.0)

		# Kiểm tra vị trí để tránh di chuyển ra khỏi map
		var viewport_size = get_viewport_rect().size
		var screen_position = global_position

		# Nếu đang ở gần rìa trái của map, ưu tiên di chuyển sang phải
		if screen_position.x < viewport_size.x * 0.1:
			random_direction = abs(random_direction)  # Đảm bảo giá trị dương (sang phải)
		# Nếu đang ở gần rìa phải của map, ưu tiên di chuyển sang trái
		elif screen_position.x > viewport_size.x * 0.9:
			random_direction = -abs(random_direction)  # Đảm bảo giá trị âm (sang trái)

		velocity.x = random_direction * speed * 0.8
		_update_facing(random_direction)
		_play_run()
	else:
		_enter_idle()

# --- Signal Callbacks ---------------------------------------------------
func _on_body_hit(body: Node) -> void:
	# Chỉ gây sát thương cho đồng minh hoặc người chơi, không gây sát thương cho kẻ địch khác
	if body.has_method("take_damage") and (body.is_in_group("ally_group") or body.is_in_group("player")):
		# Đảm bảo hitbox đã được kích hoạt và đang trong animation tấn công
		if animated_sprite.animation == "attack" or animated_sprite.animation == "attack_2":
			body.take_damage(damage)
			print("Enemy hit " + body.name + " for " + str(damage) + " damage")

			# Hiển thị hiệu ứng hit
			var damage_manager = get_node_or_null("/root/DamageEffects")
			if damage_manager:
				damage_manager.create_hit_effect(body.global_position)

				# Tạo hiệu ứng tấn công thêm
				_create_attack_effect(body.global_position)

				# Rung màn hình nhẹ khi tấn công
				if type == "boss":
					damage_manager.apply_screen_shake(3.0, 0.2)  # Rung mạnh hơn cho boss
				else:
					damage_manager.apply_screen_shake(1.5, 0.1)  # Rung nhẹ cho kẻ địch thường
			else:
				# Nếu không có damage manager, tạo hiệu ứng tấn công đơn giản
				_create_attack_effect(body.global_position)

func _on_attack_zone_enter(body: Node) -> void:
	# Xử lý khi có vật thể vào vùng tấn công

	# Không tấn công kẻ địch khác
	if body.is_in_group("enemy_group"):
		return

	# Ưu tiên tấn công đồng minh
	if body.is_in_group("ally_group") and attack_target == null:
		attack_target = body
	# Nếu không phải đồng minh, kiểm tra xem có phải người chơi không
	elif body.is_in_group("player") and attack_target == null:
		attack_target = body

func _on_attack_zone_exit(body: Node) -> void:
	if body == attack_target:
		attack_target = null

func _on_animation_finished() -> void:
	var anim = animated_sprite.animation
	match anim:
		"attack", "attack_2":
			hitbox_shape.disabled = true
			is_dashing = false  # Finish dashing after dash attack ends
		"hurt":
			is_hurt = false
			if is_dead:
				animated_sprite.play("die")
			else:
				animated_sprite.play("idle")
		"die":
			# Emit signal before freeing
			emit_signal("enemy_defeated", self)

			# Give XP to player if in range
			var player = get_tree().get_first_node_in_group("player")
			if player and player.has_method("add_xp"):
				var dist = global_position.distance_to(player.global_position)
				if dist < 500:  # Only give XP if player is nearby
					XPManager.add_xp(xp_value)

			# Spawn potion drops
			var potion_manager = get_node_or_null("/root/PotionDropManager")
			if potion_manager and potion_manager.has_method("spawn_potion_drop"):
				potion_manager.spawn_potion_drop(global_position, type)
			else:
				print("WARNING: PotionDropManager not found or doesn't have spawn_potion_drop method")

			# Spawn weapon drops
			var weapon_manager = get_node_or_null("/root/WeaponDropManager")
			if weapon_manager and weapon_manager.has_method("spawn_weapon_drop"):
				weapon_manager.spawn_weapon_drop(global_position, type)
			else:
				print("WARNING: WeaponDropManager not found or doesn't have spawn_weapon_drop method")

			queue_free()
		_:
			if is_on_floor() and anim not in ["idle", "run", "attack", "attack_2"]:
				animated_sprite.play("idle")

# --- Health & Class Adaptation -------------------------------------------
func take_damage(amount: float) -> void:
	if is_hurt or is_dead or health <= 0:
		return

	# Giảm máu
	health -= amount
	is_hurt = true
	animated_sprite.play("hurt")
	health_bar.update_health(health)


	# Hiển thị số sát thương
	var damage_manager = get_node_or_null("/root/DamageEffects")
	if damage_manager:
		# Tạo số sát thương với màu đỏ cho kẻ địch
		damage_manager.create_damage_number(global_position + Vector2(0, -20), amount, false, Color(1, 0.3, 0.3))

		# Tạo hiệu ứng hit
		damage_manager.create_hit_effect(global_position)

		# Rung màn hình nếu sát thương lớn
		if amount > 20:
			damage_manager.apply_screen_shake(3.0, 0.2)

	# Hiệu ứng flash khi bị đánh
	if animated_sprite:
		animated_sprite.modulate = Color(1.5, 1, 1)  # Đỏ nhạt
		var tween = create_tween()
		tween.tween_property(animated_sprite, "modulate", Color(1, 1, 1), 0.2)

	if health <= 0:
		is_dead = true

func adapt_to_class(tres) -> void:
	if not tres:
		print("ERROR: Null resource passed to adapt_to_class")
		return

	# Thích ứng kẻ địch với lớp mới

	# Đảm bảo animated_sprite đã được khởi tạo
	if !animated_sprite or !is_instance_valid(animated_sprite):
		animated_sprite = $AnimatedSprite2D

		if !animated_sprite:
			# Tạo mới AnimatedSprite2D nếu không tìm thấy
			animated_sprite = AnimatedSprite2D.new()
			add_child(animated_sprite)

	# Thiết lập các thuộc tính
	scale = Vector2(tres.scale, tres.scale)
	attack_cooldown = tres.cooldown
	health = tres.health
	damage = tres.damage
	speed = tres.speed * 30
	jump_velocity = tres.agility * 30
	detection_range = tres.detection_range
	xp_value = tres.xp_value

	# Thiết lập sprite frames
	if tres.get("sprite_frames"):
		animated_sprite.sprite_frames = tres.sprite_frames

	# Cập nhật loại
	type = tres.name

	# Phát animation
	animated_sprite.play("idle")

# Áp dụng hiệu ứng phân biệt cho kẻ địch
func _apply_visual_distinction() -> void:
	# Đảm bảo kẻ địch thuộc nhóm enemy_group để phân tán
	if not is_in_group("enemy_group"):
		add_to_group("enemy_group")

	# Thêm hiệu ứng màu sắc và hình ảnh cho kẻ địch
	if animated_sprite:
		# Tạo màu sắc dựa trên loại kẻ địch
		var base_color = Color(1.0, 1.0, 1.0)
		var enemy_color = Color(1.0, 1.0, 1.0)

		# Màu sắc dựa trên loại kẻ địch
		match type:
			"sword":
				# Màu đỏ cho kẻ địch kiếm
				enemy_color = Color(1.2, 0.7, 0.7)
			"spear":
				# Màu xanh lá cho kẻ địch giáo
				enemy_color = Color(0.7, 1.2, 0.7)
			"bow":
				# Màu xanh dương cho kẻ địch cung
				enemy_color = Color(0.7, 0.7, 1.2)
			"boss":
				# Màu vàng cho boss
				enemy_color = Color(1.5, 1.2, 0.5)
			_:
				# Màu ngẫu nhiên cho các loại khác
				var hue = randf()
				enemy_color = Color.from_hsv(hue, 0.7, 1.0)

		# Thêm biến thiên ngẫu nhiên nhỏ để phân biệt giữa các kẻ địch cùng loại
		var hue_variation = randf_range(-0.05, 0.05)
		var value_variation = randf_range(-0.1, 0.1)

		# Tạo màu mới dựa trên màu loại kẻ địch với biến thiên ngẫu nhiên
		var new_color = Color(
			clamp(enemy_color.r + value_variation, 0.5, 1.5),
			clamp(enemy_color.g + value_variation, 0.5, 1.5),
			clamp(enemy_color.b + value_variation, 0.5, 1.5),
			1.0
		)

		# Áp dụng màu mới
		animated_sprite.modulate = new_color

		# Thêm hiệu ứng đổ bóng cho kẻ địch
		var shadow = ColorRect.new()
		shadow.name = "EnemyShadow"
		shadow.color = Color(0, 0, 0, 0.3)
		shadow.size = Vector2(30, 10)
		shadow.position = Vector2(-15, -5)
		add_child(shadow)

		# Đặt shadow ở dưới sprite
		shadow.z_index = -1

		# Thêm hiệu ứng phát sáng cho boss
		if type == "boss":
			var glow_effect = CPUParticles2D.new()
			glow_effect.name = "BossGlowEffect"
			glow_effect.amount = 20
			glow_effect.lifetime = 1.0
			glow_effect.emission_shape = CPUParticles2D.EMISSION_SHAPE_SPHERE
			glow_effect.emission_sphere_radius = 30.0
			glow_effect.gravity = Vector2(0, -20)
			glow_effect.color = Color(1.0, 0.7, 0.2, 0.5)
			add_child(glow_effect)

			# Đặt hiệu ứng phát sáng ở dưới sprite
			glow_effect.z_index = -1

# Tìm kiếm mục tiêu ban đầu khi khởi tạo
func _find_initial_target() -> void:
	# Tìm đồng minh gần nhất trên toàn bản đồ
	var closest_ally = _find_closest_ally()

	# Nếu tìm thấy đồng minh, đặt làm mục tiêu tấn công
	if closest_ally:
		attack_target = closest_ally

		# Nếu đồng minh ở trên cao, chuẩn bị nhảy
		if is_on_floor():
			var height_diff = global_position.y - closest_ally.global_position.y
			if height_diff < -jump_detection_height * 0.3:
				# Nhảy ngay lập tức để tiếp cận đồng minh
				var jump_power = 2.0
				velocity.y = jump_velocity * jump_power
				is_jumping = true
				can_double_jump = true
				jump_cooldown = 0.1  # Thời gian chờ rất ngắn

				# Di chuyển về phía đồng minh
				var dir_to_ally = sign(closest_ally.global_position.x - global_position.x)
				velocity.x = dir_to_ally * speed * 1.5
				_update_facing(dir_to_ally)
				animated_sprite.play("run")

# Kiểm tra vị trí để tránh di chuyển ra khỏi map
func _check_map_boundaries() -> void:
	var viewport_size = get_viewport_rect().size
	var screen_position = global_position

	# Nếu đang ở gần rìa trái của map và đang di chuyển sang trái, đổi hướng
	if screen_position.x < viewport_size.x * 0.1 and velocity.x < 0:
		velocity.x = abs(velocity.x)  # Đổi hướng sang phải
		_update_facing(1)

	# Nếu đang ở gần rìa phải của map và đang di chuyển sang phải, đổi hướng
	elif screen_position.x > viewport_size.x * 0.9 and velocity.x > 0:
		velocity.x = -abs(velocity.x)  # Đổi hướng sang trái
		_update_facing(-1)

# Tìm mục tiêu trong tầm tấn công
func _find_target_in_range() -> CharacterBody2D:
	var allies = get_tree().get_nodes_in_group("ally_group")
	var player = get_tree().get_first_node_in_group("player")
	var targets = []

	# Thêm tất cả đồng minh vào danh sách mục tiêu
	for ally in allies:
		if is_instance_valid(ally) and not (ally.has_method("is_dead") and ally.is_dead() or ally.get("is_dead") == true):
			# Đảm bảo ally là CharacterBody2D
			if ally is CharacterBody2D:
				targets.append(ally)

	# Thêm người chơi vào danh sách mục tiêu nếu không có đồng minh
	if targets.size() == 0 and is_instance_valid(player) and player is CharacterBody2D:
		targets.append(player)

	# Tìm mục tiêu gần nhất trong tầm tấn công
	var closest_target: CharacterBody2D = null
	var closest_distance = attack_range * 1.5  # Mở rộng phạm vi một chút

	for target in targets:
		if is_instance_valid(target) and target is CharacterBody2D:
			var distance = global_position.distance_to(target.global_position)
			var height_diff = abs(global_position.y - target.global_position.y)

			# Chỉ xét mục tiêu trong tầm tấn công và không quá cao hoặc thấp
			if distance < closest_distance and height_diff < jump_detection_height * 0.5:
				closest_distance = distance
				closest_target = target

	return closest_target


# Áp dụng lực đẩy để tránh chồng chéo với kẻ địch khác
func _apply_separation_force(_delta: float) -> void:
	var enemies = get_tree().get_nodes_in_group("enemy_group")
	var separation_force = Vector2.ZERO
	var separation_count = 0
	var separation_radius = 50.0  # Khoảng cách tối thiểu giữa các kẻ địch - tăng lên để phân tán rộng hơn

	# Đảm bảo kẻ địch thuộc nhóm enemy_group
	if not is_in_group("enemy_group"):
		add_to_group("enemy_group")

	# Tính toán lực đẩy từ các kẻ địch khác
	for enemy in enemies:
		if enemy != self and is_instance_valid(enemy):
			var distance = global_position.distance_to(enemy.global_position)
			if distance < separation_radius:
				var direction = global_position - enemy.global_position
				if direction.length() > 0:
					direction = direction.normalized()

					# Lực đẩy mạnh hơn khi kẻ địch quá gần nhau
					var force_strength = 1.0
					if distance < 20.0:
						force_strength = 3.0  # Lực đẩy mạnh hơn khi quá gần

					# Lực đẩy tỷ lệ nghịch với khoảng cách
					var force = direction * (separation_radius - distance) / separation_radius * force_strength

					# Thêm một chút lực đẩy ngẫu nhiên để tránh kẻ địch xếp thành hàng
					var random_offset = Vector2(randf_range(-0.2, 0.2), randf_range(-0.1, 0.1))
					force += random_offset

					separation_force += force
					separation_count += 1

	# Áp dụng lực đẩy nếu có
	if separation_count > 0:
		separation_force = separation_force / separation_count

		# Áp dụng lực đẩy vào vận tốc hiện tại
		# Tăng hệ số để phân tán mạnh hơn
		velocity += separation_force * 80.0 * _delta

		# Giới hạn tốc độ phân tán để không quá nhanh
		var max_separation_speed = 100.0
		if velocity.length() > max_separation_speed:
			velocity = velocity.normalized() * max_separation_speed
