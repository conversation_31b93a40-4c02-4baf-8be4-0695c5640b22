# AbilityDefinition.gd
extends Resource
class_name AbilityDefinition

@export var ability_name: String = "Unnamed Ability"
@export var animation_name: String = ""       # The animation to play on activation.
@export var base_damage: int = 0                # Base damage before any multipliers.
@export var cooldown: float = 1.0               # Cooldown in seconds.
@export var mana_cost: int = 0                  # Optional cost to use the ability.
@export var chain_options: Array[String] = []           # Array of ability keys (strings) for valid follow-up attacks.
