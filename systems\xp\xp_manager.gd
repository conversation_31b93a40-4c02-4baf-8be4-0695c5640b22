# XPManager.gd manages user's global XP
extends Node

signal xp_changed(new_xp)
signal level_changed(new_level)

var player: Player = null

func set_up_player(player_custom: Player):
	player = player_custom
	RankingSystem.set_up_player(player_custom)

func xp_required_for_next_level() -> int:
	# Kiểm tra nếu player ch<PERSON><PERSON> được khởi tạo
	if player == null:
		return 100 # <PERSON><PERSON><PERSON> trị mặc định

	return player.level * 100

func add_xp(amount: int) -> void:
	# Kiểm tra nếu player chư<PERSON> được khởi tạo
	if player == null:
		return

	player.xp += amount
	emit_signal("xp_changed", player.xp)
	check_level_up()
	RankingSystem.check_for_rank_up()

func check_level_up() -> void:
	# Kiểm tra nếu player chưa được khởi tạo
	if player == null:
		return

	var xp_required = xp_required_for_next_level()

	while player.xp >= xp_required:
		player.xp -= xp_required
		player.level += 1
		emit_signal("level_changed", player.level)
		# optionally add effects
		print("Level up! New level: " + str(player.level))

		# Cập nhật xp_required cho vòng lặp tiếp theo
		xp_required = xp_required_for_next_level()
