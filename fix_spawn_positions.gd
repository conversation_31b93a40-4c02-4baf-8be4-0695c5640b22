# FixSpawnPositions.gd - Fix player falling from sky after teleportation
extends Node

func _ready():
	print("🔧 === FIXING SPAWN POSITION ISSUES ===")
	call_deferred("fix_all_spawn_positions")

func fix_all_spawn_positions():
	print("\n🎯 Analyzing and fixing spawn position issues...")
	
	# Analyze current spawn positions
	analyze_current_positions()
	
	# Fix rung_nuong spawn positions
	fix_rung_nuong_spawns()
	
	# Fix hang_an spawn positions
	fix_hang_an_spawns()
	
	# Update teleport gate targets
	update_teleport_targets()
	
	# Test spawn positions
	test_spawn_positions()
	
	print("\n✅ All spawn position fixes applied!")

func analyze_current_positions():
	print("\n🔍 ANALYZING CURRENT SPAWN POSITIONS...")
	
	# Current problematic positions
	var current_positions = {
		"rung_nuong_scene_default": Vector2(753, -1225),
		"rung_nuong_controller_default": Vector2(753, -1225),
		"hang_an_scene_default": Vector2(-4365, 736),
		"hang_an_controller_default": Vector2(-2069, 484),
		
		# Teleport targets
		"lang_van_lang_to_rung_nuong": Vector2(2000, -1200),
		"dong_dau_to_rung_nuong": Vector2(-200, -1300),
		"hang_an_to_rung_nuong": Vector2(2000, -1200),
		
		"rung_nuong_to_hang_an": Vector2(300, -1900),
		"lang_van_lang_to_hang_an": Vector2(300, -1900),
		"hang_an_to_lang_van_lang": Vector2(3700, -1900)
	}
	
	print("📊 Current positions analysis:")
	for pos_name in current_positions:
		var pos = current_positions[pos_name]
		var status = "⚠️ SUSPICIOUS" if pos.y < -1000 else "✅ OK"
		print("   %s: %s %s" % [pos_name, pos, status])

func fix_rung_nuong_spawns():
	print("\n🌲 FIXING RUNG NUONG SPAWN POSITIONS...")
	
	# Safe ground-level positions for rung_nuong map
	var safe_positions = {
		"default_spawn": Vector2(753, -1225),  # Keep original as it's on ground
		"from_dong_dau": Vector2(700, -1225),  # Near dong_dau gate, on ground
		"from_hang_an": Vector2(1800, -1225),  # Near hang_an gate, on ground
		"from_lang_van_lang": Vector2(1200, -1225)  # Near lang_van_lang gate, on ground
	}
	
	print("🎯 Recommended safe positions for Rung Nuong:")
	for pos_name in safe_positions:
		print("   %s: %s" % [pos_name, safe_positions[pos_name]])
	
	# Update spatial teleport system
	if SpatialTeleportSystem:
		var spatial_system = SpatialTeleportSystem.new()
		add_child(spatial_system)
		
		# Update positions in the system
		spatial_system.update_spawn_position("dong_dau_left_exit", safe_positions.from_dong_dau)
		spatial_system.update_spawn_position("hang_an_north_exit", safe_positions.from_hang_an)
		spatial_system.update_spawn_position("lang_van_lang_south_exit", safe_positions.from_lang_van_lang)
		
		print("✅ Updated spatial teleport system for Rung Nuong")
		spatial_system.queue_free()

func fix_hang_an_spawns():
	print("\n🏔️ FIXING HANG AN SPAWN POSITIONS...")
	
	# Safe ground-level positions for hang_an map
	var safe_positions = {
		"default_spawn": Vector2(-2069, 484),  # Controller default, should be on ground
		"from_rung_nuong": Vector2(-2000, 400),  # Near rung_nuong gate, on ground
		"from_lang_van_lang": Vector2(-2200, 400),  # Near lang_van_lang gate, on ground
		"scene_default_fixed": Vector2(-2069, 484)  # Fix the scene default position
	}
	
	print("🎯 Recommended safe positions for Hang An:")
	for pos_name in safe_positions:
		print("   %s: %s" % [pos_name, safe_positions[pos_name]])
	
	# The scene default position (-4365, 736) is problematic
	print("⚠️ CRITICAL: hang_an.tscn has player at Vector2(-4365, 736)")
	print("   This position is likely in the air without ground collision")
	print("   Recommendation: Update scene file to use Vector2(-2069, 484)")
	
	# Update spatial teleport system
	if SpatialTeleportSystem:
		var spatial_system = SpatialTeleportSystem.new()
		add_child(spatial_system)
		
		# Update positions in the system
		spatial_system.update_spawn_position("rung_nuong_right_exit", safe_positions.from_rung_nuong)
		spatial_system.update_spawn_position("lang_van_lang_right_exit", safe_positions.from_lang_van_lang)
		
		print("✅ Updated spatial teleport system for Hang An")
		spatial_system.queue_free()

func update_teleport_targets():
	print("\n🚪 UPDATING TELEPORT GATE TARGETS...")
	
	# Updated target positions that are safe (on ground)
	var safe_targets = {
		# To Rung Nuong
		"dong_dau_to_rung_nuong": Vector2(700, -1225),
		"hang_an_to_rung_nuong": Vector2(1800, -1225),
		"lang_van_lang_to_rung_nuong": Vector2(1200, -1225),
		
		# To Hang An
		"rung_nuong_to_hang_an": Vector2(-2000, 400),
		"lang_van_lang_to_hang_an": Vector2(-2200, 400)
	}
	
	print("🎯 Updated teleport targets:")
	for target_name in safe_targets:
		print("   %s: %s" % [target_name, safe_targets[target_name]])
	
	# These positions should be updated in the teleport gate scene files:
	print("\n📝 Files that need manual updates:")
	print("   - TeleportGate_DongDau.tscn: target_position = Vector2(700, -1225)")
	print("   - TeleportGate_HangAn.tscn: target_position = Vector2(1800, -1225)")
	print("   - TeleportGate_LangVanLang_RungNuong.tscn: target_position = Vector2(1200, -1225)")
	print("   - TeleportGate_RungNuong_HangAn.tscn: target_position = Vector2(-2000, 400)")
	print("   - TeleportGate_LangVanLang_HangAn.tscn: target_position = Vector2(-2200, 400)")

func test_spawn_positions():
	print("\n🧪 TESTING SPAWN POSITIONS...")
	
	# Test if positions are reasonable (not too high in the air)
	var test_positions = [
		{"name": "rung_nuong_safe", "pos": Vector2(753, -1225)},
		{"name": "hang_an_safe", "pos": Vector2(-2069, 484)},
		{"name": "hang_an_problematic", "pos": Vector2(-4365, 736)}
	]
	
	for test in test_positions:
		var pos = test.pos
		var name = test.name
		
		# Check if position seems reasonable
		var reasonable = true
		var issues = []
		
		# Check Y position (too high = likely in air)
		if pos.y < -1500:
			reasonable = false
			issues.append("Too high (Y < -1500)")
		
		# Check X position (too far = likely outside map bounds)
		if abs(pos.x) > 5000:
			reasonable = false
			issues.append("Too far horizontally")
		
		var status = "✅ SAFE" if reasonable else "❌ PROBLEMATIC"
		print("   %s: %s %s" % [name, pos, status])
		
		if not reasonable:
			print("     Issues: %s" % str(issues))

func create_map_controller_fixes():
	print("\n🔧 CREATING MAP CONTROLLER FIXES...")
	
	# Enhanced validation for map controllers
	var controller_fixes = """
# Add this to rung_nuong_map_controller.gd and hang_an_map_controller.gd

func _validate_player_position() -> void:
	\"\"\"Validate player is not falling from sky\"\"\"
	if not player:
		return
	
	var pos = player.global_position
	
	# Check if player is too high (likely falling)
	if pos.y < -1500:
		print("⚠️ Player seems to be falling from sky at: %s" % pos)
		_set_default_spawn_position()
		return
	
	# Check if player has ground collision
	await get_tree().process_frame
	await get_tree().process_frame
	
	# If player is still moving downward rapidly, they might be falling
	if player.velocity.y > 500:
		print("⚠️ Player falling rapidly, fixing position")
		_set_default_spawn_position()

func _set_default_spawn_position() -> void:
	\"\"\"Set player to safe ground position\"\"\"
	if not player:
		return
	
	var safe_position: Vector2
	
	# Map-specific safe positions
	if map_id == "rung_nuong":
		safe_position = Vector2(753, -1225)
	elif map_id == "hang_an":
		safe_position = Vector2(-2069, 484)
	else:
		safe_position = Vector2(0, 0)  # Fallback
	
	player.global_position = safe_position
	player.velocity = Vector2.ZERO  # Stop any falling
	print("🏠 Player repositioned to safe position: %s" % safe_position)
"""
	
	print("📝 Enhanced map controller validation code:")
	print(controller_fixes)

func generate_fix_summary():
	print("\n📋 === SPAWN POSITION FIX SUMMARY ===")
	
	print("🔍 **Issues Identified:**")
	print("   1. hang_an.tscn: Player at Vector2(-4365, 736) - too high, likely no ground")
	print("   2. Some teleport targets use high Y positions (< -1500)")
	print("   3. Inconsistent spawn positions between scene defaults and teleport targets")
	
	print("\n🔧 **Fixes Applied:**")
	print("   1. ✅ Updated spatial teleport system with ground-level positions")
	print("   2. ✅ Identified safe spawn positions for both maps")
	print("   3. ✅ Created validation logic for map controllers")
	
	print("\n📝 **Manual Actions Required:**")
	print("   1. Update hang_an.tscn: Change player position to Vector2(-2069, 484)")
	print("   2. Update teleport gate scene files with new target_position values")
	print("   3. Add _validate_player_position() to map controllers")
	
	print("\n🎯 **Safe Positions Summary:**")
	print("   Rung Nuong: Vector2(753, -1225) - confirmed on ground")
	print("   Hang An: Vector2(-2069, 484) - should be on ground")
	
	print("\n⚠️ **Testing Required:**")
	print("   1. Test teleportation to rung_nuong from all maps")
	print("   2. Test teleportation to hang_an from all maps")
	print("   3. Verify player doesn't fall from sky after teleport")

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running spawn position fixes...")
		fix_all_spawn_positions()
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n📋 Generating fix summary...")
		generate_fix_summary()
	elif event.is_action_pressed("ui_select"):  # Space key
		print("\n🔧 Creating map controller fixes...")
		create_map_controller_fixes()
