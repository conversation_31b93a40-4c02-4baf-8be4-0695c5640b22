extends CharacterBody2D

@export var sprites: Sprite<PERSON>ram<PERSON>
@export var npc_name: String = "NPC"
@export var dialog_text: String = "Xin chào!"

@onready var animatedSprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var nameLabel: Label = $NameLabel
@onready var clickableButton: Button = $ClickableButton

const SPEED = 300.0
const JUMP_VELOCITY = -400.0
const GRAVITY = 980.0

# Preload dialog box scene
var DialogBoxScene = preload("res://npcs/scenes/DialogBox.tscn")
var dialog_box = null

func _ready() -> void:
	# Set sprite frames
	animatedSprite.sprite_frames = sprites

	# Set NPC name
	nameLabel.text = npc_name

	# Connect click signal
	clickableButton.pressed.connect(_on_clickable_button_pressed)

	# Đảm bảo button có thể nhận input
	clickableButton.mouse_filter = Control.MOUSE_FILTER_STOP

	print("NPC " + npc_name + " đã được khởi tạo")

func _physics_process(delta: float) -> void:
	# Add the gravity.
	if not is_on_floor():
		velocity.y += GRAVITY * delta

	# NPCs don't move by default
	velocity.x = 0

	move_and_slide()

func _on_clickable_button_pressed():
	print(npc_name + ": Đã click vào NPC")
	_show_dialog()

func _show_dialog():
	print(npc_name + ": " + dialog_text)

	# Tạo hộp thoại mới mỗi lần click
	# Xóa hộp thoại cũ nếu có
	if dialog_box != null:
		dialog_box.queue_free()
		dialog_box = null

	# Tạo hộp thoại mới
	dialog_box = DialogBoxScene.instantiate()
	get_tree().root.add_child(dialog_box)
	dialog_box.dialog_closed.connect(_on_dialog_closed)

	# Hiển thị hộp thoại
	dialog_box.show_dialog(self, npc_name, dialog_text)

func _on_dialog_closed():
	# Xử lý khi hộp thoại đóng
	pass
