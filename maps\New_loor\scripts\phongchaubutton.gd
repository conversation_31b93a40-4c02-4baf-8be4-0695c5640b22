extends TextureButton

signal hovered(title: String, description: String)
signal unhovered

@export_file("*.tscn") var target_scene: String = "res://maps/lang_van_lang/scenes/lang_van_lang.tscn"
@export var button_title: String = "<PERSON>ong Châu"
@export var button_description: String = "Vào thế kỷ 7 trước công <PERSON>, nhà nước Văn Lang ra đời trên nền văn hóa <PERSON> Sơn, với kinh đô <PERSON> Châu (Việt Trì, Phú Thọ ngà<PERSON> nay)."

# ----- Constants -----
# Không sử dụng hiệu ứng scale

# ----- Variables -----
@export var is_locked: bool = true  # Mặc định là khóa

# ----- Lifecycle -----
func _ready() -> void:
    if not is_instance_valid(self):
        push_error("TextureButton instance is invalid")
        return

    # Không thay đổi kích thước
    if target_scene.is_empty():
        push_error("Target scene path is empty")
        return

    if not FileAccess.file_exists(target_scene):
        push_error("Target scene file does not exist: " + target_scene)
        return

    mouse_entered.connect(_on_mouse_entered)
    mouse_exited.connect(_on_mouse_exited)
    pressed.connect(_on_pressed)


# ----- Event Handlers -----
func _on_mouse_entered() -> void:
    if not is_instance_valid(self):
        return

    if is_locked:
        # Nếu bị khóa, chỉ emit signal với thông báo khóa
        hovered.emit(button_title, "Khu vực này hiện đang bị khóa. Hãy hoàn thành nhiệm vụ trước để mở khóa.")
        return

    # Không thay đổi kích thước khi hover
    hovered.emit(button_title, button_description)

func _on_mouse_exited() -> void:
    if not is_instance_valid(self):
        return

    # Không thay đổi kích thước khi unhover
    unhovered.emit()

func _on_pressed() -> void:
    if not is_instance_valid(self):
        return

    if is_locked:
        # Phát hiệu ứng âm thanh hoặc visual cho biết đang khóa
        print("Phong Châu area is locked!")
        return

    if not FileAccess.file_exists(target_scene):
        push_error("Cannot load target scene: " + target_scene)
        return

    # Không thay đổi kích thước khi nhấn, chỉ chuyển scene
    await get_tree().create_timer(0.1).timeout
    SceneManager.goto_scene(target_scene)

# ----- Public Methods -----
func unlock() -> void:
    is_locked = false
    print("Phong Châu area unlocked!")

func lock() -> void:
    is_locked = true
    print("Phong Châu area locked!")





