extends Node
class_name AnimalManager

var classes: Dictionary = {
	"boar": preload("res://animals/resources/black_boar.tres"),
	"bee": preload("res://animals/resources/bee.tres"),
	"snail": preload("res://animals/resources/snail.tres")
}

func find_class(name: String) -> AnimalDefinition:
	return classes[name]

func adapt_to_class(owner: Animal):
	var def = find_class(owner.type)
	owner.isgoing = def.isgoing
	owner.isflying = def.isflying
	owner.scale *= def.scale
	owner.animatedSprite.sprite_frames = def.animatedSprite
	owner.collisionShape.shape = def.collisionShape
