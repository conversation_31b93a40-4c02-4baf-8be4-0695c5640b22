# Script quản lý việc rơi vũ khí (weapon_drop_manager.gd)
extends Node

# Preload scene cho weapon drop
var weapon_drop_scene = preload("res://Items/Weapon/scene/weapon_drop.tscn")

# Tỉ lệ rơi vũ khí (%)
@export var sword_drop_rate: float = 8.0      # 8% cơ hội rơi kiếm
@export var spear_drop_rate: float = 6.0      # 6% c<PERSON> hội rơi thương
@export var bow_drop_rate: float = 5.0        # 5% c<PERSON> hội rơi cung
@export var armor_drop_rate: float = 4.0      # 4% cơ hội rơi áo giáp
@export var pants_drop_rate: float = 3.0      # 3% cơ hội rơi quần giáp

# Tỉ lệ rơi vật phẩm dựa trên loại kẻ địch
var enemy_drop_rate_multiplier = {
	"sword": 1.0,   # Tỉ lệ c<PERSON> bản
	"spear": 1.2,   # Tăng 20%
	"bow": 1.5,     # T<PERSON><PERSON> 50%
	"boss": 2.5     # Tăng 150% (rất cao)
}

# Mapping loại kẻ địch với vũ khí ưu tiên
var enemy_preferred_weapons = {
	"sword": ["Sword", "Armor"],      # Kẻ địch kiếm thường rơi kiếm và áo giáp
	"spear": ["Spear", "Pant"],       # Kẻ địch thương thường rơi thương và quần giáp
	"bow": ["arrow", "Armor"],        # Kẻ địch cung thường rơi cung và áo giáp
	"boss": ["Sword", "Spear", "arrow", "Armor", "Pant"]  # Boss có thể rơi tất cả
}

# Hàm tạo vũ khí rơi ra tại vị trí của kẻ địch
func spawn_weapon_drop(enemy_position: Vector2, enemy_type: String) -> void:
	# Tính toán tỉ lệ rơi dựa trên loại kẻ địch
	var multiplier = enemy_drop_rate_multiplier.get(enemy_type, 1.0)
	
	# Lấy danh sách vũ khí ưu tiên cho loại kẻ địch này
	var preferred_weapons = enemy_preferred_weapons.get(enemy_type, ["Sword"])
	
	# Tính tỉ lệ rơi cho từng loại vũ khí
	var adjusted_rates = {
		"Sword": sword_drop_rate * multiplier,
		"Spear": spear_drop_rate * multiplier,
		"arrow": bow_drop_rate * multiplier,
		"Armor": armor_drop_rate * multiplier,
		"Pant": pants_drop_rate * multiplier
	}
	
	# Tăng tỉ lệ cho vũ khí ưu tiên
	for weapon in preferred_weapons:
		if weapon in adjusted_rates:
			adjusted_rates[weapon] *= 1.5  # Tăng 50% cho vũ khí ưu tiên
	
	# Tạo số ngẫu nhiên để quyết định có rơi vũ khí không
	var random_value = randf() * 100  # Số ngẫu nhiên từ 0 đến 100
	var cumulative_rate = 0.0
	
	# Kiểm tra từng loại vũ khí
	for weapon_type in adjusted_rates.keys():
		cumulative_rate += adjusted_rates[weapon_type]
		if random_value < cumulative_rate:
			_create_weapon_drop(enemy_position, weapon_type)
			break
	
	# Nếu là boss, có cơ hội rơi thêm vũ khí
	if enemy_type == "boss":
		# Boss có 50% cơ hội rơi thêm 1 vũ khí nữa
		if randf() < 0.5:
			var bonus_weapon = preferred_weapons[randi() % preferred_weapons.size()]
			_create_weapon_drop(enemy_position + Vector2(randf_range(-20, 20), 0), bonus_weapon)

# Hàm tạo vũ khí cụ thể
func _create_weapon_drop(position: Vector2, weapon_type: String) -> void:
	# Tạo instance của scene weapon_drop
	var weapon_instance = weapon_drop_scene.instantiate()
	
	# Thiết lập loại vũ khí
	weapon_instance.weapon_type = weapon_type
	
	# Thiết lập vị trí
	weapon_instance.global_position = position
	
	# Thêm vào scene
	var current_scene = get_tree().current_scene
	if current_scene:
		current_scene.add_child(weapon_instance)
	else:
		get_tree().root.add_child(weapon_instance)
	
	# Thêm lực ngẫu nhiên để vũ khí "bắn" ra
	weapon_instance.apply_impulse(Vector2(randf_range(-60, 60), randf_range(-120, -60)))
	
	print("Spawned " + weapon_type + " weapon at " + str(position))
