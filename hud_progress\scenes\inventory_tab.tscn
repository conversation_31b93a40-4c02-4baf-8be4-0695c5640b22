[gd_scene load_steps=25 format=3 uid="uid://3t2wi7rjhqpx"]

[ext_resource type="Texture2D" uid="uid://j4rou2busrne" path="res://hud_progress/images/BackPack_Inventory.png" id="1_djgeb"]
[ext_resource type="Script" path="res://hud_progress/scripts/inventory_tab.gd" id="1_oug2r"]
[ext_resource type="Texture2D" uid="uid://bu7yt8thgnu8q" path="res://hud_progress/images/close 1.png" id="2_g6yhq"]
[ext_resource type="Texture2D" uid="uid://ko5svgdg634e" path="res://hud_progress/images/Group 1493.png" id="3_ebja8"]
[ext_resource type="Texture2D" uid="uid://darqm32h0cpj7" path="res://hud_progress/images/Group 1501.png" id="4_ko6sy"]
[ext_resource type="Texture2D" uid="uid://dl7ucdnnpjdrg" path="res://hud_progress/images/Group 1494.png" id="5_u5agg"]
[ext_resource type="Texture2D" uid="uid://chhvfvg7fvw03" path="res://hud_progress/images/Group 1505.png" id="6_wmyh4"]
[ext_resource type="Texture2D" uid="uid://byep2fst4irrb" path="res://hud_progress/images/Group 1495.png" id="7_o8eb7"]
[ext_resource type="Texture2D" uid="uid://bed2f8xpe4wrq" path="res://hud_progress/images/Group 1504.png" id="8_ucsoq"]
[ext_resource type="Texture2D" uid="uid://cx0nn3qcow14s" path="res://hud_progress/images/Group 1496.png" id="9_ororu"]
[ext_resource type="Texture2D" uid="uid://cb4pip81phr0y" path="res://hud_progress/images/Group 1503.png" id="10_3r6fa"]
[ext_resource type="Texture2D" uid="uid://dv2wyeabq0sf6" path="res://hud_progress/images/Items_Label.png" id="11_aosgb"]
[ext_resource type="Texture2D" uid="uid://dq7lx6qxcr6ep" path="res://hud_progress/images/Slot_Panel.png" id="12_nshm6"]
[ext_resource type="Texture2D" uid="uid://b274xy3o5ew88" path="res://hud_progress/images/Inventory_Label.png" id="13_ngi7s"]
[ext_resource type="Texture2D" uid="uid://l1fae18c08k3" path="res://hud_progress/images/mp_border.png" id="15_x8d48"]
[ext_resource type="Texture2D" uid="uid://dlrs7wwi50o5e" path="res://hud_progress/images/mp_color.png" id="16_3furq"]
[ext_resource type="Texture2D" uid="uid://cmek6fyluh4ul" path="res://hud_progress/images/HP_border.png" id="17_l0e2v"]
[ext_resource type="Texture2D" uid="uid://dmbk6gi0o1nn1" path="res://hud_progress/images/hp_color.png" id="18_2ylfp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ojkpg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pyk2o"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_o2mhp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lrp18"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_f668p"]

[sub_resource type="Theme" id="Theme_d5x2o"]
VScrollBar/styles/grabber = SubResource("StyleBoxEmpty_ojkpg")
VScrollBar/styles/grabber_highlight = SubResource("StyleBoxEmpty_pyk2o")
VScrollBar/styles/grabber_pressed = SubResource("StyleBoxEmpty_o2mhp")
VScrollBar/styles/scroll = SubResource("StyleBoxEmpty_lrp18")
VScrollBar/styles/scroll_focus = SubResource("StyleBoxEmpty_f668p")

[node name="Inventory_Tab" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 1.0
offset_right = 1.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(1e-05, 1e-05)
script = ExtResource("1_oug2r")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Panel" type="Panel" parent="CanvasLayer"]
offset_right = 40.0
offset_bottom = 40.0

[node name="BackPackInventory" type="Sprite2D" parent="CanvasLayer/Panel"]
position = Vector2(240, 360)
texture = ExtResource("1_djgeb")

[node name="Close_Button" type="TextureButton" parent="CanvasLayer"]
offset_left = 456.0
offset_top = 17.0
offset_right = 501.0
offset_bottom = 62.0
texture_normal = ExtResource("2_g6yhq")

[node name="All_Tab_Button" type="HBoxContainer" parent="CanvasLayer"]
offset_left = 16.0
offset_top = 127.0
offset_right = 464.0
offset_bottom = 190.0

[node name="Mission_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("4_ko6sy")
texture_pressed = ExtResource("3_ebja8")
texture_disabled = ExtResource("4_ko6sy")

[node name="Inventory_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("6_wmyh4")
texture_disabled = ExtResource("5_u5agg")

[node name="Skills_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("7_o8eb7")
texture_pressed = ExtResource("8_ucsoq")
texture_disabled = ExtResource("7_o8eb7")

[node name="Etc_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("9_ororu")
texture_pressed = ExtResource("10_3r6fa")
texture_disabled = ExtResource("9_ororu")

[node name="ScrollContainer" type="ScrollContainer" parent="CanvasLayer"]
offset_left = 2.0
offset_top = 195.0
offset_right = 477.0
offset_bottom = 690.0
theme = SubResource("Theme_d5x2o")

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Items_Label" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("11_aosgb")

[node name="Slots_Label" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label2" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label3" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label4" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label5" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label6" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("13_ngi7s")

[node name="Slots_Label7" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label8" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label9" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label10" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="Slots_Label11" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_nshm6")

[node name="ManaProgressBar" type="TextureProgressBar" parent="CanvasLayer"]
offset_left = 171.0
offset_top = 47.0
offset_right = 373.0
offset_bottom = 70.0
value = 100.0
texture_under = ExtResource("15_x8d48")
texture_progress = ExtResource("16_3furq")
texture_progress_offset = Vector2(1, 1)

[node name="HealthProgressBar" type="TextureProgressBar" parent="CanvasLayer"]
offset_left = 170.0
offset_top = 10.0
offset_right = 727.0
offset_bottom = 55.0
scale = Vector2(0.5, 0.5)
value = 50.0
texture_under = ExtResource("17_l0e2v")
texture_progress = ExtResource("18_2ylfp")
texture_progress_offset = Vector2(3, 2)
