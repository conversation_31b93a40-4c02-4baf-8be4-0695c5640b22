extends CanvasLayer

signal dialog_closed
signal choice_made(choice_index: int)

@onready var npc_name_label = $DialogPanel/NPCName
@onready var dialog_text_label = $DialogPanel/DialogText
@onready var dialog_panel = $DialogPanel
@onready var choices_container = $DialogPanel/ChoicesContainer
@onready var next_button = $DialogPanel/NextButton

var current_npc = null
var current_dialog_index = 0
var dialog_sequence = []
var has_choices = false

func _ready():
	# Ẩn hộp thoại khi khởi động
	hide_dialog()

	# Kết nối nút đóng và nút tiếp theo
	$DialogPanel/CloseButton.pressed.connect(_on_close_button_pressed)
	next_button.pressed.connect(_on_next_button_pressed)

func show_dialog(npc, npc_name, dialog_data):
	# Lưu NPC hiện tại
	current_npc = npc

	# Lưu chuỗi hội thoại
	dialog_sequence = dialog_data
	current_dialog_index = 0

	# <PERSON><PERSON><PERSON> thị hộp thoại
	visible = true
	dialog_panel.visible = true

	# Hiển thị phần đầu tiên của hội thoại
	_show_current_dialog(npc_name)

	print("Hiển thị hộp thoại tương tác cho: " + npc_name)

func _show_current_dialog(speaker_name = ""):
	if current_dialog_index >= dialog_sequence.size():
		hide_dialog()
		return

	var dialog_entry = dialog_sequence[current_dialog_index]

	# Xác định người nói
	var display_name = dialog_entry.get("speaker", speaker_name)
	npc_name_label.text = display_name

	# Hiển thị nội dung hội thoại
	dialog_text_label.text = dialog_entry.get("text", "")

	# Kiểm tra xem có lựa chọn không
	var choices = dialog_entry.get("choices", [])
	has_choices = choices.size() > 0

	# Hiển thị hoặc ẩn nút tiếp theo dựa vào việc có lựa chọn hay không
	next_button.visible = !has_choices

	# Xóa các lựa chọn cũ
	if choices_container and is_instance_valid(choices_container):
		for child in choices_container.get_children():
			child.queue_free()
	else:
		print("WARNING: choices_container is null or invalid")

	# Hiển thị các lựa chọn nếu có
	if has_choices:
		choices_container.visible = true
		for i in range(choices.size()):
			var choice = choices[i]
			var button = Button.new()
			button.text = choice.get("text", "Lựa chọn " + str(i+1))
			button.custom_minimum_size = Vector2(700, 40)
			button.pressed.connect(_on_choice_button_pressed.bind(i))
			choices_container.add_child(button)
	else:
		choices_container.visible = false

func _on_choice_button_pressed(choice_index: int):
	if current_dialog_index < dialog_sequence.size():
		var dialog_entry = dialog_sequence[current_dialog_index]
		var choices = dialog_entry.get("choices", [])

		if choice_index < choices.size():
			var choice = choices[choice_index]

			# Phát tín hiệu lựa chọn
			choice_made.emit(choice_index)

			# Nếu có next_index, chuyển đến đoạn hội thoại tiếp theo
			if choice.has("next_index"):
				current_dialog_index = choice.get("next_index")
				_show_current_dialog()
			else:
				# Nếu không có next_index, tiếp tục bình thường
				current_dialog_index += 1
				_show_current_dialog()

func _on_next_button_pressed():
	current_dialog_index += 1
	_show_current_dialog()

func hide_dialog():
	# Ẩn hộp thoại
	visible = false
	dialog_panel.visible = false

	# Phát tín hiệu đã đóng hộp thoại
	dialog_closed.emit()
	current_npc = null

	print("Đã đóng hộp thoại tương tác")

func _on_close_button_pressed():
	print("Đã nhấn nút đóng")
	hide_dialog()

# Trả về chỉ số hội thoại hiện tại
func get_current_dialog_index() -> int:
	return current_dialog_index
