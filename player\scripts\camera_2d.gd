extends Camera2D

# The player is the parent node
@onready var player: Player = get_parent()

func _ready() -> void:
	# Enable the camera so it becomes the active one
	enabled = true
	
	# Find the map limits in the current scene
	# We search the whole tree starting from the root of the current scene
	var map_limit_node = _find_map_limit(get_tree().current_scene)
	
	if map_limit_node:
		print("MapLimit node found: ", map_limit_node.name, ". Applying limits.")
		self.limit_left = map_limit_node.left
		self.limit_right = map_limit_node.right
		self.limit_top = map_limit_node.top
		self.limit_bottom = map_limit_node.bottom
	else:
		print("WARNING: No MapLimit node found in the scene. Camera will be unbounded.")
		
	print("Camera2D ready. Final limits: L:", self.limit_left, ", R:", self.limit_right, ", T:", self.limit_top, ", B:", self.limit_bottom)

# Recursively search for a node with the MapLimit class name
func _find_map_limit(node: Node) -> MapLimit:
	# Check if the node is valid first
	if not node or not is_instance_valid(node):
		return null
	
	# Check if the current node is a MapLimit
	if node is MapLimit:
		return node
	
	# If not, check its children safely - thêm null check
	if node and is_instance_valid(node):
		for child in node.get_children():
			var found = _find_map_limit(child)
			if found:
				return found
			
	# Return null if not found in this branch
	return null

func _process(_delta: float) -> void:
	# Godot's Camera2D automatically follows its parent and respects the limits.
	# No manual positioning code is needed here.
	# This keeps the camera logic clean and relies on the engine's features.
	pass
