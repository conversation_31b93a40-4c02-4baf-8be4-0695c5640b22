# Test script to verify all map names are displayed correctly
extends Node

# Expected map names in Vietnamese
var expected_map_names = {
	"dong_dau": "<PERSON><PERSON><PERSON>",
	"lang_van_lang": "<PERSON><PERSON><PERSON>", 
	"rung_nuong": "<PERSON><PERSON><PERSON> Nươ<PERSON>",
	"hang_an": "<PERSON> Ẩn",
	"suoi_thieng": "Su<PERSON><PERSON> Thiêng",
	"doi_tre": "Đồi Tre"
}

# Map scene paths for testing
var map_scenes = {
	"dong_dau": "res://maps/dong_dau/scenes/dong_dau.tscn",
	"lang_van_lang": "res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
	"rung_nuong": "res://maps/rung_nuong/scenes/rung_nuong.tscn",
	"hang_an": "res://maps/hang_an/scenes/hang_an.tscn",
	"suoi_thieng": "res://maps/suoi_thieng/scenes/suoi_thieng.tscn",
	"doi_tre": "res://maps/doi_tre/scenes/doi_tre.tscn"
}

func _ready():
	print("🧪 Map Name Verification Test Started")
	print("=" * 50)
	
	# Test 1: Verify map controller names
	test_map_controller_names()
	
	# Test 2: Test GlobalMapNameUI functionality
	test_global_map_name_ui()
	
	# Test 3: Test UI persistence
	test_ui_persistence()
	
	print("=" * 50)
	print("🏁 Map Name Verification Test Completed")

func test_map_controller_names():
	"""Test that all map controllers have correct Vietnamese names"""
	print("\n📋 Test 1: Verifying Map Controller Names")
	print("-" * 30)
	
	var all_correct = true
	
	for map_id in expected_map_names.keys():
		var expected_name = expected_map_names[map_id]
		print("🗺️ Testing %s → Expected: '%s'" % [map_id, expected_name])
		
		# Load the scene to check the controller
		var scene_path = map_scenes[map_id]
		if not ResourceLoader.exists(scene_path):
			print("❌ Scene not found: %s" % scene_path)
			all_correct = false
			continue
		
		var scene_resource = load(scene_path)
		if not scene_resource:
			print("❌ Failed to load scene: %s" % scene_path)
			all_correct = false
			continue
		
		var scene_instance = scene_resource.instantiate()
		if not scene_instance:
			print("❌ Failed to instantiate scene: %s" % scene_path)
			all_correct = false
			continue
		
		# Find the map controller
		var controller = null
		for child in scene_instance.get_children():
			if child.name.ends_with("MapController"):
				controller = child
				break
		
		if not controller:
			print("❌ Map controller not found in %s" % map_id)
			all_correct = false
			scene_instance.queue_free()
			continue
		
		# Check the map name
		if controller.has_method("get") and controller.has("map_name"):
			var actual_name = controller.map_name
			if actual_name == expected_name:
				print("✅ %s: '%s' - CORRECT" % [map_id, actual_name])
			else:
				print("❌ %s: Expected '%s', got '%s' - INCORRECT" % [map_id, expected_name, actual_name])
				all_correct = false
		else:
			print("❌ %s: map_name property not found" % map_id)
			all_correct = false
		
		scene_instance.queue_free()
	
	if all_correct:
		print("✅ All map controller names are CORRECT!")
	else:
		print("❌ Some map controller names are INCORRECT!")

func test_global_map_name_ui():
	"""Test GlobalMapNameUI functionality"""
	print("\n📋 Test 2: Testing GlobalMapNameUI Functionality")
	print("-" * 30)
	
	if not GlobalMapNameUI:
		print("❌ GlobalMapNameUI not available!")
		return
	
	print("✅ GlobalMapNameUI is available")
	
	# Test setting each map name
	for map_id in expected_map_names.keys():
		var expected_name = expected_map_names[map_id]
		print("🔧 Setting map name: %s" % map_id)
		
		GlobalMapNameUI.set_map_name(expected_name)
		
		# Wait a moment for the UI to update
		await get_tree().process_frame
		
		# Check if UI is visible
		if GlobalMapNameUI.is_map_name_visible():
			var current_name = GlobalMapNameUI.get_current_map_name()
			if current_name == expected_name:
				print("✅ %s: UI shows '%s' - CORRECT" % [map_id, current_name])
			else:
				print("❌ %s: Expected '%s', UI shows '%s' - INCORRECT" % [map_id, expected_name, current_name])
		else:
			print("❌ %s: UI is not visible!" % map_id)
		
		# Wait before next test
		await get_tree().create_timer(0.5).timeout

func test_ui_persistence():
	"""Test that the UI stays visible permanently"""
	print("\n📋 Test 3: Testing UI Persistence")
	print("-" * 30)
	
	if not GlobalMapNameUI:
		print("❌ GlobalMapNameUI not available!")
		return
	
	# Set a test map name
	var test_map = "dong_dau"
	var test_name = expected_map_names[test_map]
	
	print("🔧 Setting test map: %s ('%s')" % [test_map, test_name])
	GlobalMapNameUI.set_map_name(test_name)
	
	# Check initial visibility
	await get_tree().process_frame
	if not GlobalMapNameUI.is_map_name_visible():
		print("❌ UI is not visible after setting map name!")
		return
	
	print("✅ UI is initially visible")
	
	# Wait for 10 seconds to see if it disappears
	print("⏱️ Waiting 10 seconds to test persistence...")
	var start_time = Time.get_time_dict_from_system()
	
	for i in range(10):
		await get_tree().create_timer(1.0).timeout
		if not GlobalMapNameUI.is_map_name_visible():
			print("❌ UI disappeared after %d seconds!" % (i + 1))
			return
		print("⏱️ %d seconds: UI still visible ✅" % (i + 1))
	
	print("✅ UI remained visible for 10 seconds - PERSISTENT!")

func _input(event):
	"""Handle input for manual testing"""
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				print("🔄 Manual test: Đồng Đậu")
				GlobalMapNameUI.set_map_name("Đồng Đậu")
			KEY_2:
				print("🔄 Manual test: Làng Văn Lang")
				GlobalMapNameUI.set_map_name("Làng Văn Lang")
			KEY_3:
				print("🔄 Manual test: Rừng Nương")
				GlobalMapNameUI.set_map_name("Rừng Nương")
			KEY_4:
				print("🔄 Manual test: Hang Ẩn")
				GlobalMapNameUI.set_map_name("Hang Ẩn")
			KEY_5:
				print("🔄 Manual test: Suối Thiêng")
				GlobalMapNameUI.set_map_name("Suối Thiêng")
			KEY_6:
				print("🔄 Manual test: Đồi Tre")
				GlobalMapNameUI.set_map_name("Đồi Tre")
			KEY_D:
				print("🔍 Debug UI info:")
				GlobalMapNameUI.debug_ui_info()
			KEY_R:
				print("🔄 Restarting tests...")
				_ready()
