[gd_scene load_steps=29 format=3 uid="uid://v03ryapgvoky"]

[ext_resource type="Script" uid="uid://b11n8opextoyg" path="res://hud_progress/scripts/skills_tab.gd" id="1_8oise"]
[ext_resource type="Texture2D" uid="uid://bu7yt8thgnu8q" path="res://hud_progress/images/close 1.png" id="2_w7nno"]
[ext_resource type="Texture2D" uid="uid://ko5svgdg634e" path="res://hud_progress/images/Group 1493.png" id="4_4x0uq"]
[ext_resource type="Texture2D" uid="uid://darqm32h0cpj7" path="res://hud_progress/images/Group 1501.png" id="5_ervss"]
[ext_resource type="Texture2D" uid="uid://dl7ucdnnpjdrg" path="res://hud_progress/images/Group 1494.png" id="6_o6yaw"]
[ext_resource type="Texture2D" uid="uid://chhvfvg7fvw03" path="res://hud_progress/images/Group 1505.png" id="7_17uob"]
[ext_resource type="Texture2D" uid="uid://byep2fst4irrb" path="res://hud_progress/images/Group 1495.png" id="8_5dsn2"]
[ext_resource type="Texture2D" uid="uid://bed2f8xpe4wrq" path="res://hud_progress/images/Group 1504.png" id="9_63dxr"]
[ext_resource type="Texture2D" uid="uid://cx0nn3qcow14s" path="res://hud_progress/images/Group 1496.png" id="10_1ydp3"]
[ext_resource type="Texture2D" uid="uid://c7555lsehyg1x" path="res://hud_progress/images/BackPack_Skills.png" id="11_2ugd4"]
[ext_resource type="Texture2D" uid="uid://c5hfgyfbp67o2" path="res://hud_progress/images/Health_Status.png" id="11_ayf8e"]
[ext_resource type="Texture2D" uid="uid://cb4pip81phr0y" path="res://hud_progress/images/Group 1503.png" id="11_tesjf"]
[ext_resource type="Texture2D" uid="uid://d3c40svxlxn3f" path="res://hud_progress/images/Mana_Status.png" id="12_6477d"]
[ext_resource type="Texture2D" uid="uid://cxd1irldok4m8" path="res://hud_progress/images/Attack_Status.png" id="13_hwrbh"]
[ext_resource type="Texture2D" uid="uid://cvoi3clnjgk68" path="res://hud_progress/images/Sword_Label.png" id="14_ubigu"]
[ext_resource type="Texture2D" uid="uid://dq7lx6qxcr6ep" path="res://hud_progress/images/Slot_Panel.png" id="15_lo6rw"]
[ext_resource type="Texture2D" uid="uid://cuccnjlrtl12s" path="res://hud_progress/images/Bow_Label.png" id="16_kbprc"]
[ext_resource type="Texture2D" uid="uid://x5dplf6wua05" path="res://hud_progress/images/Spear_Label.png" id="17_hgxbu"]
[ext_resource type="Texture2D" uid="uid://l1fae18c08k3" path="res://hud_progress/images/mp_border.png" id="19_0vkla"]
[ext_resource type="Texture2D" uid="uid://dlrs7wwi50o5e" path="res://hud_progress/images/mp_color.png" id="20_rdcdf"]
[ext_resource type="Texture2D" uid="uid://cmek6fyluh4ul" path="res://hud_progress/images/HP_border.png" id="21_ike25"]
[ext_resource type="Texture2D" uid="uid://dmbk6gi0o1nn1" path="res://hud_progress/images/hp_color.png" id="22_kvlqq"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mi7d8"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pmxky"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_tk13n"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_eeiv7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_052l3"]

[sub_resource type="Theme" id="Theme_tf50x"]
VScrollBar/styles/grabber = SubResource("StyleBoxEmpty_mi7d8")
VScrollBar/styles/grabber_highlight = SubResource("StyleBoxEmpty_pmxky")
VScrollBar/styles/grabber_pressed = SubResource("StyleBoxEmpty_tk13n")
VScrollBar/styles/scroll = SubResource("StyleBoxEmpty_eeiv7")
VScrollBar/styles/scroll_focus = SubResource("StyleBoxEmpty_052l3")

[node name="Skills_Tab" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_8oise")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Panel" type="Panel" parent="CanvasLayer"]
offset_right = 40.0
offset_bottom = 40.0

[node name="BackPackSkills" type="Sprite2D" parent="CanvasLayer/Panel"]
position = Vector2(240, 360)
texture = ExtResource("11_2ugd4")

[node name="Close_Button" type="TextureButton" parent="CanvasLayer"]
offset_left = 456.0
offset_top = 17.0
offset_right = 501.0
offset_bottom = 62.0
texture_normal = ExtResource("2_w7nno")

[node name="All_Tab_Button" type="HBoxContainer" parent="CanvasLayer"]
offset_left = 16.0
offset_top = 127.0
offset_right = 464.0
offset_bottom = 190.0

[node name="Mission_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("5_ervss")
texture_pressed = ExtResource("4_4x0uq")
texture_disabled = ExtResource("5_ervss")

[node name="Inventory_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("6_o6yaw")
texture_pressed = ExtResource("7_17uob")
texture_disabled = ExtResource("6_o6yaw")

[node name="Skills_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("9_63dxr")
texture_disabled = ExtResource("8_5dsn2")

[node name="Etc_Button" type="TextureButton" parent="CanvasLayer/All_Tab_Button"]
layout_mode = 2
texture_normal = ExtResource("10_1ydp3")
texture_pressed = ExtResource("11_tesjf")
texture_disabled = ExtResource("10_1ydp3")

[node name="ScrollContainer" type="ScrollContainer" parent="CanvasLayer"]
offset_left = 2.0
offset_top = 195.0
offset_right = 477.0
offset_bottom = 719.0
theme = SubResource("Theme_tf50x")

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Health_Status" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("11_ayf8e")

[node name="Mana_Status" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("12_6477d")

[node name="Attack_Status" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("13_hwrbh")

[node name="Swrod_Label" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("14_ubigu")

[node name="Slots_Label4" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label5" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label6" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label7" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label8" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Bow_Label" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("16_kbprc")

[node name="Slots_Label11" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label12" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label13" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label14" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Slots_Label15" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Spear_Label" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("17_hgxbu")

[node name="Spear_Label2" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Spear_Label3" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Spear_Label4" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="Spear_Label5" type="TextureRect" parent="CanvasLayer/ScrollContainer/VBoxContainer"]
layout_mode = 2
texture = ExtResource("15_lo6rw")

[node name="ManaProgressBar" type="TextureProgressBar" parent="CanvasLayer"]
offset_left = 171.0
offset_top = 47.0
offset_right = 373.0
offset_bottom = 70.0
value = 100.0
texture_under = ExtResource("19_0vkla")
texture_progress = ExtResource("20_rdcdf")
texture_progress_offset = Vector2(1, 1)

[node name="HealthProgressBar" type="TextureProgressBar" parent="CanvasLayer"]
offset_left = 170.0
offset_top = 10.0
offset_right = 727.0
offset_bottom = 55.0
scale = Vector2(0.5, 0.5)
value = 50.0
texture_under = ExtResource("21_ike25")
texture_progress = ExtResource("22_kvlqq")
texture_progress_offset = Vector2(3, 2)
