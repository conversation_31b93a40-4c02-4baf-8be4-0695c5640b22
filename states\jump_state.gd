# JumpState.gd
extends PlayerState
class_name JumpState

func get_state_name():
	return "jump"

func enter(owner):
	if owner.is_attacking:
		return
	print("Player entered Jump State")
	owner.velocity.y = owner.jump_velocity
	owner.play_animation("jump_start")

func exit(owner):
	print("Player exited Jump State")

func handle_input(owner, event):
	if owner.is_attacking:
		return
	# Bỏ gọi super.handle_input vì lớp cha không thực hiện gì
	# You can handle air control or other inputs here if desired.
	pass

func update(owner, delta):
	# Allow horizontal movement while in air
	var direction = Input.get_axis("move_left", "move_right")
	owner.velocity.x = direction * owner.speed

	# When the player lands, transition back to idle (or run if input exists)
	if owner.is_on_floor():
		owner.change_state(IdleState.new())
	else:
		if owner.velocity.y > 0:
			owner.play_animation("jump_end")
		else:
			owner.play_animation("jump_start")
