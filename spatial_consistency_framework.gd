# SpatialConsistencyFramework.gd - Comprehensive spatial teleportation system
extends Node
class_name SpatialConsistencyFramework

# 🗺️ SPATIAL WORLD MAP LAYOUT
# This defines the logical spatial relationship between all maps
#
#     [<PERSON><PERSON>]
#          |
#    [Hang <PERSON>] ←→ [Rung <PERSON>] ←→ [<PERSON>] ←→ [Doi Tre]
#          ↑           ↑              ↑
#          └─────── [<PERSON>] ──┘
#                   (Central Hub)

# 🎯 DIRECTIONAL NAMING CONVENTION
enum Direction {
	LEFT,
	RIGHT, 
	NORTH,
	SOUTH,
	CENTER  # For hub connections
}

# 📍 SPATIAL MAP CONFIGURATION
var spatial_map_config = {
	"lang_van_lang": {
		"type": "hub",
		"connections": {
			Direction.LEFT: "dong_dau",
			Direction.RIGHT: "hang_an", 
			Direction.NORTH: "rung_nuong",
			Direction.SOUTH: "suoi_thieng"
		},
		"bounds": {"left": 0, "right": 4000, "top": -2200, "bottom": -1800}
	},
	
	"dong_dau": {
		"type": "linear",
		"connections": {
			Direction.LEFT: "doi_tre",
			Direction.RIGHT: "lang_van_lang",
			Direction.NORTH: "rung_nuong"
		},
		"bounds": {"left": -2535, "right": 5400, "top": -1600, "bottom": -125}
	},
	
	"doi_tre": {
		"type": "terminal",
		"connections": {
			Direction.RIGHT: "dong_dau"
		},
		"bounds": {"left": -3000, "right": 1500, "top": -800, "bottom": -120}
	},
	
	"rung_nuong": {
		"type": "linear", 
		"connections": {
			Direction.LEFT: "dong_dau",
			Direction.RIGHT: "hang_an",
			Direction.SOUTH: "lang_van_lang"
		},
		"bounds": {"left": -1000, "right": 3000, "top": -1500, "bottom": -1000}
	},
	
	"hang_an": {
		"type": "linear",
		"connections": {
			Direction.LEFT: "rung_nuong",
			Direction.SOUTH: "lang_van_lang"
		},
		"bounds": {"left": -2500, "right": 1000, "top": -2000, "bottom": -1500}
	},
	
	"suoi_thieng": {
		"type": "terminal",
		"connections": {
			Direction.NORTH: "lang_van_lang"
		},
		"bounds": {"left": -2500, "right": 500, "top": 0, "bottom": 500}
	}
}

# 🚪 GATE NAMING SYSTEM
func get_gate_name(from_map: String, to_map: String) -> String:
	"""Generate standardized gate name based on spatial relationship"""
	var direction = get_direction_between_maps(from_map, to_map)
	var direction_name = get_direction_name(direction)
	
	if spatial_map_config[from_map].type == "hub":
		return "%s_exit" % direction_name
	else:
		return "%s_entrance" % direction_name

func get_direction_between_maps(from_map: String, to_map: String) -> Direction:
	"""Get the direction from one map to another"""
	var from_config = spatial_map_config.get(from_map, {})
	var connections = from_config.get("connections", {})
	
	for direction in connections:
		if connections[direction] == to_map:
			return direction
	
	return Direction.CENTER  # Fallback

func get_direction_name(direction: Direction) -> String:
	"""Convert direction enum to string"""
	match direction:
		Direction.LEFT: return "left"
		Direction.RIGHT: return "right"
		Direction.NORTH: return "north"
		Direction.SOUTH: return "south"
		Direction.CENTER: return "center"
		_: return "unknown"

# 📍 SPATIAL POSITION CALCULATION
func calculate_spawn_position(from_map: String, to_map: String) -> Vector2:
	"""Calculate where player should spawn based on spatial logic"""
	var from_direction = get_direction_between_maps(from_map, to_map)
	var to_direction = get_opposite_direction(from_direction)
	
	var to_bounds = spatial_map_config[to_map].bounds
	var spawn_pos = Vector2.ZERO
	
	# Calculate spawn position based on direction
	match to_direction:
		Direction.LEFT:
			spawn_pos.x = to_bounds.left + 100  # Near left edge
			spawn_pos.y = (to_bounds.top + to_bounds.bottom) / 2
		Direction.RIGHT:
			spawn_pos.x = to_bounds.right - 100  # Near right edge
			spawn_pos.y = (to_bounds.top + to_bounds.bottom) / 2
		Direction.NORTH:
			spawn_pos.x = (to_bounds.left + to_bounds.right) / 2
			spawn_pos.y = to_bounds.top + 100  # Near top edge
		Direction.SOUTH:
			spawn_pos.x = (to_bounds.left + to_bounds.right) / 2
			spawn_pos.y = to_bounds.bottom - 100  # Near bottom edge
		Direction.CENTER:
			spawn_pos.x = (to_bounds.left + to_bounds.right) / 2
			spawn_pos.y = (to_bounds.top + to_bounds.bottom) / 2
	
	return spawn_pos

func get_opposite_direction(direction: Direction) -> Direction:
	"""Get the opposite direction for spawn positioning"""
	match direction:
		Direction.LEFT: return Direction.RIGHT
		Direction.RIGHT: return Direction.LEFT
		Direction.NORTH: return Direction.SOUTH
		Direction.SOUTH: return Direction.NORTH
		Direction.CENTER: return Direction.CENTER
		_: return Direction.CENTER

# 🎯 GATE POSITION CALCULATION
func calculate_gate_position(map_name: String, direction: Direction) -> Vector2:
	"""Calculate where gate should be positioned within a map"""
	var bounds = spatial_map_config[map_name].bounds
	var gate_pos = Vector2.ZERO
	
	match direction:
		Direction.LEFT:
			gate_pos.x = bounds.left + 50
			gate_pos.y = (bounds.top + bounds.bottom) / 2
		Direction.RIGHT:
			gate_pos.x = bounds.right - 50
			gate_pos.y = (bounds.top + bounds.bottom) / 2
		Direction.NORTH:
			gate_pos.x = (bounds.left + bounds.right) / 2
			gate_pos.y = bounds.top + 50
		Direction.SOUTH:
			gate_pos.x = (bounds.left + bounds.right) / 2
			gate_pos.y = bounds.bottom - 50
		Direction.CENTER:
			gate_pos.x = (bounds.left + bounds.right) / 2
			gate_pos.y = (bounds.top + bounds.bottom) / 2
	
	return gate_pos

# 🔄 VALIDATION FUNCTIONS
func validate_spatial_consistency() -> bool:
	"""Validate that all spatial connections are reciprocal"""
	for from_map in spatial_map_config:
		var connections = spatial_map_config[from_map].connections
		for direction in connections:
			var to_map = connections[direction]
			if not has_reciprocal_connection(from_map, to_map):
				print("❌ Missing reciprocal connection: %s ←→ %s" % [from_map, to_map])
				return false
	return true

func has_reciprocal_connection(map_a: String, map_b: String) -> bool:
	"""Check if two maps have reciprocal connections"""
	var a_connections = spatial_map_config[map_a].connections
	var b_connections = spatial_map_config[map_b].connections
	
	var a_connects_to_b = false
	var b_connects_to_a = false
	
	for direction in a_connections:
		if a_connections[direction] == map_b:
			a_connects_to_b = true
			break
	
	for direction in b_connections:
		if b_connections[direction] == map_a:
			b_connects_to_a = true
			break
	
	return a_connects_to_b and b_connects_to_a

# 📊 DEBUG AND UTILITY FUNCTIONS
func print_spatial_layout():
	"""Print the complete spatial layout for debugging"""
	print("🗺️ SPATIAL WORLD LAYOUT:")
	for map_name in spatial_map_config:
		var config = spatial_map_config[map_name]
		print("  %s (%s):" % [map_name, config.type])
		for direction in config.connections:
			var target = config.connections[direction]
			print("    %s → %s" % [get_direction_name(direction), target])

func get_all_gate_configurations() -> Dictionary:
	"""Generate complete gate configuration for all maps"""
	var all_gates = {}
	
	for from_map in spatial_map_config:
		var connections = spatial_map_config[from_map].connections
		for direction in connections:
			var to_map = connections[direction]
			var gate_id = "%s_to_%s" % [from_map, to_map]
			var gate_name = get_gate_name(from_map, to_map)
			var gate_pos = calculate_gate_position(from_map, direction)
			var spawn_pos = calculate_spawn_position(from_map, to_map)
			
			all_gates[gate_id] = {
				"gate_name": gate_name,
				"from_map": from_map,
				"to_map": to_map,
				"direction": direction,
				"gate_position": gate_pos,
				"spawn_position": spawn_pos,
				"target_scene": "res://maps/%s/scenes/%s.tscn" % [to_map, to_map]
			}
	
	return all_gates
