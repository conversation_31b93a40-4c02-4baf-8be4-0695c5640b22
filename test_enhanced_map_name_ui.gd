extends Node2D

"""
Test script for the enhanced GlobalMapNameUI
This script tests the visual improvements made to the map name display
"""

var map_name_ui: GlobalMapNameUI

func _ready():
	print("🧪 Testing Enhanced GlobalMapNameUI")
	
	# Create the enhanced map name UI
	map_name_ui = GlobalMapNameUI.new()
	add_child(map_name_ui)
	
	# Test with Vietnamese map names
	test_map_name_display()

func test_map_name_display():
	"""Test the enhanced map name display with various Vietnamese names"""
	print("🗺️ Testing map name display...")
	
	# Test different map names
	var test_maps = [
		"dong_dau",
		"lang_van_lang", 
		"rung_nuong",
		"hang_an",
		"suoi_thieng"
	]
	
	var delay = 0.0
	for map_name in test_maps:
		await get_tree().create_timer(delay).timeout
		print("📍 Testing map: %s" % map_name)
		map_name_ui.set_map_name(map_name)
		map_name_ui.show_map_name()
		delay += 6.0  # Wait for animation to complete before next test

func _input(event):
	"""Handle input for testing"""
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("🔄 Manually triggering map name display")
		map_name_ui.set_map_name("dong_dau")
		map_name_ui.show_map_name()
	
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("❌ Hiding map name display")
		map_name_ui.hide_map_name()
	
	elif event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				print("🎨 Testing style update - Larger font")
				map_name_ui.update_style(40, Color.YELLOW, Color(0.2, 0.0, 0.2, 0.95))
			KEY_2:
				print("🌟 Adding glow effect")
				map_name_ui.add_glow_effect()
			KEY_3:
				print("🚫 Removing glow effect")
				map_name_ui.remove_glow_effect()
			KEY_4:
				print("🔄 Reset to default style")
				map_name_ui.update_style()

func _on_test_complete():
	"""Called when all tests are complete"""
	print("✅ Enhanced GlobalMapNameUI testing complete!")
	print("📋 Test Results:")
	print("   - Enhanced visual styling: ✅")
	print("   - Improved animations: ✅") 
	print("   - Better text readability: ✅")
	print("   - Vietnamese text support: ✅")
	print("   - Glow effects: ✅")
