# UI Improvements Test Script - <PERSON><PERSON><PERSON> tra tất cả cải tiến UI
extends Node

# Test results
var test_results: Dictionary = {}
var total_tests: int = 0
var passed_tests: int = 0
var failed_tests: int = 0

# Component references
var map_name_ui: Node = null
var theme_manager: Node = null
var navigation_manager: Node = null
var accessibility_settings: Node = null

func _ready():
	print("🧪 UI Improvements Test Suite loaded (manual start required)")
	# CRITICAL FIX: Don't auto-run tests during normal game startup
	# Tests must be started manually to prevent startup conflicts

func run_all_tests():
	"""Run all UI improvement tests"""
	print("\n" + "=".repeat(50))
	print("🧪 UI IMPROVEMENTS TEST SUITE")
	print("=".repeat(50))

	# Test 1: Map Name Notification System
	await test_map_name_notification_system()

	# Test 2: Enhanced Theme System
	await test_enhanced_theme_system()

	# Test 3: Menu Navigation and Interactions
	await test_menu_navigation_system()

	# Test 4: Accessibility Features
	await test_accessibility_features()

	# Test 5: Loading Screen Enhancements
	await test_loading_screen_enhancements()

	# Test 6: Visual Feedback and Animations
	await test_visual_feedback_system()

	# Test 7: UI System Integration
	await test_ui_system_integration()

	# Test 8: Cross-System Compatibility
	await test_cross_system_compatibility()

	# Print final results
	print_test_summary()

func test_map_name_notification_system() -> void:
	"""Test enhanced map name notification system"""
	print("\n📍 Testing Map Name Notification System...")
	
	# Test 1.1: UI Creation
	var test_name = "Map Name UI Creation"
	total_tests += 1
	
	if GlobalMapNameUI:
		GlobalMapNameUI._create_enhanced_map_ui()
		if GlobalMapNameUI.background_panel and GlobalMapNameUI.map_name_label:
			record_test_result(test_name, true, "Enhanced UI components created successfully")
		else:
			record_test_result(test_name, false, "Failed to create UI components")
	else:
		record_test_result(test_name, false, "GlobalMapNameUI not available")
	
	# Test 1.2: Map Name Display
	test_name = "Map Name Display"
	total_tests += 1
	
	if GlobalMapNameUI:
		GlobalMapNameUI.set_map_name("Test Map")
		await get_tree().create_timer(0.5).timeout
		
		if GlobalMapNameUI.get_current_map_name() == "Test Map":
			record_test_result(test_name, true, "Map name set and retrieved correctly")
		else:
			record_test_result(test_name, false, "Map name not set correctly")
	else:
		record_test_result(test_name, false, "GlobalMapNameUI not available")
	
	# Test 1.3: Show/Hide Animation
	test_name = "Show/Hide Animation"
	total_tests += 1
	
	if GlobalMapNameUI:
		GlobalMapNameUI.show_map_name()
		await get_tree().create_timer(0.5).timeout
		
		if GlobalMapNameUI.is_map_name_visible():
			GlobalMapNameUI.hide_map_name()
			await get_tree().create_timer(0.5).timeout
			
			if not GlobalMapNameUI.is_map_name_visible():
				record_test_result(test_name, true, "Show/hide animations work correctly")
			else:
				record_test_result(test_name, false, "Hide animation failed")
		else:
			record_test_result(test_name, false, "Show animation failed")
	else:
		record_test_result(test_name, false, "GlobalMapNameUI not available")
	
	# Test 1.4: Style Customization
	test_name = "Style Customization"
	total_tests += 1
	
	if GlobalMapNameUI:
		GlobalMapNameUI.update_style(40, Color.RED, Color.BLUE)
		GlobalMapNameUI.add_glow_effect()
		await get_tree().create_timer(0.2).timeout
		GlobalMapNameUI.remove_glow_effect()
		record_test_result(test_name, true, "Style customization functions executed")
	else:
		record_test_result(test_name, false, "GlobalMapNameUI not available")

func test_enhanced_theme_system() -> void:
	"""Test enhanced theme system"""
	print("\n🎨 Testing Enhanced Theme System...")
	
	# Test 2.1: Theme Manager Creation
	var test_name = "Theme Manager Creation"
	total_tests += 1
	
	var theme_script = preload("res://ui/scripts/enhanced_theme_manager.gd")
	theme_manager = theme_script.new()
	add_child(theme_manager)
	
	if theme_manager:
		record_test_result(test_name, true, "Theme manager created successfully")
	else:
		record_test_result(test_name, false, "Failed to create theme manager")
	
	# Test 2.2: Button Style Creation
	test_name = "Button Style Creation"
	total_tests += 1
	
	if theme_manager:
		var styles = theme_manager.create_enhanced_button_style()
		if styles.has("normal") and styles.has("hover") and styles.has("pressed"):
			record_test_result(test_name, true, "Button styles created successfully")
		else:
			record_test_result(test_name, false, "Button styles incomplete")
	else:
		record_test_result(test_name, false, "Theme manager not available")
	
	# Test 2.3: Color Scheme Changes
	test_name = "Color Scheme Changes"
	total_tests += 1
	
	if theme_manager:
		theme_manager.set_color_scheme("blue")
		theme_manager.set_color_scheme("green")
		theme_manager.set_color_scheme("golden")
		record_test_result(test_name, true, "Color schemes changed successfully")
	else:
		record_test_result(test_name, false, "Theme manager not available")

func test_menu_navigation_system() -> void:
	"""Test menu navigation and interactions"""
	print("\n🎮 Testing Menu Navigation System...")
	
	# Test 3.1: Navigation Manager Creation
	var test_name = "Navigation Manager Creation"
	total_tests += 1
	
	var nav_script = preload("res://ui/scripts/enhanced_navigation_manager.gd")
	navigation_manager = nav_script.new()
	add_child(navigation_manager)
	
	if navigation_manager:
		record_test_result(test_name, true, "Navigation manager created successfully")
	else:
		record_test_result(test_name, false, "Failed to create navigation manager")
	
	# Test 3.2: Focus System
	test_name = "Focus System"
	total_tests += 1
	
	if navigation_manager:
		# Create test buttons
		var test_buttons: Array[Control] = []
		for i in range(3):
			var button = Button.new()
			button.text = "Test Button %d" % (i + 1)
			button.position = Vector2(100, 100 + i * 50)
			button.size = Vector2(200, 40)
			add_child(button)
			test_buttons.append(button)
		
		navigation_manager.register_focusable_controls(test_buttons)
		await get_tree().create_timer(0.2).timeout
		
		var focused_control = navigation_manager.get_current_focused_control()
		if focused_control:
			record_test_result(test_name, true, "Focus system working correctly")
		else:
			record_test_result(test_name, false, "Focus system not working")
		
		# Cleanup test buttons
		for button in test_buttons:
			button.queue_free()
	else:
		record_test_result(test_name, false, "Navigation manager not available")

func test_accessibility_features() -> void:
	"""Test accessibility features"""
	print("\n♿ Testing Accessibility Features...")
	
	# Test 4.1: Accessibility Settings Creation
	var test_name = "Accessibility Settings Creation"
	total_tests += 1
	
	var accessibility_script = preload("res://ui/scripts/accessibility_settings.gd")
	accessibility_settings = accessibility_script.new()
	add_child(accessibility_settings)
	
	if accessibility_settings:
		record_test_result(test_name, true, "Accessibility settings created successfully")
	else:
		record_test_result(test_name, false, "Failed to create accessibility settings")
	
	# Test 4.2: Settings Management
	test_name = "Settings Management"
	total_tests += 1
	
	if accessibility_settings:
		accessibility_settings.set_setting("high_contrast", true)
		accessibility_settings.set_setting("large_text", true)
		
		var high_contrast = accessibility_settings.get_setting("high_contrast")
		var large_text = accessibility_settings.get_setting("large_text")
		
		if high_contrast == true and large_text == true:
			record_test_result(test_name, true, "Settings management working correctly")
		else:
			record_test_result(test_name, false, "Settings not saved/retrieved correctly")
	else:
		record_test_result(test_name, false, "Accessibility settings not available")

func test_loading_screen_enhancements() -> void:
	"""Test loading screen enhancements"""
	print("\n📺 Testing Loading Screen Enhancements...")
	
	# Test 5.1: Enhanced Loading Screen Creation
	var test_name = "Enhanced Loading Screen"
	total_tests += 1
	
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if loading_scene:
		var loading_instance = loading_scene.instantiate()
		add_child(loading_instance)
		
		await get_tree().create_timer(0.5).timeout
		
		if loading_instance.has_method("update_progress_enhanced"):
			loading_instance.update_progress_enhanced(50.0, "Testing...")
			await get_tree().create_timer(0.5).timeout
			loading_instance.show_completion_effect()
			record_test_result(test_name, true, "Enhanced loading screen functions work")
		else:
			record_test_result(test_name, false, "Enhanced functions not available")
		
		loading_instance.queue_free()
	else:
		record_test_result(test_name, false, "Loading screen scene not found")

func test_visual_feedback_system() -> void:
	"""Test visual feedback and animations"""
	print("\n✨ Testing Visual Feedback System...")
	
	# Test 6.1: Animation System
	var test_name = "Animation System"
	total_tests += 1
	
	# Create test control for animations
	var test_control = Control.new()
	test_control.size = Vector2(100, 100)
	test_control.position = Vector2(200, 200)
	add_child(test_control)
	
	# Test scale animation
	var tween = create_tween()
	tween.tween_property(test_control, "scale", Vector2(1.2, 1.2), 0.2)
	tween.tween_property(test_control, "scale", Vector2(1.0, 1.0), 0.2)
	
	await tween.finished
	
	if test_control.scale == Vector2(1.0, 1.0):
		record_test_result(test_name, true, "Animation system working correctly")
	else:
		record_test_result(test_name, false, "Animation system not working")
	
	test_control.queue_free()

func test_ui_system_integration() -> void:
	"""Test UI system integration"""
	print("\n🔗 Testing UI System Integration...")

	# Test 7.1: Map Name UI + Theme Manager Integration
	var test_name = "Map Name UI + Theme Manager Integration"
	total_tests += 1

	if GlobalMapNameUI and theme_manager:
		# Test if theme manager can style map name UI
		GlobalMapNameUI.set_map_name("Integration Test")
		await get_tree().create_timer(0.2).timeout

		# Try to apply theme changes
		theme_manager.set_color_scheme("blue")
		await get_tree().create_timer(0.2).timeout

		record_test_result(test_name, true, "Map name UI and theme manager work together")
	else:
		record_test_result(test_name, false, "Required components not available")

	# Test 7.2: Navigation Manager + Accessibility Integration
	test_name = "Navigation Manager + Accessibility Integration"
	total_tests += 1

	if navigation_manager and accessibility_settings:
		# Test navigation with accessibility features
		navigation_manager.set_reduced_motion(true)
		accessibility_settings.set_setting("keyboard_navigation", true)

		record_test_result(test_name, true, "Navigation and accessibility systems integrated")
	else:
		record_test_result(test_name, false, "Required components not available")

func test_cross_system_compatibility() -> void:
	"""Test cross-system compatibility"""
	print("\n🌐 Testing Cross-System Compatibility...")

	# Test 8.1: UI + Game Systems Compatibility
	var test_name = "UI + Game Systems Compatibility"
	total_tests += 1

	# Test if UI systems don't interfere with game systems
	var scene_manager_available = SceneManager != null
	var global_ui_available = GlobalMapNameUI != null

	if scene_manager_available and global_ui_available:
		# Test scene transition with UI active
		GlobalMapNameUI.set_map_name("Compatibility Test")
		GlobalMapNameUI.show_map_name()
		await get_tree().create_timer(0.5).timeout
		GlobalMapNameUI.hide_map_name()

		record_test_result(test_name, true, "UI systems compatible with game systems")
	else:
		record_test_result(test_name, false, "Required systems not available")

	# Test 8.2: Memory and Performance Impact
	test_name = "Memory and Performance Impact"
	total_tests += 1

	# Simple performance test
	var start_time = Time.get_ticks_msec()

	# Create and destroy UI components multiple times
	for i in range(10):
		if theme_manager:
			var test_button = Button.new()
			theme_manager.apply_button_theme(test_button)
			add_child(test_button)
			await get_tree().process_frame
			test_button.queue_free()

	var end_time = Time.get_ticks_msec()
	var duration = end_time - start_time

	if duration < 1000:  # Less than 1 second for 10 operations
		record_test_result(test_name, true, "Performance impact acceptable (%d ms)" % duration)
	else:
		record_test_result(test_name, false, "Performance impact too high (%d ms)" % duration)

func record_test_result(test_name: String, passed: bool, message: String):
	"""Record a test result"""
	test_results[test_name] = {
		"passed": passed,
		"message": message
	}
	
	if passed:
		passed_tests += 1
		print("  ✅ %s: %s" % [test_name, message])
	else:
		failed_tests += 1
		print("  ❌ %s: %s" % [test_name, message])

func print_test_summary():
	"""Print final test summary"""
	var separator = "=================================================="
	print("\n" + separator)
	print("🧪 TEST SUMMARY")
	print(separator)
	print("Total Tests: %d" % total_tests)
	var pass_percentage = (passed_tests * 100.0 / total_tests) if total_tests > 0 else 0.0
	var fail_percentage = (failed_tests * 100.0 / total_tests) if total_tests > 0 else 0.0
	print("Passed: %d (%.1f%%)" % [passed_tests, pass_percentage])
	print("Failed: %d (%.1f%%)" % [failed_tests, fail_percentage])
	print(separator)
	
	if failed_tests == 0:
		print("🎉 ALL TESTS PASSED! UI improvements are working correctly.")
	else:
		print("⚠️ Some tests failed. Please review the failed tests above.")
	
	print("\n📋 DETAILED RESULTS:")
	for test_name in test_results.keys():
		var result = test_results[test_name]
		var status = "✅ PASS" if result["passed"] else "❌ FAIL"
		print("  %s - %s: %s" % [status, test_name, result["message"]])

# Manual test functions for interactive testing
func test_map_name_display_manual():
	"""Manual test for map name display"""
	print("🧪 Manual Test: Map Name Display")
	if GlobalMapNameUI:
		GlobalMapNameUI.set_map_name("Manual Test Map")
		GlobalMapNameUI.show_map_name()
		print("Map name should be visible now. Check the top-right corner.")

func test_theme_application_manual():
	"""Manual test for theme application"""
	print("🧪 Manual Test: Theme Application")
	if theme_manager:
		var test_button = Button.new()
		test_button.text = "Test Theme Button"
		test_button.position = Vector2(400, 300)
		test_button.size = Vector2(200, 50)
		add_child(test_button)
		
		theme_manager.apply_button_theme(test_button)
		theme_manager.create_hover_animation(test_button)
		theme_manager.create_press_animation(test_button)
		
		print("Test button created with enhanced theme. Hover and click to test.")

# Input handling for manual tests
func _input(event):
	"""Handle input for manual testing"""
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				test_map_name_display_manual()
			KEY_2:
				test_theme_application_manual()
			KEY_R:
				print("🔄 Restarting all tests...")
				run_all_tests()
			KEY_H:
				print_help()

func print_help():
	"""Print help for manual testing"""
	var help_separator = "========================================"
	print("\n" + help_separator)
	print("🆘 MANUAL TESTING HELP")
	print(help_separator)
	print("Press 1: Test Map Name Display")
	print("Press 2: Test Theme Application")
	print("Press R: Restart All Tests")
	print("Press H: Show This Help")
	print(help_separator)
