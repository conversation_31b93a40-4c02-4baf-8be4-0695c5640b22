extends TextureButton

# ----- Signals -----
signal hovered(title: String, description: String, image: Texture2D)
signal clicked(title: String, description: String, image: Texture2D)

# ----- Constants -----
const FLOOR_IMAGE = preload("res://assets/images/background/screens/Lau1.png")
const DEFAULT_COLOR = Color(1, 1, 1, 1)  # White
const HOVER_COLOR = Color(1, 0.8, 0.8, 1)  # Light red
const FLOOR_TITLE = "Thông tin tầng 2"
const FLOOR_DESCRIPTION = "Người chơi bắt đầu hành trình tại tầng 2 của tòa tháp, nơi có nhiều thử thách và bí ẩn đang chờ đợi. Khám phá các căn phòng và giải mã những bí ẩn để tiến lên tầng cao hơn."

# ----- Lifecycle Methods -----
func _ready() -> void:
    if not is_instance_valid(self):
        return

    # <PERSON>ết nố<PERSON> signals
    mouse_entered.connect(_on_mouse_entered)
    mouse_exited.connect(_on_mouse_exited)
    pressed.connect(_on_button_pressed)

    # Đặt màu mặc định
    modulate = DEFAULT_COLOR

# ----- Event Handlers -----
func _on_mouse_entered() -> void:
    if not is_instance_valid(self):
        return

    # Hiệu ứng hover
    modulate = HOVER_COLOR
    scale = Vector2(1.1, 1.1)  # Scale up slightly

    hovered.emit(FLOOR_TITLE, FLOOR_DESCRIPTION, FLOOR_IMAGE)

func _on_mouse_exited() -> void:
    if not is_instance_valid(self):
        return

    # Reset hiệu ứng
    modulate = DEFAULT_COLOR
    scale = Vector2(1, 1)  # Reset scale

    # Emit signal ẩn thông tin (chỉ khi chưa có tầng nào được nhấn)
    # Việc kiểm tra is_floor_clicked sẽ được xử lý trong main_foot.gd
    hovered.emit("", "", null)

func _on_button_pressed() -> void:
    if not is_instance_valid(self):
        return

    # Hiệu ứng khi nhấn
    scale = Vector2(1.2, 1.2)  # Scale up more when clicked

    # Emit signal khi nhấn để hiển thị thông tin
    if FLOOR_IMAGE:
        clicked.emit(FLOOR_TITLE, FLOOR_DESCRIPTION, FLOOR_IMAGE)
    else:
        push_error("Floor image resource failed to load")



