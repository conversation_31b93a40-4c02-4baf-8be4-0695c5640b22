# ResourceLoadingFix.gd - Fix all resource loading issues in the project
extends Node

func _ready():
	print("🔧 === RESOURCE LOADING FIX SYSTEM ===")
	call_deferred("fix_all_resource_issues")

func fix_all_resource_issues():
	print("\n🔍 Fixing all resource loading issues...")
	
	# Fix 1: Validate and fix scene paths
	validate_scene_paths()
	
	# Fix 2: Fix texture and asset loading
	fix_texture_loading()
	
	# Fix 3: Validate teleport gate targets
	validate_teleport_targets()
	
	# Fix 4: Check UI scene references
	validate_ui_scenes()
	
	# Fix 5: Generate missing resource fallbacks
	create_fallback_resources()
	
	print("\n✅ All resource loading fixes applied!")

func validate_scene_paths():
	print("\n📁 VALIDATING SCENE PATHS...")
	
	var critical_scenes = [
		"res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn",
		"res://maps/doi_tre/scenes/doi_tre.tscn",
		"res://maps/hang_an/scenes/hang_an.tscn",
		"res://maps/rung_nuong/scenes/rung_nuong.tscn",
		"res://maps/suoi_thieng/scenes/suoi_thieng.tscn",
		"res://hud_progress/scenes/inventory_tab.tscn",
		"res://ui/scenes/inventory_button_manager.tscn"
	]
	
	var missing_scenes = []
	var valid_scenes = []
	
	for scene_path in critical_scenes:
		if FileAccess.file_exists(scene_path):
			valid_scenes.append(scene_path)
			print("✅ %s" % scene_path.get_file())
		else:
			missing_scenes.append(scene_path)
			print("❌ %s - MISSING" % scene_path.get_file())
	
	print("\n📊 Scene validation results:")
	print("   Valid scenes: %d/%d" % [valid_scenes.size(), critical_scenes.size()])
	print("   Missing scenes: %d" % missing_scenes.size())
	
	if missing_scenes.size() > 0:
		print("⚠️ Missing scenes need to be created or paths updated:")
		for scene in missing_scenes:
			print("   - %s" % scene)

func fix_texture_loading():
	print("\n🖼️ FIXING TEXTURE LOADING...")
	
	# Common texture paths that might be missing
	var texture_paths = [
		"res://assets/images/ui/inventory_button.png",
		"res://assets/images/item/bag.png", 
		"res://Legacy-Fantasy - High Forest 2.3/HUD/Base-01.png",
		"res://assets/images/ui/teleport_gate_icon.png"
	]
	
	var missing_textures = []
	var valid_textures = []
	
	for texture_path in texture_paths:
		if FileAccess.file_exists(texture_path):
			valid_textures.append(texture_path)
			print("✅ %s" % texture_path.get_file())
		else:
			missing_textures.append(texture_path)
			print("❌ %s - MISSING" % texture_path.get_file())
	
	print("\n📊 Texture validation results:")
	print("   Valid textures: %d/%d" % [valid_textures.size(), texture_paths.size()])
	print("   Missing textures: %d" % missing_textures.size())

func validate_teleport_targets():
	print("\n🚪 VALIDATING TELEPORT GATE TARGETS...")
	
	# Get all teleport gates in the project
	var teleport_gates = get_tree().get_nodes_in_group("teleport_gates")
	print("Found %d teleport gates to validate" % teleport_gates.size())
	
	var invalid_targets = []
	var valid_targets = []
	
	for gate in teleport_gates:
		if gate.has_method("get") and gate.get("target_scene"):
			var target_scene = gate.target_scene
			if target_scene and not target_scene.is_empty():
				if FileAccess.file_exists(target_scene):
					valid_targets.append({"gate": gate.name, "target": target_scene})
					print("✅ %s → %s" % [gate.name, target_scene.get_file()])
				else:
					invalid_targets.append({"gate": gate.name, "target": target_scene})
					print("❌ %s → %s (MISSING)" % [gate.name, target_scene.get_file()])
			else:
				print("⚠️ %s has no target scene set" % gate.name)
	
	print("\n📊 Teleport target validation:")
	print("   Valid targets: %d" % valid_targets.size())
	print("   Invalid targets: %d" % invalid_targets.size())

func validate_ui_scenes():
	print("\n🎨 VALIDATING UI SCENE REFERENCES...")
	
	var ui_scenes = [
		"res://hud_progress/scenes/inventory_tab.tscn",
		"res://hud_progress/scenes/missions_tab.tscn",
		"res://hud_progress/scenes/skills_tab.tscn",
		"res://ui/scenes/inventory_button_manager.tscn"
	]
	
	var missing_ui = []
	var valid_ui = []
	
	for ui_scene in ui_scenes:
		if FileAccess.file_exists(ui_scene):
			valid_ui.append(ui_scene)
			print("✅ %s" % ui_scene.get_file())
		else:
			missing_ui.append(ui_scene)
			print("❌ %s - MISSING" % ui_scene.get_file())
	
	print("\n📊 UI scene validation:")
	print("   Valid UI scenes: %d/%d" % [valid_ui.size(), ui_scenes.size()])
	print("   Missing UI scenes: %d" % missing_ui.size())

func create_fallback_resources():
	print("\n🛠️ CREATING FALLBACK RESOURCES...")
	
	# Create fallback texture for missing UI elements
	create_fallback_texture()
	
	# Create fallback scenes for missing components
	create_fallback_scenes()

func create_fallback_texture():
	"""Create a simple fallback texture for missing UI elements"""
	print("Creating fallback texture...")
	
	# Create a simple colored texture
	var image = Image.create(64, 64, false, Image.FORMAT_RGB8)
	image.fill(Color(0.5, 0.5, 0.5))  # Gray color
	
	var texture = ImageTexture.new()
	texture.create_from_image(image)
	
	# Save as fallback
	var fallback_path = "res://assets/fallback_texture.tres"
	ResourceSaver.save(texture, fallback_path)
	print("✅ Created fallback texture at: %s" % fallback_path)

func create_fallback_scenes():
	"""Create minimal fallback scenes for missing UI components"""
	print("Creating fallback scenes...")
	
	# This would require more complex scene creation
	# For now, just log what needs to be created
	var missing_scenes = [
		"res://ui/scenes/inventory_button_manager.tscn",
		"res://hud_progress/scenes/skills_tab.tscn"
	]
	
	for scene_path in missing_scenes:
		if not FileAccess.file_exists(scene_path):
			print("⚠️ Need to create: %s" % scene_path)

func generate_resource_report():
	print("\n📋 === RESOURCE LOADING HEALTH REPORT ===")
	
	var total_issues = 0
	var fixed_issues = 0
	
	# Count scene issues
	var scene_issues = count_missing_scenes()
	total_issues += scene_issues
	print("Scene issues: %d" % scene_issues)
	
	# Count texture issues  
	var texture_issues = count_missing_textures()
	total_issues += texture_issues
	print("Texture issues: %d" % texture_issues)
	
	# Count teleport issues
	var teleport_issues = count_invalid_teleport_targets()
	total_issues += teleport_issues
	print("Teleport target issues: %d" % teleport_issues)
	
	print("\n📊 TOTAL RESOURCE ISSUES: %d" % total_issues)
	
	if total_issues == 0:
		print("🟢 EXCELLENT - All resources are valid")
	elif total_issues <= 5:
		print("🟡 GOOD - Minor resource issues")
	elif total_issues <= 10:
		print("🟠 FAIR - Several resource issues need attention")
	else:
		print("🔴 POOR - Many resource issues need fixing")

func count_missing_scenes() -> int:
	var critical_scenes = [
		"res://maps/lang_van_lang/scenes/lang_van_lang.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn",
		"res://hud_progress/scenes/inventory_tab.tscn"
	]
	
	var missing = 0
	for scene in critical_scenes:
		if not FileAccess.file_exists(scene):
			missing += 1
	
	return missing

func count_missing_textures() -> int:
	var texture_paths = [
		"res://assets/images/ui/inventory_button.png",
		"res://assets/images/ui/teleport_gate_icon.png"
	]
	
	var missing = 0
	for texture in texture_paths:
		if not FileAccess.file_exists(texture):
			missing += 1
	
	return missing

func count_invalid_teleport_targets() -> int:
	var teleport_gates = get_tree().get_nodes_in_group("teleport_gates")
	var invalid = 0
	
	for gate in teleport_gates:
		if gate.has_method("get") and gate.get("target_scene"):
			var target_scene = gate.target_scene
			if target_scene and not target_scene.is_empty():
				if not FileAccess.file_exists(target_scene):
					invalid += 1
	
	return invalid

func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		print("\n🔄 Re-running resource fixes...")
		fix_all_resource_issues()
	elif event.is_action_pressed("ui_cancel"):  # Escape key
		print("\n📋 Generating resource report...")
		generate_resource_report()
	elif event.is_action_pressed("ui_select"):  # Space key
		print("\n🛠️ Creating fallback resources...")
		create_fallback_resources()
