# Godot engine generated files
.import/
.export/
mono/

# Exported binaries (built games)
*.exe
*.apk
*.x86_64
*.zip
*.pck
*.dmg

# System/Editor files
.DS_Store
Thumbs.db
desktop.ini

# Editor settings (optional: ignore if you want per-user settings only)
.editor/
.godot/

# IDE/editor folders
.vscode/
.idea/
*.code-workspace

# OS-generated files
*.log
*.tmp
*.bak

# C# specific (if using Godot with C#)
*.csproj
*.sln
*.mono/
*.dll
*.pdb
*.mdb

# Python scripts (if you have tools/scripts)
__pycache__/
*.pyc


#Context Code Ignore
/memory-bank
# Godot export presets (OPTIONAL)
# Uncomment if you don't want export settings shared across team members
# export_presets.cfg
