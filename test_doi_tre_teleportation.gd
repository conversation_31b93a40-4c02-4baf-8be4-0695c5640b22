# Test script for doi_tre teleportation issues
extends Node

var test_results = {}

func _ready():
	print("🧪 Starting doi_tre teleportation tests...")
	call_deferred("run_all_tests")

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				run_all_tests()
			KEY_F2:
				test_dong_dau_to_doi_tre()
			KEY_F3:
				test_lang_van_lang_to_doi_tre()
			KEY_F4:
				test_map_name_display()
			KEY_F5:
				debug_doi_tre_spawn_positions()

func run_all_tests():
	print("\n🔍 COMPREHENSIVE DOI_TRE TELEPORTATION TESTS")
	print("=" * 50)
	
	# Test 1: Check teleport gate configurations
	test_teleport_gate_configs()
	
	# Test 2: Check spawn positions
	test_spawn_positions()
	
	# Test 3: Check map name configuration
	test_map_name_config()
	
	# Test 4: Check scene files
	test_scene_files()
	
	print_test_summary()

func test_teleport_gate_configs():
	print("\n📋 Testing teleport gate configurations...")
	
	# Test dong_dau to doi_tre gate
	var dong_dau_gate_path = "res://maps/dong_dau/scenes/TeleportGate_DongDau_DoiTre.tscn"
	test_results["dong_dau_gate_exists"] = ResourceLoader.exists(dong_dau_gate_path)
	print("✅ DongDau→DoiTre gate exists: %s" % test_results["dong_dau_gate_exists"])
	
	# Test lang_van_lang to doi_tre gate
	var lang_gate_path = "res://maps/lang_van_lang/scenes/TeleportGate_LangVanLang_DoiTre.tscn"
	test_results["lang_gate_exists"] = ResourceLoader.exists(lang_gate_path)
	print("✅ LangVanLang→DoiTre gate exists: %s" % test_results["lang_gate_exists"])
	
	# Test doi_tre scene
	var doi_tre_scene_path = "res://maps/doi_tre/scenes/doi_tre.tscn"
	test_results["doi_tre_scene_exists"] = ResourceLoader.exists(doi_tre_scene_path)
	print("✅ DoiTre scene exists: %s" % test_results["doi_tre_scene_exists"])

func test_spawn_positions():
	print("\n📍 Testing spawn positions...")
	
	# Expected spawn position for doi_tre
	var expected_spawn = Vector2(-2292, -538)
	print("🎯 Expected doi_tre spawn position: %s" % expected_spawn)
	
	# Check if position is within map bounds
	var map_bounds = {
		"left": -2951,
		"right": 1497,
		"top": -850,
		"bottom": -120
	}
	
	var within_bounds = (
		expected_spawn.x >= map_bounds.left and 
		expected_spawn.x <= map_bounds.right and
		expected_spawn.y >= map_bounds.top and 
		expected_spawn.y <= map_bounds.bottom
	)
	
	test_results["spawn_within_bounds"] = within_bounds
	print("✅ Spawn position within bounds: %s" % within_bounds)
	
	if not within_bounds:
		print("❌ CRITICAL: Spawn position is outside map bounds!")
		print("   Map bounds: left=%d, right=%d, top=%d, bottom=%d" % [map_bounds.left, map_bounds.right, map_bounds.top, map_bounds.bottom])

func test_map_name_config():
	print("\n🗺️ Testing map name configuration...")
	
	# Expected map name
	var expected_name = "Đồi Tre"
	print("📝 Expected map name: '%s'" % expected_name)
	
	# Check if GlobalMapNameUI is available
	test_results["global_ui_available"] = GlobalMapNameUI != null
	print("✅ GlobalMapNameUI available: %s" % test_results["global_ui_available"])

func test_scene_files():
	print("\n📁 Testing scene file integrity...")
	
	var scenes_to_test = [
		"res://maps/doi_tre/scenes/doi_tre.tscn",
		"res://maps/dong_dau/scenes/dong_dau.tscn", 
		"res://maps/lang_van_lang/scenes/lang_van_lang.tscn"
	]
	
	for scene_path in scenes_to_test:
		var exists = ResourceLoader.exists(scene_path)
		var scene_name = scene_path.get_file().get_basename()
		test_results[scene_name + "_exists"] = exists
		print("✅ %s exists: %s" % [scene_name, exists])

func test_dong_dau_to_doi_tre():
	print("\n🧪 MANUAL TEST: DongDau → DoiTre")
	
	if SceneManager:
		print("📍 Setting spawn position: Vector2(-2292, -538)")
		SceneManager.set_next_spawn_position(Vector2(-2292, -538))
		
		print("🔄 Teleporting to doi_tre...")
		SceneManager.goto_scene("res://maps/doi_tre/scenes/doi_tre.tscn")
	else:
		print("❌ SceneManager not available")

func test_lang_van_lang_to_doi_tre():
	print("\n🧪 MANUAL TEST: LangVanLang → DoiTre")
	
	if SceneManager:
		print("📍 Setting spawn position: Vector2(-2292, -538)")
		SceneManager.set_next_spawn_position(Vector2(-2292, -538))
		
		print("🔄 Teleporting to doi_tre...")
		SceneManager.goto_scene("res://maps/doi_tre/scenes/doi_tre.tscn")
	else:
		print("❌ SceneManager not available")

func test_map_name_display():
	print("\n🧪 MANUAL TEST: Map Name Display")
	
	if GlobalMapNameUI:
		print("🗺️ Setting map name to 'Đồi Tre'")
		GlobalMapNameUI.set_map_name("Đồi Tre")
		GlobalMapNameUI.show_map_name()
		print("✅ Map name display test completed")
	else:
		print("❌ GlobalMapNameUI not available")

func debug_doi_tre_spawn_positions():
	print("\n🔍 DEBUG: DoiTre spawn positions")
	
	# Default player position in doi_tre.tscn
	var default_pos = Vector2(-2232, -581)
	print("🏠 Default player position: %s" % default_pos)
	
	# Safe spawn position from controller
	var safe_pos = Vector2(-2292, -538)
	print("🛡️ Safe spawn position: %s" % safe_pos)
	
	# Teleport gate position in doi_tre
	var gate_pos = Vector2(-2798, -229)
	print("🚪 Teleport gate position: %s" % gate_pos)
	
	# Calculate distances
	var dist_default_to_safe = default_pos.distance_to(safe_pos)
	var dist_safe_to_gate = safe_pos.distance_to(gate_pos)
	
	print("📏 Distance default→safe: %.1f" % dist_default_to_safe)
	print("📏 Distance safe→gate: %.1f" % dist_safe_to_gate)

func print_test_summary():
	print("\n📊 TEST SUMMARY")
	print("=" * 30)
	
	var passed = 0
	var total = 0
	
	for key in test_results:
		total += 1
		if test_results[key]:
			passed += 1
			print("✅ %s: PASS" % key)
		else:
			print("❌ %s: FAIL" % key)
	
	print("\n🎯 Results: %d/%d tests passed (%.1f%%)" % [passed, total, (float(passed)/total) * 100])
	
	if passed == total:
		print("🎉 ALL TESTS PASSED! Teleportation should work correctly.")
	else:
		print("⚠️ Some tests failed. Check the issues above.")
	
	print("\n🎮 CONTROLS:")
	print("F1 - Run all tests")
	print("F2 - Test DongDau→DoiTre teleport")
	print("F3 - Test LangVanLang→DoiTre teleport") 
	print("F4 - Test map name display")
	print("F5 - Debug spawn positions")
