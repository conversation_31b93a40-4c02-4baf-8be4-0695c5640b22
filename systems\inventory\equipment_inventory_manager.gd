# Equipment Inventory Manager - <PERSON><PERSON><PERSON><PERSON> <PERSON>ý trang bị của ng<PERSON><PERSON><PERSON> chơi
extends Node

# Dictionary để lưu trữ số lượng trang bị
var equipment_inventory = {
	"Armor": 0,
	"Pant": 0,
	"Boots": 0,    # Gi<PERSON><PERSON> - mớ<PERSON> thêm
	"Helmet": 0,   # Future expansion
	"Gloves": 0    # Future expansion
}

# Signal để thông báo khi equipment inventory thay đổi
signal equipment_inventory_updated(equipment_type: String, new_count: int)

# Thêm trang bị vào inventory
func add_equipment(equipment_type: String, quantity: int = 1) -> void:
	if equipment_type in equipment_inventory:
		equipment_inventory[equipment_type] += quantity
		print("Added " + str(quantity) + " " + equipment_type + " to equipment inventory. Total: " + str(equipment_inventory[equipment_type]))
		emit_signal("equipment_inventory_updated", equipment_type, equipment_inventory[equipment_type])
	else:
		print("WARNING: Unknown equipment type: " + equipment_type)

# L<PERSON>y số lượng trang bị hiện có
func get_equipment_count(equipment_type: String) -> int:
	return equipment_inventory.get(equipment_type, 0)

# Sử dụng trang bị (giảm số lượng)
func use_equipment(equipment_type: String, quantity: int = 1) -> bool:
	if equipment_type in equipment_inventory and equipment_inventory[equipment_type] >= quantity:
		equipment_inventory[equipment_type] -= quantity
		print("Used " + str(quantity) + " " + equipment_type + ". Remaining: " + str(equipment_inventory[equipment_type]))
		emit_signal("equipment_inventory_updated", equipment_type, equipment_inventory[equipment_type])
		return true
	else:
		print("Not enough " + equipment_type + " in equipment inventory")
		return false

# Lấy toàn bộ equipment inventory
func get_full_equipment_inventory() -> Dictionary:
	return equipment_inventory.duplicate()

# Xóa tất cả trang bị (reset inventory)
func clear_equipment_inventory() -> void:
	for equipment_type in equipment_inventory.keys():
		equipment_inventory[equipment_type] = 0
		emit_signal("equipment_inventory_updated", equipment_type, 0)
	print("Equipment inventory cleared")

# Kiểm tra xem có trang bị nào không
func has_any_equipment() -> bool:
	for count in equipment_inventory.values():
		if count > 0:
			return true
	return false

# Lấy tổng số trang bị
func get_total_equipment_count() -> int:
	var total = 0
	for count in equipment_inventory.values():
		total += count
	return total

# Hiển thị equipment inventory (debug)
func print_equipment_inventory() -> void:
	print("=== EQUIPMENT INVENTORY ===")
	for equipment_type in equipment_inventory.keys():
		print(equipment_type + ": " + str(equipment_inventory[equipment_type]))
	print("===========================")

# Equip trang bị (có thể mở rộng sau)
func equip_item(equipment_type: String) -> bool:
	if get_equipment_count(equipment_type) > 0:
		print("Equipped " + equipment_type)
		# TODO: Thêm logic equip trang bị ở đây
		return true
	else:
		print("No " + equipment_type + " available to equip")
		return false

# Unequip trang bị (có thể mở rộng sau)
func unequip_item(equipment_type: String) -> void:
	print("Unequipped " + equipment_type)
	# TODO: Thêm logic unequip trang bị ở đây
