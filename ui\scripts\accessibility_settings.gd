# Accessibility Settings Manager - <PERSON><PERSON><PERSON><PERSON> <PERSON> c<PERSON>i đặt accessibility
extends Control

# UI Components
@onready var high_contrast_toggle: <PERSON><PERSON><PERSON>
@onready var large_text_toggle: <PERSON><PERSON><PERSON>
@onready var reduced_motion_toggle: <PERSON><PERSON><PERSON>
@onready var screen_reader_toggle: <PERSON><PERSON><PERSON>
@onready var keyboard_nav_toggle: <PERSON><PERSON><PERSON>
@onready var gamepad_nav_toggle: <PERSON><PERSON><PERSON>
@onready var audio_cues_toggle: <PERSON><PERSON><PERSON>
@onready var font_size_slider: <PERSON><PERSON><PERSON>r
@onready var font_size_label: Label
@onready var ui_scale_slider: H<PERSON>lider
@onready var ui_scale_label: Label

# Settings
var settings: Dictionary = {
	"high_contrast": false,
	"large_text": false,
	"reduced_motion": false,
	"screen_reader": false,
	"keyboard_navigation": true,
	"gamepad_navigation": true,
	"audio_cues": true,
	"font_size_multiplier": 1.0,
	"ui_scale_multiplier": 1.0
}

# Theme manager reference
var theme_manager: Node = null
var navigation_manager: Node = null

signal settings_changed(setting_name: String, value)

func _ready():
	_setup_ui_components()
	_load_settings()
	_connect_signals()
	_apply_current_settings()
	print("♿ Accessibility Settings initialized")

func _setup_ui_components():
	"""Setup UI components programmatically if not found in scene"""
	# This would create the UI if it doesn't exist in the scene
	if not high_contrast_toggle:
		_create_accessibility_ui()

func _create_accessibility_ui():
	"""Create accessibility UI programmatically"""
	# Create main container
	var main_container = VBoxContainer.new()
	main_container.name = "AccessibilityContainer"
	add_child(main_container)
	
	# Title
	var title = Label.new()
	title.text = "Cài đặt Accessibility"
	title.add_theme_font_size_override("font_size", 32)
	title.add_theme_color_override("font_color", Color(1, 0.9, 0.3, 1))
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_container.add_child(title)
	
	# Spacer
	var spacer1 = Control.new()
	spacer1.custom_minimum_size = Vector2(0, 20)
	main_container.add_child(spacer1)
	
	# Visual Settings Section
	var visual_section = _create_section("Cài đặt Hình ảnh")
	main_container.add_child(visual_section)
	
	high_contrast_toggle = _create_toggle("Chế độ Tương phản Cao", "Tăng độ tương phản để dễ nhìn hơn")
	visual_section.add_child(high_contrast_toggle)
	
	large_text_toggle = _create_toggle("Chữ Lớn", "Tăng kích thước chữ trong toàn bộ game")
	visual_section.add_child(large_text_toggle)
	
	reduced_motion_toggle = _create_toggle("Giảm Chuyển động", "Giảm hiệu ứng animation và chuyển động")
	visual_section.add_child(reduced_motion_toggle)
	
	# Font size slider
	var font_container = HBoxContainer.new()
	var font_label = Label.new()
	font_label.text = "Kích thước chữ:"
	font_label.custom_minimum_size = Vector2(200, 0)
	font_container.add_child(font_label)
	
	font_size_slider = HSlider.new()
	font_size_slider.min_value = 0.8
	font_size_slider.max_value = 2.0
	font_size_slider.step = 0.1
	font_size_slider.value = 1.0
	font_size_slider.custom_minimum_size = Vector2(200, 30)
	font_container.add_child(font_size_slider)
	
	font_size_label = Label.new()
	font_size_label.text = "100%"
	font_size_label.custom_minimum_size = Vector2(60, 0)
	font_container.add_child(font_size_label)
	
	visual_section.add_child(font_container)
	
	# Navigation Settings Section
	var nav_section = _create_section("Cài đặt Điều hướng")
	main_container.add_child(nav_section)
	
	keyboard_nav_toggle = _create_toggle("Điều hướng Bàn phím", "Sử dụng bàn phím để điều hướng menu")
	nav_section.add_child(keyboard_nav_toggle)
	
	gamepad_nav_toggle = _create_toggle("Điều hướng Gamepad", "Sử dụng gamepad để điều hướng menu")
	nav_section.add_child(gamepad_nav_toggle)
	
	audio_cues_toggle = _create_toggle("Âm thanh Phản hồi", "Phát âm thanh khi điều hướng và tương tác")
	nav_section.add_child(audio_cues_toggle)
	
	# Action buttons
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	
	var apply_button = Button.new()
	apply_button.text = "Áp dụng"
	apply_button.custom_minimum_size = Vector2(120, 40)
	apply_button.pressed.connect(_apply_settings)
	button_container.add_child(apply_button)
	
	var reset_button = Button.new()
	reset_button.text = "Đặt lại"
	reset_button.custom_minimum_size = Vector2(120, 40)
	reset_button.pressed.connect(_reset_settings)
	button_container.add_child(reset_button)
	
	main_container.add_child(button_container)
	
	print("🎨 Accessibility UI created programmatically")

func _create_section(title: String) -> VBoxContainer:
	"""Create a settings section with title"""
	var section = VBoxContainer.new()
	
	var section_title = Label.new()
	section_title.text = title
	section_title.add_theme_font_size_override("font_size", 24)
	section_title.add_theme_color_override("font_color", Color(0.8, 0.6, 0.2, 1))
	section.add_child(section_title)
	
	var separator = HSeparator.new()
	separator.add_theme_constant_override("separation", 10)
	section.add_child(separator)
	
	return section

func _create_toggle(label_text: String, tooltip: String) -> CheckBox:
	"""Create a toggle with label and tooltip"""
	var toggle = CheckBox.new()
	toggle.text = label_text
	toggle.tooltip_text = tooltip
	toggle.custom_minimum_size = Vector2(0, 35)
	
	# Apply enhanced styling
	toggle.add_theme_font_size_override("font_size", 18)
	toggle.add_theme_color_override("font_color", Color.WHITE)
	toggle.add_theme_color_override("font_hover_color", Color(1, 0.9, 0.3, 1))
	
	return toggle

func _connect_signals():
	"""Connect all UI signals"""
	if high_contrast_toggle:
		high_contrast_toggle.toggled.connect(_on_high_contrast_toggled)
	if large_text_toggle:
		large_text_toggle.toggled.connect(_on_large_text_toggled)
	if reduced_motion_toggle:
		reduced_motion_toggle.toggled.connect(_on_reduced_motion_toggled)
	if keyboard_nav_toggle:
		keyboard_nav_toggle.toggled.connect(_on_keyboard_nav_toggled)
	if gamepad_nav_toggle:
		gamepad_nav_toggle.toggled.connect(_on_gamepad_nav_toggled)
	if audio_cues_toggle:
		audio_cues_toggle.toggled.connect(_on_audio_cues_toggled)
	if font_size_slider:
		font_size_slider.value_changed.connect(_on_font_size_changed)

# Signal handlers
func _on_high_contrast_toggled(enabled: bool):
	settings["high_contrast"] = enabled
	_apply_high_contrast(enabled)
	settings_changed.emit("high_contrast", enabled)

func _on_large_text_toggled(enabled: bool):
	settings["large_text"] = enabled
	_apply_large_text(enabled)
	settings_changed.emit("large_text", enabled)

func _on_reduced_motion_toggled(enabled: bool):
	settings["reduced_motion"] = enabled
	_apply_reduced_motion(enabled)
	settings_changed.emit("reduced_motion", enabled)

func _on_keyboard_nav_toggled(enabled: bool):
	settings["keyboard_navigation"] = enabled
	_apply_keyboard_navigation(enabled)
	settings_changed.emit("keyboard_navigation", enabled)

func _on_gamepad_nav_toggled(enabled: bool):
	settings["gamepad_navigation"] = enabled
	_apply_gamepad_navigation(enabled)
	settings_changed.emit("gamepad_navigation", enabled)

func _on_audio_cues_toggled(enabled: bool):
	settings["audio_cues"] = enabled
	_apply_audio_cues(enabled)
	settings_changed.emit("audio_cues", enabled)

func _on_font_size_changed(value: float):
	settings["font_size_multiplier"] = value
	if font_size_label:
		font_size_label.text = "%d%%" % (value * 100)
	_apply_font_size(value)
	settings_changed.emit("font_size_multiplier", value)

# Apply settings functions
func _apply_high_contrast(enabled: bool):
	"""Apply high contrast mode"""
	if theme_manager:
		if enabled:
			theme_manager.set_color_scheme("high_contrast")
		else:
			theme_manager.set_color_scheme("golden")
	print("🎨 High contrast mode: %s" % ("enabled" if enabled else "disabled"))

func _apply_large_text(enabled: bool):
	"""Apply large text mode"""
	var multiplier = 1.3 if enabled else 1.0
	_apply_font_size(multiplier)
	print("📝 Large text mode: %s" % ("enabled" if enabled else "disabled"))

func _apply_reduced_motion(enabled: bool):
	"""Apply reduced motion setting"""
	if navigation_manager:
		navigation_manager.set_reduced_motion(enabled)
	print("🎬 Reduced motion: %s" % ("enabled" if enabled else "disabled"))

func _apply_keyboard_navigation(enabled: bool):
	"""Apply keyboard navigation setting"""
	if navigation_manager:
		navigation_manager.keyboard_navigation = enabled
	print("⌨️ Keyboard navigation: %s" % ("enabled" if enabled else "disabled"))

func _apply_gamepad_navigation(enabled: bool):
	"""Apply gamepad navigation setting"""
	if navigation_manager:
		navigation_manager.gamepad_navigation = enabled
	print("🎮 Gamepad navigation: %s" % ("enabled" if enabled else "disabled"))

func _apply_audio_cues(enabled: bool):
	"""Apply audio cues setting"""
	# This would enable/disable audio feedback
	print("🔊 Audio cues: %s" % ("enabled" if enabled else "disabled"))

func _apply_font_size(multiplier: float):
	"""Apply font size multiplier"""
	# This would update font sizes across the UI
	print("📝 Font size multiplier: %.1f" % multiplier)

func _apply_current_settings():
	"""Apply all current settings"""
	_apply_high_contrast(settings["high_contrast"])
	_apply_large_text(settings["large_text"])
	_apply_reduced_motion(settings["reduced_motion"])
	_apply_keyboard_navigation(settings["keyboard_navigation"])
	_apply_gamepad_navigation(settings["gamepad_navigation"])
	_apply_audio_cues(settings["audio_cues"])
	_apply_font_size(settings["font_size_multiplier"])

func _apply_settings():
	"""Apply button pressed"""
	_apply_current_settings()
	_save_settings()
	print("✅ Accessibility settings applied and saved")

func _reset_settings():
	"""Reset to default settings"""
	settings = {
		"high_contrast": false,
		"large_text": false,
		"reduced_motion": false,
		"screen_reader": false,
		"keyboard_navigation": true,
		"gamepad_navigation": true,
		"audio_cues": true,
		"font_size_multiplier": 1.0,
		"ui_scale_multiplier": 1.0
	}
	_update_ui_from_settings()
	_apply_current_settings()
	print("🔄 Accessibility settings reset to defaults")

func _update_ui_from_settings():
	"""Update UI controls from current settings"""
	if high_contrast_toggle:
		high_contrast_toggle.button_pressed = settings["high_contrast"]
	if large_text_toggle:
		large_text_toggle.button_pressed = settings["large_text"]
	if reduced_motion_toggle:
		reduced_motion_toggle.button_pressed = settings["reduced_motion"]
	if keyboard_nav_toggle:
		keyboard_nav_toggle.button_pressed = settings["keyboard_navigation"]
	if gamepad_nav_toggle:
		gamepad_nav_toggle.button_pressed = settings["gamepad_navigation"]
	if audio_cues_toggle:
		audio_cues_toggle.button_pressed = settings["audio_cues"]
	if font_size_slider:
		font_size_slider.value = settings["font_size_multiplier"]
		if font_size_label:
			font_size_label.text = "%d%%" % (settings["font_size_multiplier"] * 100)

# Save/Load functions
func _save_settings():
	"""Save settings to file"""
	var config = ConfigFile.new()
	for key in settings.keys():
		config.set_value("accessibility", key, settings[key])
	config.save("user://accessibility_settings.cfg")

func _load_settings():
	"""Load settings from file"""
	var config = ConfigFile.new()
	if config.load("user://accessibility_settings.cfg") == OK:
		for key in settings.keys():
			if config.has_section_key("accessibility", key):
				settings[key] = config.get_value("accessibility", key)
	_update_ui_from_settings()

# Public API
func get_setting(setting_name: String):
	"""Get a specific setting value"""
	return settings.get(setting_name, null)

func set_setting(setting_name: String, value):
	"""Set a specific setting value"""
	if settings.has(setting_name):
		settings[setting_name] = value
		_update_ui_from_settings()
		settings_changed.emit(setting_name, value)
