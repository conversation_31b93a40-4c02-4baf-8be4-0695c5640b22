[gd_resource type="SpriteFrames" load_steps=14 format=3 uid="uid://b68brhb20y53t"]

[sub_resource type="AtlasTexture" id="AtlasTexture_7s4rp"]
region = Rect2(0, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tobi5"]
region = Rect2(64, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_pqxnj"]
region = Rect2(128, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_87ctp"]
region = Rect2(192, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_635b2"]
region = Rect2(256, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7mtfe"]
region = Rect2(320, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_1wrr3"]
region = Rect2(384, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_v8vvj"]
region = Rect2(448, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_c563p"]
region = Rect2(512, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_rauix"]
region = Rect2(576, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_16koq"]
region = Rect2(640, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_fv0sn"]
region = Rect2(704, 256, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_w1bgm"]
region = Rect2(768, 256, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7s4rp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tobi5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pqxnj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_87ctp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_635b2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7mtfe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1wrr3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v8vvj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c563p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rauix")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_16koq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fv0sn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w1bgm")
}],
"loop": true,
"name": &"burst",
"speed": 10.0
}]
