# Global UI Manager - <PERSON><PERSON><PERSON><PERSON> lý UI to<PERSON>n cục
extends Node

var inventory_button_manager: CanvasLayer = null

func _ready() -> void:
	# Khởi tạo inventory button manager
	setup_inventory_button()

func setup_inventory_button() -> void:
	# Load inventory button manager scene with fallback paths
	var inventory_button_paths = [
		"res://ui/scenes/inventory_button_manager.tscn",
		"res://hud_progress/scenes/inventory_button_manager.tscn",
		"res://scenes/inventory_button_manager.tscn"
	]

	var inventory_button_scene = null
	for path in inventory_button_paths:
		if FileAccess.file_exists(path):
			inventory_button_scene = load(path)
			if inventory_button_scene:
				print("Loaded inventory button manager from: %s" % path)
				break

	if inventory_button_scene:
		inventory_button_manager = inventory_button_scene.instantiate()
		add_child(inventory_button_manager)
		print("Inventory button manager loaded successfully")
	else:
		print("INFO: Cannot load inventory_button_manager.tscn from any path - creating programmatically")
		print("   Checked paths: %s" % str(inventory_button_paths))
		# Create inventory button manager programmatically as fallback
		_create_fallback_inventory_button()

func _create_fallback_inventory_button() -> void:
	"""Create inventory button manager programmatically if scene file is missing"""
	inventory_button_manager = CanvasLayer.new()
	inventory_button_manager.name = "InventoryButtonManager"
	add_child(inventory_button_manager)

	# Add the inventory button manager script
	var script = load("res://ui/scripts/inventory_button_manager.gd")
	if script:
		inventory_button_manager.set_script(script)
		print("✅ Created fallback inventory button manager")
	else:
		print("⚠️ Could not load inventory button manager script")

# Hàm để toggle inventory từ các script khác
func toggle_inventory() -> void:
	if inventory_button_manager and inventory_button_manager.has_method("toggle_inventory"):
		inventory_button_manager.toggle_inventory()

# Hàm kiểm tra xem inventory có đang mở không
func is_inventory_open() -> bool:
	if inventory_button_manager and inventory_button_manager.has_method("is_inventory_tab_open"):
		return inventory_button_manager.is_inventory_tab_open()
	return false
