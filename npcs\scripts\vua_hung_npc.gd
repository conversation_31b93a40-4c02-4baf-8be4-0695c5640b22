extends CharacterBody2D

@export var sprites: SpriteFrames
@export var npc_name: String = "Vua Hùng"
@export var dialog_text: String = "Xin chào!"
@export var enemy_spawner_path: NodePath

@onready var animatedSprite: AnimatedSprite2D = $AnimatedSprite2D
@onready var nameLabel: Label = $NameLabel
@onready var clickableButton: Button = $ClickableButton
@onready var enemy_spawner = null
@onready var _debug_spawner_path = enemy_spawner_path

const SPEED = 300.0
const JUMP_VELOCITY = -400.0
const GRAVITY = 980.0

# Preload dialog box scene
var InteractiveDialogBoxScene = preload("res://npcs/scenes/InteractiveDialogBox.tscn")
var dialog_box = null

# Trạng thái nhiệm vụ
var mission_state = "not_started" # not_started, in_progress, completed

# Quest IDs
const TALK_QUEST_ID = "vua_hung_talk"
const DEFEND_QUEST_ID = "defend_village"

# D<PERSON> liệu hội thoại
var dialog_data = [
	# 0: Hội thoại ban đầu
	{
		"speaker": "Vua Hùng",
		"text": "Chào chàng trẻ - người được chọn, ta có một việc vô cùng quan trọng, có mối tương quan rất lớn đối với tương lai của dân tộc. Cần đến cậu giúp đỡ.",
		"choices": [
			{
				"text": "Tôi sẵn sàng giúp đỡ. Ngài cần gì?",
				"next_index": 1
			},
			{
				"text": "Ngài có thể cho tôi biết thêm chi tiết không?",
				"next_index": 2
			},
			{
				"text": "Tôi đang bận, để lúc khác nhé.",
				"next_index": 3
			}
		]
	},
	# 1: Hội thoại khi người chơi đồng ý ngay lập tức
	{
		"speaker": "Vua Hùng",
		"text": "Hiện tại bộ lạc ta đang bị tấn công. Người trong làng bị ảnh hưởng rất nặng nề. Kẻ thù đang tập trung lực lượng để tấn công cả hai cổng làng. Cậu hãy giúp ta hỗ trợ binh lính bảo vệ Làng được không?",
		"choices": [
			{
				"text": "Tôi sẽ bảo vệ làng bằng mọi giá!",
				"next_index": 4
			},
			{
				"text": "Tôi sẽ nhận nhiệm vụ này, nhưng phần thưởng là gì?",
				"next_index": 6
			}
		]
	},
	# 2: Hội thoại khi người chơi hỏi thêm thông tin
	{
		"speaker": "Vua Hùng",
		"text": "Làng của chúng ta đang bị đe dọa bởi các bộ lạc thù địch. Chúng muốn chiếm đất đai và tài nguyên của chúng ta. Hiện tại, chúng đang tập trung lực lượng để tấn công cả hai cổng làng. Nếu không ngăn chặn kịp thời, hậu quả sẽ rất nghiêm trọng.",
		"choices": [
			{
				"text": "Tôi hiểu rồi. Tôi sẽ giúp Ngài.",
				"next_index": 1
			},
			{
				"text": "Tôi cần thêm thời gian chuẩn bị.",
				"next_index": 3
			}
		]
	},
	# 3: Hội thoại từ chối
	{
		"speaker": "Vua Hùng",
		"text": "Ta hiểu. Khi nào cậu sẵn sàng, hãy quay lại gặp ta. Nhưng đừng để quá muộn, làng của chúng ta đang gặp nguy hiểm."
	},
	# 4: Người chơi đồng ý bảo vệ làng
	{
		"speaker": "Người chơi",
		"text": "Tôi luôn sẵn lòng bảo vệ làng và người dân.",
		"next_index": 5
	},
	# 5: Vua Hùng giao nhiệm vụ
	{
		"speaker": "Vua Hùng",
		"text": "Tuyệt vời! Ta sẽ trao cho cậu những trang bị khởi đầu để giúp cậu trong nhiệm vụ này. Hãy tiêu diệt 50 kẻ địch đang tấn công cổng làng. Khi hoàn thành, hãy quay lại gặp ta để nhận phần thưởng."
	},
	# 6: Hội thoại về phần thưởng
	{
		"speaker": "Vua Hùng",
		"text": "Một chiến binh dũng cảm như cậu xứng đáng được hậu đãi. Khi hoàn thành nhiệm vụ, cậu sẽ nhận được một bộ trang bị khởi đầu quý giá và một số vàng để cậu có thể tiếp tục hành trình. Đây là những vật phẩm hiếm có trong làng của chúng ta.",
		"choices": [
			{
				"text": "Nghe hấp dẫn đấy. Tôi nhận nhiệm vụ!",
				"next_index": 4
			},
			{
				"text": "Tôi sẽ làm vì làng, không phải vì phần thưởng.",
				"next_index": 4
			}
		]
	},
	# 7: Hội thoại khi nhiệm vụ đang thực hiện
	{
		"speaker": "Vua Hùng",
		"text": "Cậu đã hoàn thành nhiệm vụ bảo vệ làng chưa? Mọi người đang cần sự giúp đỡ của cậu. Hãy tiêu diệt hết kẻ địch đang tấn công cổng làng."
	},
	# 8: Hội thoại khi hoàn thành nhiệm vụ
	{
		"speaker": "Vua Hùng",
		"text": "Cảm ơn chàng trai! Cậu đã giúp ta bảo vệ Làng thành công. Đây là phần thưởng xứng đáng cho công lao của cậu - một bộ trang bị khởi đầu và số vàng để cậu có thể tiếp tục hành trình.",
		"choices": [
			{
				"text": "Cảm ơn Ngài. Tôi sẽ tiếp tục bảo vệ làng.",
				"next_index": 9
			},
			{
				"text": "Còn nhiệm vụ nào khác tôi có thể giúp không?",
				"next_index": 10
			}
		]
	},
	# 9: Phản hồi của người chơi - cảm ơn
	{
		"speaker": "Người chơi",
		"text": "Cảm ơn phần thưởng. Tôi sẽ tiếp tục bảo vệ làng!",
		"next_index": 11
	},
	# 10: Hội thoại về nhiệm vụ tiếp theo
	{
		"speaker": "Vua Hùng",
		"text": "Hiện tại, dân làng đang chịu nhiều tổn thất, phần lớn đều bị thương. Ta nghe nói, sau khu rừng, gần thượng nguồn suối Thiêng có một vị thần y ẩn cư. Trong tương lai, có lẽ ta sẽ nhờ cậu đi tìm ông ta xin thuốc về trị thương cho mọi người. Nhưng hiện tại, hãy nghỉ ngơi và hồi phục sức lực đã.",
		"next_index": 11
	},
	# 11: Kết thúc hội thoại
	{
		"speaker": "Vua Hùng",
		"text": "Ta tin tưởng ở cậu. Hãy luôn cảnh giác, kẻ thù có thể quay lại bất cứ lúc nào!"
	}
]

# Hội thoại khi đang thực hiện nhiệm vụ
var in_progress_dialog = [
	{
		"speaker": "Vua Hùng",
		"text": "Cậu đã hoàn thành nhiệm vụ bảo vệ làng chưa? Mọi người đang cần sự giúp đỡ của cậu. Hãy tiêu diệt hết kẻ địch đang tấn công cổng làng.",
		"choices": [
			{
				"text": "Tôi đang cố gắng hết sức.",
				"next_index": 1
			},
			{
				"text": "Kẻ địch ở đâu?",
				"next_index": 2
			}
		]
	},
	{
		"speaker": "Vua Hùng",
		"text": "Hãy tiếp tục cố gắng. Tương lai của làng phụ thuộc vào cậu!"
	},
	{
		"speaker": "Vua Hùng",
		"text": "Kẻ địch đang tấn công ở cả hai cổng làng - cổng trước và cổng sau. Hãy nhanh chóng tiêu diệt chúng trước khi quá muộn!"
	}
]

# Hội thoại khi hoàn thành nhiệm vụ đầu tiên
var completed_first_mission_dialog = [
	{
		"speaker": "Vua Hùng",
		"text": "Cảm ơn chàng trai! Cậu đã giúp ta bảo vệ Làng thành công. Đây là phần thưởng xứng đáng cho công lao của cậu - một bộ trang bị khởi đầu và số vàng để cậu có thể tiếp tục hành trình.",
		"choices": [
			{
				"text": "Cảm ơn Ngài. Tôi sẽ tiếp tục bảo vệ làng.",
				"next_index": 1
			},
			{
				"text": "Còn nhiệm vụ nào khác tôi có thể giúp không?",
				"next_index": 2
			}
		]
	},
	{
		"speaker": "Vua Hùng",
		"text": "Ta rất biết ơn sự giúp đỡ của cậu. Làng của chúng ta sẽ luôn chào đón cậu."
	},
	{
		"speaker": "Vua Hùng",
		"text": "Hiện tại, dân làng đang chịu nhiều tổn thất, phần lớn đều bị thương. Ta nghe nói, sau khu rừng, gần thượng nguồn suối Thiêng có một vị thần y ẩn cư. Trong tương lai, có lẽ ta sẽ nhờ cậu đi tìm ông ta xin thuốc về trị thương cho mọi người. Nhưng hiện tại, hãy nghỉ ngơi và hồi phục sức lực đã."
	}
]

func _ready() -> void:
	# Set sprite frames
	animatedSprite.sprite_frames = sprites

	# Set NPC name
	nameLabel.text = npc_name

	# Connect click signal
	clickableButton.pressed.connect(_on_clickable_button_pressed)

	# Đảm bảo button có thể nhận input
	clickableButton.mouse_filter = Control.MOUSE_FILTER_STOP

	# Check quest status
	if QuestSystem.is_quest_completed(TALK_QUEST_ID) and QuestSystem.is_quest_completed(DEFEND_QUEST_ID):
		mission_state = "completed"
	elif QuestSystem.is_quest_active(DEFEND_QUEST_ID):
		mission_state = "in_progress"

	# Connect to quest signals
	QuestSystem.quest_completed.connect(_on_quest_completed)

	# Debug enemy spawner
	print("NPC " + npc_name + " đã được khởi tạo")
	print("Enemy spawner path: " + str(_debug_spawner_path))

	# Tìm enemy spawner ngay khi khởi tạo
	_find_enemy_spawner()

	if enemy_spawner:
		print("Enemy spawner found during initialization: " + str(enemy_spawner))
	else:
		print("WARNING: Enemy spawner not found during initialization")

	# Tìm enemy spawner bằng nhiều cách khác nhau
	# Cách 1: Thử lấy từ đường dẫn đã cấu hình
	enemy_spawner = get_node_or_null(enemy_spawner_path)

	# Cách 2: Thử tìm trong scene
	if enemy_spawner == null:
		print("Trying to find spawner in scene...")
		enemy_spawner = get_tree().root.find_child("EnemyWaveSpawner", true, false)

	# Cách 3: Thử tìm theo loại node
	if enemy_spawner == null:
		print("Trying to find spawner by type...")
		var nodes = get_tree().get_nodes_in_group("enemy_spawners")
		if nodes.size() > 0:
			enemy_spawner = nodes[0]

	if enemy_spawner:
		print("Enemy spawner found: " + str(enemy_spawner))
	else:
		print("ERROR: Enemy spawner not found after all attempts!")

func _physics_process(delta: float) -> void:
	# Add the gravity.
	if not is_on_floor():
		velocity.y += GRAVITY * delta

	# NPCs don't move by default
	velocity.x = 0

	move_and_slide()

func _on_clickable_button_pressed():
	print(npc_name + ": Đã click vào NPC")
	_show_dialog()

func _show_dialog():
	print(npc_name + ": Hiển thị hội thoại")

	# Tạo hộp thoại mới mỗi lần click
	# Xóa hộp thoại cũ nếu có
	if dialog_box != null:
		dialog_box.queue_free()
		dialog_box = null

	# Tạo hộp thoại mới
	dialog_box = InteractiveDialogBoxScene.instantiate()
	get_tree().root.add_child(dialog_box)
	dialog_box.dialog_closed.connect(_on_dialog_closed)
	dialog_box.choice_made.connect(_on_choice_made)

	# Hiển thị hộp thoại dựa vào trạng thái nhiệm vụ
	if mission_state == "not_started":
		dialog_box.show_dialog(self, npc_name, dialog_data)
	elif mission_state == "in_progress":
		dialog_box.show_dialog(self, npc_name, in_progress_dialog)
	elif mission_state == "completed":
		dialog_box.show_dialog(self, npc_name, completed_first_mission_dialog)

func _on_dialog_closed():
	# Xử lý khi hộp thoại đóng
	print("Hộp thoại đã đóng")

func _on_choice_made(choice_index: int):
	print("Người chơi đã chọn: " + str(choice_index))

	# Lấy dialog_index hiện tại từ hộp thoại
	var current_dialog_index = 0
	if dialog_box and dialog_box.has_method("get_current_dialog_index"):
		current_dialog_index = dialog_box.get_current_dialog_index()

	print("Dialog index hiện tại: " + str(current_dialog_index))

	# Xử lý lựa chọn dựa trên dialog_index và choice_index
	match current_dialog_index:
		0:  # Hội thoại ban đầu
			if choice_index == 0:  # "Tôi sẵn sàng giúp đỡ. Ngài cần gì?"
				print("Người chơi sẵn sàng giúp đỡ")
			elif choice_index == 1:  # "Ngài có thể cho tôi biết thêm chi tiết không?"
				print("Người chơi yêu cầu thêm thông tin")
			elif choice_index == 2:  # "Tôi đang bận, để lúc khác nhé."
				print("Người chơi từ chối nhiệm vụ")

		1:  # Hội thoại khi người chơi đồng ý ngay lập tức
			if choice_index == 0:  # "Tôi sẽ bảo vệ làng bằng mọi giá!"
				print("Người chơi đồng ý bảo vệ làng")
				# Bắt đầu nhiệm vụ
				start_village_defense_quest()
			elif choice_index == 1:  # "Tôi sẽ nhận nhiệm vụ này, nhưng phần thưởng là gì?"
				print("Người chơi hỏi về phần thưởng")

		2:  # Hội thoại khi người chơi hỏi thêm thông tin
			if choice_index == 0:  # "Tôi hiểu rồi. Tôi sẽ giúp Ngài."
				print("Người chơi đồng ý sau khi nghe thêm thông tin")
				# Không bắt đầu nhiệm vụ ngay, chuyển đến hội thoại tiếp theo
			elif choice_index == 1:  # "Tôi cần thêm thời gian chuẩn bị."
				print("Người chơi cần thêm thời gian chuẩn bị")

		4:  # Người chơi đồng ý bảo vệ làng
			# Bắt đầu nhiệm vụ
			start_village_defense_quest()

		6:  # Hội thoại về phần thưởng
			if choice_index == 0 or choice_index == 1:  # Cả hai lựa chọn đều dẫn đến nhận nhiệm vụ
				print("Người chơi đồng ý sau khi nghe về phần thưởng")
				# Bắt đầu nhiệm vụ
				start_village_defense_quest()

		8:  # Hội thoại khi hoàn thành nhiệm vụ
			if choice_index == 0:  # "Cảm ơn Ngài. Tôi sẽ tiếp tục bảo vệ làng."
				print("Người chơi cảm ơn và tiếp tục bảo vệ làng")
			elif choice_index == 1:  # "Còn nhiệm vụ nào khác tôi có thể giúp không?"
				print("Người chơi hỏi về nhiệm vụ tiếp theo")

# Hàm bắt đầu nhiệm vụ bảo vệ làng
func start_village_defense_quest():
	# Người chơi đồng ý nhận nhiệm vụ
	mission_state = "in_progress"

	# Hiển thị thông báo nhận nhiệm vụ
	var accept_message = "Đã nhận nhiệm vụ: Bảo vệ Làng Văn Lang"
	_show_quest_notification(accept_message)

	# Bắt đầu nhiệm vụ trò chuyện với Vua Hùng
	QuestSystem.start_quest(TALK_QUEST_ID)
	QuestSystem.update_quest_progress(TALK_QUEST_ID, 1)  # Hoàn thành ngay lập tức

	# Bắt đầu nhiệm vụ bảo vệ làng
	QuestSystem.start_quest(DEFEND_QUEST_ID)

	# Bắt đầu cuộc tấn công của kẻ thù
	print("Attempting to start enemy spawner...")

	# Tìm enemy spawner bằng nhiều cách
	_find_enemy_spawner()

	# Kiểm tra lại và bắt đầu spawner
	if enemy_spawner:
		print("Enemy spawner found: " + str(enemy_spawner))

		# Kiểm tra xem spawner có phương thức start_spawning không
		if enemy_spawner.has_method("start_spawning"):
			print("Starting spawning...")

			# Bắt đầu spawner - các kiểm tra spawn points đã được xử lý trong hàm start_spawning
			enemy_spawner.start_spawning()
			print("Enemy spawner started!")
		else:
			print("ERROR: Enemy spawner does not have start_spawning method!")
			_create_new_spawner()
	else:
		print("ERROR: Enemy spawner not found after all attempts!")
		_create_new_spawner()

	# Hiển thị bảng thông tin nhiệm vụ ở góc trên bên phải
	_show_quest_info_panel()

	# Đóng hộp thoại sau khi đã khởi tạo mọi thứ
	if dialog_box:
		print("Đóng hộp thoại sau khi nhận nhiệm vụ")
		dialog_box.hide_dialog()
		dialog_box = null

	print("Nhiệm vụ bảo vệ làng đã bắt đầu")

# Tìm enemy spawner bằng nhiều cách
func _find_enemy_spawner():
	if enemy_spawner != null and is_instance_valid(enemy_spawner):
		print("Enemy spawner already exists: " + str(enemy_spawner))
		return

	print("Finding enemy spawner...")

	# Cách 1: Thử lấy từ đường dẫn đã cấu hình
	if enemy_spawner_path:
		enemy_spawner = get_node_or_null(enemy_spawner_path)
		print("Tried to get spawner from path: " + str(enemy_spawner_path) + ", result: " + str(enemy_spawner))

	# Cách 2: Thử tìm trong scene
	if enemy_spawner == null:
		print("Trying to find spawner in scene...")
		var found_spawner = get_tree().root.find_child("EnemyWaveSpawner", true, false)
		if found_spawner:
			enemy_spawner = found_spawner
			print("Found spawner in scene: " + str(enemy_spawner))
		else:
			print("No spawner found in scene")

	# Cách 3: Thử tìm theo group
	if enemy_spawner == null:
		print("Trying to find spawner by group...")
		var nodes = get_tree().get_nodes_in_group("enemy_spawners")
		print("Found " + str(nodes.size()) + " nodes in enemy_spawners group")
		if nodes.size() > 0:
			enemy_spawner = nodes[0]
			print("Found spawner in group: " + str(enemy_spawner))
		else:
			print("No spawners found in enemy_spawners group")

	# Cách 4: Tìm trong map hiện tại
	if enemy_spawner == null:
		print("Trying to find spawner in current map...")
		var current_map = get_tree().current_scene
		if current_map:
			var spawner_in_map = current_map.find_child("EnemyWaveSpawner", true, false)
			if spawner_in_map:
				enemy_spawner = spawner_in_map
				print("Found spawner in current map: " + str(enemy_spawner))
			else:
				print("No spawner found in current map")

	# Cách 5: Tìm trực tiếp từ đường dẫn cụ thể
	if enemy_spawner == null:
		print("Trying to find spawner by direct path...")
		var direct_path = "../EnemyWaveSpawner"
		var direct_spawner = get_node_or_null(direct_path)
		if direct_spawner:
			enemy_spawner = direct_spawner
			print("Found spawner by direct path: " + str(enemy_spawner))
		else:
			print("No spawner found by direct path: " + direct_path)

# Tạo spawner mới nếu không tìm thấy
func _create_new_spawner():
	print("Creating new spawner...")
	var EnemyWaveSpawnerScene = load("res://systems/quest/enemy_wave_spawner.tscn")
	if EnemyWaveSpawnerScene:
		print("Successfully loaded EnemyWaveSpawner scene")
		enemy_spawner = EnemyWaveSpawnerScene.instantiate()

		if not enemy_spawner:
			print("ERROR: Failed to instantiate EnemyWaveSpawner")
			return

		print("Successfully instantiated EnemyWaveSpawner")

		# Thêm vào scene
		var current_map = get_tree().current_scene
		if current_map:
			current_map.add_child(enemy_spawner)
			print("Added spawner to current map")
		else:
			get_tree().root.add_child(enemy_spawner)
			print("Added spawner to root")

		# Thêm vào group để dễ tìm kiếm
		if not enemy_spawner.is_in_group("enemy_spawners"):
			enemy_spawner.add_to_group("enemy_spawners")
			print("Added spawner to enemy_spawners group")

		# Tìm player để lấy vị trí tham chiếu
		var player = get_tree().get_first_node_in_group("player")
		if player:
			# Đặt spawner ở vị trí player
			enemy_spawner.global_position = player.global_position
			print("Set spawner position to player position: " + str(player.global_position))

		print("New spawner created, starting spawning...")
		enemy_spawner.start_spawning()
	else:
		print("ERROR: Could not load spawner scene!")

	# Hiển thị bảng thông tin nhiệm vụ ở góc trên bên phải
	_show_quest_info_panel()

	# Đóng hộp thoại sau khi đã khởi tạo mọi thứ
	if dialog_box:
		print("Đóng hộp thoại sau khi nhận nhiệm vụ")
		dialog_box.hide_dialog()
		dialog_box = null

	print("Nhiệm vụ bảo vệ làng đã bắt đầu")

# Hàm để đánh dấu hoàn thành nhiệm vụ đầu tiên
func complete_first_mission():
	mission_state = "completed"
	print("Nhiệm vụ bảo vệ làng đã hoàn thành")

# Xử lý khi nhiệm vụ hoàn thành
func _on_quest_completed(quest_id: String):
	if quest_id == DEFEND_QUEST_ID:
		complete_first_mission()

		# Hiển thị thông báo hoàn thành nhiệm vụ
		var complete_message = "Đã hoàn thành nhiệm vụ: Bảo vệ Làng Văn Lang"
		_show_quest_notification(complete_message)

		# Xóa bảng thông tin nhiệm vụ sau 3 giây
		if is_instance_valid(quest_info_panel):
			var tween = get_tree().create_tween()
			tween.tween_interval(3.0)
			await tween.finished
			# Tìm và xóa canvas layer chứa panel
			var canvas_layer = get_tree().root.get_node_or_null("QuestInfoPanelLayer")
			if canvas_layer:
				canvas_layer.queue_free()
			quest_info_panel = null

# Hiển thị thông báo nhiệm vụ
func _show_quest_notification(message: String) -> void:
	print("Hiển thị thông báo: " + message)

	# Tạo panel chứa thông báo
	var panel = Panel.new()
	panel.size = Vector2(600, 80)
	panel.position = Vector2(340, 100)  # Giữa trên màn hình

	# Tạo style cho panel
	var style = StyleBoxFlat.new()
	style.bg_color = Color(0.1, 0.1, 0.1, 0.9)
	style.border_width_left = 3
	style.border_width_top = 3
	style.border_width_right = 3
	style.border_width_bottom = 3
	style.border_color = Color(1, 0.8, 0.2)
	style.corner_radius_top_left = 10
	style.corner_radius_top_right = 10
	style.corner_radius_bottom_right = 10
	style.corner_radius_bottom_left = 10
	panel.add_theme_stylebox_override("panel", style)

	# Tạo một label hiển thị thông báo
	var message_label = Label.new()
	message_label.text = message
	message_label.position = Vector2(20, 20)
	message_label.size = Vector2(560, 40)

	# Thiết lập font và màu sắc
	var font_settings = LabelSettings.new()
	font_settings.font_size = 24
	font_settings.font_color = Color(1, 0.8, 0.2)  # Màu vàng
	font_settings.outline_size = 2
	font_settings.outline_color = Color(0, 0, 0)
	message_label.label_settings = font_settings
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER

	panel.add_child(message_label)

	# Thêm vào scene
	var canvas_layer = CanvasLayer.new()
	canvas_layer.layer = 100  # Đảm bảo hiển thị trên cùng
	get_tree().root.add_child(canvas_layer)
	canvas_layer.add_child(panel)

	# Animation hiển thị
	panel.modulate.a = 0  # Bắt đầu trong suốt
	var tween = get_tree().create_tween()
	tween.tween_property(panel, "modulate:a", 1.0, 0.5)  # Hiện dần
	tween.tween_interval(3.0)  # Hiển thị trong 3 giây
	tween.tween_property(panel, "modulate:a", 0.0, 0.5)  # Mờ dần

	# Xóa sau khi hiển thị xong
	await tween.finished
	canvas_layer.queue_free()

# Biến lưu trữ tham chiếu đến bảng thông tin nhiệm vụ
var quest_info_panel = null

# Hiển thị bảng thông tin nhiệm vụ ở góc trên bên phải
func _show_quest_info_panel() -> void:
	# Nếu đã có bảng thông tin, không tạo mới
	if is_instance_valid(quest_info_panel):
		return

	# Tạo CanvasLayer để hiển thị UI
	var canvas_layer = CanvasLayer.new()
	canvas_layer.layer = 5
	canvas_layer.name = "QuestInfoPanelLayer"
	get_tree().root.add_child(canvas_layer)

	# Tạo Panel chứa thông tin nhiệm vụ
	var panel = Panel.new()
	panel.name = "QuestInfoPanel"
	panel.size = Vector2(300, 150)
	panel.position = Vector2(980, 0)  # Góc trên bên phải

	# Đảm bảo panel nằm trong màn hình
	var viewport_size = get_viewport().get_visible_rect().size
	if panel.position.x + panel.size.x > viewport_size.x:
		panel.position.x = viewport_size.x - panel.size.x

	# Tạo style cho panel
	var style = StyleBoxFlat.new()
	style.bg_color = Color(0.1, 0.1, 0.1, 0.8)
	style.border_width_left = 2
	style.border_width_top = 2
	style.border_width_right = 2
	style.border_width_bottom = 2
	style.border_color = Color(1, 0.8, 0.2)
	style.corner_radius_top_left = 8
	style.corner_radius_top_right = 8
	style.corner_radius_bottom_right = 8
	style.corner_radius_bottom_left = 8
	panel.add_theme_stylebox_override("panel", style)

	canvas_layer.add_child(panel)

	# Tạo tiêu đề
	var title = Label.new()
	title.text = "Nhiệm vụ hiện tại"
	title.position = Vector2(10, 10)
	title.size = Vector2(280, 30)

	var title_font = LabelSettings.new()
	title_font.font_size = 18
	title_font.font_color = Color(1, 0.8, 0.2)
	title_font.outline_size = 1
	title_font.outline_color = Color(0, 0, 0)
	title.label_settings = title_font

	panel.add_child(title)

	# Tạo nội dung nhiệm vụ
	var content = Label.new()
	content.text = "Bảo vệ Làng Văn Lang\nTiêu diệt: 0/50 kẻ địch"
	content.position = Vector2(20, 50)
	content.size = Vector2(260, 60)
	content.name = "QuestContent"

	var content_font = LabelSettings.new()
	content_font.font_size = 14
	content_font.font_color = Color(1, 1, 1)
	content.label_settings = content_font

	panel.add_child(content)

	# Tạo nút đóng/mở
	var toggle_button = Button.new()
	toggle_button.text = "X"
	toggle_button.position = Vector2(270, 5)
	toggle_button.size = Vector2(25, 25)
	toggle_button.pressed.connect(_toggle_quest_info_panel)

	panel.add_child(toggle_button)

	# Lưu tham chiếu đến panel
	quest_info_panel = panel

	# Kết nối tín hiệu cập nhật tiến độ nhiệm vụ
	QuestSystem.quest_updated.connect(_update_quest_info_panel)

# Cập nhật nội dung bảng thông tin nhiệm vụ
func _update_quest_info_panel(quest_id: String, progress: int, max_progress: int) -> void:
	if quest_id != DEFEND_QUEST_ID or not is_instance_valid(quest_info_panel):
		return

	var content = quest_info_panel.get_node("QuestContent")
	if content:
		content.text = "Bảo vệ Làng Văn Lang\nTiêu diệt: " + str(progress) + "/" + str(max_progress) + " kẻ địch"

# Bật/tắt bảng thông tin nhiệm vụ
func _toggle_quest_info_panel() -> void:
	if not is_instance_valid(quest_info_panel):
		return

	# Lấy tham chiếu đến các thành phần
	var content = quest_info_panel.get_node_or_null("QuestContent")
	var title = quest_info_panel.get_child(0) if quest_info_panel.get_child_count() > 0 else null
	var toggle_button = null

	# Tìm nút toggle - thêm null check
	if quest_info_panel and is_instance_valid(quest_info_panel):
		for child in quest_info_panel.get_children():
			if child is Button:
				toggle_button = child
				break

	if not toggle_button or not content:
		return

	# Kiểm tra trạng thái hiện tại của panel
	if content.visible:
		# Thu gọn panel
		quest_info_panel.size = Vector2(30, 30)
		content.visible = false
		if title:
			title.visible = false

		# Đổi nút đóng thành nút mở
		toggle_button.text = "+"
		toggle_button.position = Vector2(5, 5)
	else:
		# Mở rộng panel
		quest_info_panel.size = Vector2(300, 150)
		content.visible = true
		if title:
			title.visible = true

		# Đổi nút mở thành nút đóng
		toggle_button.text = "X"
		toggle_button.position = Vector2(270, 5)
