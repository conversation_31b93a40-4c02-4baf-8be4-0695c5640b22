# Inventory Manager - <PERSON><PERSON><PERSON><PERSON> <PERSON>ý kho đồ của ngư<PERSON><PERSON> chơi
extends Node

# Dictionary để lưu trữ số lượng vũ khí
var weapon_inventory = {
	"Sword": 0,
	"Spear": 0,
	"arrow": 0,
	"Armor": 0,
	"Pant": 0
}

# Signal để thông báo khi inventory thay đổi
signal inventory_updated(weapon_type: String, new_count: int)

# Thêm vũ khí vào inventory
func add_weapon(weapon_type: String, quantity: int = 1) -> void:
	if weapon_type in weapon_inventory:
		weapon_inventory[weapon_type] += quantity
		print("Added " + str(quantity) + " " + weapon_type + " to inventory. Total: " + str(weapon_inventory[weapon_type]))
		emit_signal("inventory_updated", weapon_type, weapon_inventory[weapon_type])
	else:
		print("WARNING: Unknown weapon type: " + weapon_type)

# L<PERSON>y số lượng vũ khí hiện có
func get_weapon_count(weapon_type: String) -> int:
	return weapon_inventory.get(weapon_type, 0)

# <PERSON><PERSON> dụng vũ khí (gi<PERSON><PERSON> số lượng)
func use_weapon(weapon_type: String, quantity: int = 1) -> bool:
	if weapon_type in weapon_inventory and weapon_inventory[weapon_type] >= quantity:
		weapon_inventory[weapon_type] -= quantity
		print("Used " + str(quantity) + " " + weapon_type + ". Remaining: " + str(weapon_inventory[weapon_type]))
		emit_signal("inventory_updated", weapon_type, weapon_inventory[weapon_type])
		return true
	else:
		print("Not enough " + weapon_type + " in inventory")
		return false

# Lấy toàn bộ inventory
func get_full_inventory() -> Dictionary:
	return weapon_inventory.duplicate()

# Xóa tất cả vũ khí (reset inventory)
func clear_inventory() -> void:
	for weapon_type in weapon_inventory.keys():
		weapon_inventory[weapon_type] = 0
		emit_signal("inventory_updated", weapon_type, 0)
	print("Inventory cleared")

# Kiểm tra xem có vũ khí nào không
func has_any_weapons() -> bool:
	for count in weapon_inventory.values():
		if count > 0:
			return true
	return false

# Lấy tổng số vũ khí
func get_total_weapon_count() -> int:
	var total = 0
	for count in weapon_inventory.values():
		total += count
	return total

# Hiển thị inventory (debug)
func print_inventory() -> void:
	print("=== WEAPON INVENTORY ===")
	for weapon_type in weapon_inventory.keys():
		print(weapon_type + ": " + str(weapon_inventory[weapon_type]))
	print("========================")
