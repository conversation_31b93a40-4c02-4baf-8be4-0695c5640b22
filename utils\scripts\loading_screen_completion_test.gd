# Loading Screen Completion Test - Test the new auto-completion features
extends Node

var test_results: Array = []
var loading_screen_instance: Node = null

func _ready():
	print("🧪 Loading Screen Completion Test initialized")

func test_loading_completion():
	"""Test loading screen completion behavior"""
	print("\n🧪 TESTING LOADING SCREEN COMPLETION")
	print("="*50)
	
	# Test 1: Normal loading completion
	await test_normal_completion()
	
	# Test 2: Teleportation speed enhancement
	await test_teleportation_speed()
	
	# Test 3: Auto-transition functionality
	await test_auto_transition()
	
	# Print results
	print_test_results()

func test_normal_completion():
	"""Test normal loading screen completion"""
	print("\n🔍 Test 1: Normal Loading Completion")
	
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if not loading_scene:
		record_result("Normal Completion", false, "Loading scene not found")
		return
	
	loading_screen_instance = loading_scene.instantiate()
	get_tree().root.add_child(loading_screen_instance)
	
	# Configure for normal mode
	if loading_screen_instance.has_method("set_teleportation_mode"):
		loading_screen_instance.set_teleportation_mode(false)
		loading_screen_instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
		loading_screen_instance.set_auto_transition(false)  # Manual control for testing
		
		print("✅ Loading screen configured for normal mode")
		
		# Test progress updates
		var start_time = Time.get_ticks_msec()
		
		for i in range(0, 101, 25):
			loading_screen_instance.update_progress_enhanced(i, "Testing %d%%" % i)
			await get_tree().create_timer(0.2).timeout
		
		var end_time = Time.get_ticks_msec()
		var duration = (end_time - start_time) / 1000.0
		
		print("⏱️ Normal loading took: %.2f seconds" % duration)
		record_result("Normal Completion", true, "Completed in %.2f seconds" % duration)
	else:
		record_result("Normal Completion", false, "Enhanced methods not available")
	
	# Cleanup
	if loading_screen_instance:
		loading_screen_instance.queue_free()
		loading_screen_instance = null

func test_teleportation_speed():
	"""Test teleportation speed enhancement"""
	print("\n⚡ Test 2: Teleportation Speed Enhancement")
	
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if not loading_scene:
		record_result("Teleportation Speed", false, "Loading scene not found")
		return
	
	loading_screen_instance = loading_scene.instantiate()
	get_tree().root.add_child(loading_screen_instance)
	
	# Configure for teleportation mode
	if loading_screen_instance.has_method("set_teleportation_mode"):
		loading_screen_instance.set_teleportation_mode(true)
		loading_screen_instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
		loading_screen_instance.set_auto_transition(false)  # Manual control for testing
		
		print("✅ Loading screen configured for teleportation mode")
		
		# Test progress updates
		var start_time = Time.get_ticks_msec()
		
		for i in range(0, 101, 25):
			loading_screen_instance.update_progress_enhanced(i, "Teleporting %d%%" % i)
			await get_tree().create_timer(0.1).timeout  # Faster updates for teleportation
		
		var end_time = Time.get_ticks_msec()
		var duration = (end_time - start_time) / 1000.0
		
		print("⚡ Teleportation loading took: %.2f seconds" % duration)
		
		# Check if it's actually faster (should be ~1.5x faster)
		var expected_max = 1.0  # Should be under 1 second for teleportation
		if duration < expected_max:
			record_result("Teleportation Speed", true, "Fast loading: %.2f seconds" % duration)
		else:
			record_result("Teleportation Speed", false, "Too slow: %.2f seconds" % duration)
	else:
		record_result("Teleportation Speed", false, "Enhanced methods not available")
	
	# Cleanup
	if loading_screen_instance:
		loading_screen_instance.queue_free()
		loading_screen_instance = null

func test_auto_transition():
	"""Test automatic transition functionality"""
	print("\n🔄 Test 3: Auto-Transition Functionality")
	
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if not loading_scene:
		record_result("Auto-Transition", false, "Loading scene not found")
		return
	
	loading_screen_instance = loading_scene.instantiate()
	get_tree().root.add_child(loading_screen_instance)
	
	# Configure for auto-transition
	if loading_screen_instance.has_method("set_auto_transition"):
		loading_screen_instance.set_auto_transition(true)
		loading_screen_instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
		
		print("✅ Loading screen configured for auto-transition")
		
		# Monitor for scene change
		var original_scene = get_tree().current_scene
		var start_time = Time.get_ticks_msec()
		
		# Update to 100% and wait for auto-transition
		loading_screen_instance.update_progress_enhanced(100.0, "Completing...")
		
		# Wait up to 5 seconds for transition
		var timeout = 5.0
		var elapsed = 0.0
		var transition_detected = false
		
		while elapsed < timeout:
			await get_tree().create_timer(0.1).timeout
			elapsed += 0.1
			
			# Check if scene changed or loading screen was removed
			var current_scene = get_tree().current_scene
			var loading_still_exists = is_instance_valid(loading_screen_instance) and loading_screen_instance.get_parent()
			
			if current_scene != original_scene or not loading_still_exists:
				transition_detected = true
				break
		
		var end_time = Time.get_ticks_msec()
		var duration = (end_time - start_time) / 1000.0
		
		if transition_detected:
			print("✅ Auto-transition completed in %.2f seconds" % duration)
			record_result("Auto-Transition", true, "Transitioned in %.2f seconds" % duration)
		else:
			print("❌ Auto-transition failed - no transition detected")
			record_result("Auto-Transition", false, "No transition after %.2f seconds" % duration)
	else:
		record_result("Auto-Transition", false, "Auto-transition methods not available")
	
	# Cleanup
	if loading_screen_instance and is_instance_valid(loading_screen_instance):
		loading_screen_instance.queue_free()

func record_result(test_name: String, passed: bool, details: String):
	"""Record test result"""
	test_results.append({
		"name": test_name,
		"passed": passed,
		"details": details
	})

func print_test_results():
	"""Print all test results"""
	print("\n" + "="*50)
	print("📊 LOADING SCREEN COMPLETION TEST RESULTS")
	print("="*50)
	
	var passed_count = 0
	var total_count = test_results.size()
	
	for result in test_results:
		var status = "✅ PASS" if result.passed else "❌ FAIL"
		print("%s - %s: %s" % [status, result.name, result.details])
		if result.passed:
			passed_count += 1
	
	print("\n📈 SUMMARY: %d/%d tests passed" % [passed_count, total_count])
	
	if passed_count == total_count:
		print("🎉 ALL TESTS PASSED! Loading screen completion is working correctly.")
	else:
		print("⚠️ Some tests failed. Check the implementation.")

# Manual test functions
func test_quick_completion():
	"""Quick test for loading screen completion"""
	print("🚀 Running quick completion test...")
	
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if loading_scene:
		var instance = loading_scene.instantiate()
		get_tree().root.add_child(instance)
		
		if instance.has_method("set_auto_transition"):
			instance.set_auto_transition(true)
			instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
			instance.update_progress_enhanced(100.0, "Quick test!")
			print("✅ Quick test initiated")
		else:
			print("❌ Enhanced methods not available")
			instance.queue_free()
	else:
		print("❌ Loading scene not found")

func test_teleport_speed():
	"""Quick test for teleportation speed"""
	print("⚡ Running teleportation speed test...")
	
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if loading_scene:
		var instance = loading_scene.instantiate()
		get_tree().root.add_child(instance)
		
		if instance.has_method("set_teleportation_mode"):
			instance.set_teleportation_mode(true)
			instance.set_auto_transition(true)
			instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
			instance.update_progress_enhanced(100.0, "Fast teleport!")
			print("⚡ Teleportation speed test initiated")
		else:
			print("❌ Enhanced methods not available")
			instance.queue_free()
	else:
		print("❌ Loading scene not found")
