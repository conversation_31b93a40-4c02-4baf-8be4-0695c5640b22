[gd_scene load_steps=5 format=3 uid="uid://cytofbyao3jum"]

[ext_resource type="Script" path="res://animals/scripts/animal.gd" id="1_mo2jl"]
[ext_resource type="SpriteFrames" uid="uid://cssgn2yjxnicb" path="res://animals/sprites/Snail.tres" id="2_yilqq"]
[ext_resource type="Shape2D" uid="uid://4ifyps2pyfla" path="res://animals/collisions/snail.tres" id="3_1tivw"]
[ext_resource type="Script" path="res://animals/scripts/animal_class_manager.gd" id="4_g0g0l"]

[node name="Animal" type="CharacterBody2D"]
collision_layer = 2
collision_mask = 405
script = ExtResource("1_mo2jl")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = ExtResource("2_yilqq")
animation = &"walk"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0, 3)
shape = ExtResource("3_1tivw")

[node name="AnimalManager" type="Node" parent="."]
script = ExtResource("4_g0g0l")

[node name="Node" type="Node" parent="."]
