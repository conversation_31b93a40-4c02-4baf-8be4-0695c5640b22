extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var tab_container: HBoxContainer = $Panel/All_Tab_Button
@onready var nhiem_vu_button: TextureButton = $Panel/All_Tab_Button/Mission_Button
@onready var hanh_trang_button: TextureButton = $Panel/All_Tab_Button/Inventory_Button
@onready var ky_nang_button: TextureButton = $Panel/All_Tab_Button/Skills_Button
@onready var khac_button: TextureButton = $Panel/All_Tab_Button/Etc_Button
@onready var close_button: TextureButton = $Panel/Close_Button

func _ready() -> void:
	if not tab_container:
		push_error("HBoxContainer not found at path: Panel/All_Tab_Button")
		return
	if not nhiem_vu_button:
		push_error("Mission_Button not found at path: Panel/All_Tab_Button/Mission_Button")
		return
	if not hanh_trang_button:
		push_error("Inventory_Button not found at path: Panel/All_Tab_Button/Inventory_Button")
		return
	if not ky_nang_button:
		push_error("Skills_Button not found at path: Panel/All_Tab_Button/Skills_Button")
		return
	if not khac_button:
		push_error("Etc_Button not found at path: Panel/All_Tab_Button/Etc_Button")
		return
	if not close_button:
		push_error("Close_Button not found at path: Panel/Close_Button")
		return

	nhiem_vu_button.pressed.connect(_on_nhiem_vu_button_pressed)
	hanh_trang_button.pressed.connect(_on_hanh_trang_button_pressed)
	ky_nang_button.pressed.connect(_on_ky_nang_button_pressed)
	khac_button.pressed.connect(_on_khac_button_pressed)
	close_button.pressed.connect(_on_close_button_pressed)

func _on_khac_button_pressed() -> void:
	pass

func _on_hanh_trang_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/inventory_tab.tscn")

func _on_nhiem_vu_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/missions_tab.tscn")

func _on_ky_nang_button_pressed() -> void:
	get_tree().change_scene_to_file("res://hud_progress/scenes/skills_tab.tscn")

func _on_close_button_pressed() -> void:
	queue_free()
