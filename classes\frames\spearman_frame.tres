[gd_resource type="SpriteFrames" load_steps=58 format=3 uid="uid://cxkkmyos44qi0"]

[ext_resource type="Texture2D" uid="uid://b4trbc60s036p" path="res://assets/images/characters/players/sheets/weapons/chuyen_dong_cam_thuong_nvc.png" id="1_vkxol"]
[ext_resource type="Texture2D" uid="uid://bgh7kd1ki8soq" path="res://assets/images/characters/players/sheets/weapons/tan_cong_thuong_nvc.png" id="2_dvj5j"]

[sub_resource type="AtlasTexture" id="AtlasTexture_344q8"]
atlas = ExtResource("1_vkxol")
region = Rect2(0, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_p124i"]
atlas = ExtResource("1_vkxol")
region = Rect2(68, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_o43qv"]
atlas = ExtResource("1_vkxol")
region = Rect2(136, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6uwyl"]
atlas = ExtResource("1_vkxol")
region = Rect2(204, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6alwm"]
atlas = ExtResource("1_vkxol")
region = Rect2(272, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_fe1lf"]
atlas = ExtResource("1_vkxol")
region = Rect2(340, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bqcp4"]
atlas = ExtResource("1_vkxol")
region = Rect2(408, 476, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_m61qg"]
atlas = ExtResource("1_vkxol")
region = Rect2(0, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_colso"]
atlas = ExtResource("1_vkxol")
region = Rect2(68, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_paqqj"]
atlas = ExtResource("1_vkxol")
region = Rect2(136, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_ri6e6"]
atlas = ExtResource("1_vkxol")
region = Rect2(204, 408, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_fpn3m"]
atlas = ExtResource("1_vkxol")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qxu58"]
atlas = ExtResource("1_vkxol")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_85ugm"]
atlas = ExtResource("1_vkxol")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xuohn"]
atlas = ExtResource("1_vkxol")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xfp75"]
atlas = ExtResource("1_vkxol")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_uh56v"]
atlas = ExtResource("1_vkxol")
region = Rect2(340, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_scc74"]
atlas = ExtResource("1_vkxol")
region = Rect2(0, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_74hl4"]
atlas = ExtResource("1_vkxol")
region = Rect2(68, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_bo50c"]
atlas = ExtResource("1_vkxol")
region = Rect2(136, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_mve3r"]
atlas = ExtResource("1_vkxol")
region = Rect2(204, 272, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_cgllh"]
atlas = ExtResource("1_vkxol")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_5fqep"]
atlas = ExtResource("1_vkxol")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_eb7bd"]
atlas = ExtResource("1_vkxol")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_cb1fh"]
atlas = ExtResource("1_vkxol")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_w1rg1"]
atlas = ExtResource("1_vkxol")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_17wts"]
atlas = ExtResource("1_vkxol")
region = Rect2(340, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_712q4"]
atlas = ExtResource("2_dvj5j")
region = Rect2(0, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8m3bl"]
atlas = ExtResource("2_dvj5j")
region = Rect2(68, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_58obp"]
atlas = ExtResource("2_dvj5j")
region = Rect2(136, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_pnnwl"]
atlas = ExtResource("2_dvj5j")
region = Rect2(204, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_o6rsg"]
atlas = ExtResource("2_dvj5j")
region = Rect2(272, 0, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ax00"]
atlas = ExtResource("2_dvj5j")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_gcxuh"]
atlas = ExtResource("2_dvj5j")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_b6si1"]
atlas = ExtResource("2_dvj5j")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_j3cx4"]
atlas = ExtResource("2_dvj5j")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_kuxx6"]
atlas = ExtResource("2_dvj5j")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0ugev"]
atlas = ExtResource("2_dvj5j")
region = Rect2(340, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_0dm5s"]
atlas = ExtResource("2_dvj5j")
region = Rect2(0, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qnh1r"]
atlas = ExtResource("2_dvj5j")
region = Rect2(68, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_hxykc"]
atlas = ExtResource("2_dvj5j")
region = Rect2(136, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_6kp8v"]
atlas = ExtResource("2_dvj5j")
region = Rect2(204, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_q660d"]
atlas = ExtResource("2_dvj5j")
region = Rect2(272, 136, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_iemdl"]
atlas = ExtResource("2_dvj5j")
region = Rect2(0, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_pd673"]
atlas = ExtResource("2_dvj5j")
region = Rect2(68, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_n82xw"]
atlas = ExtResource("2_dvj5j")
region = Rect2(136, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_8gq7w"]
atlas = ExtResource("2_dvj5j")
region = Rect2(204, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_va872"]
atlas = ExtResource("2_dvj5j")
region = Rect2(272, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_cqly2"]
atlas = ExtResource("2_dvj5j")
region = Rect2(340, 204, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_07ssf"]
atlas = ExtResource("1_vkxol")
region = Rect2(0, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_xsik6"]
atlas = ExtResource("1_vkxol")
region = Rect2(68, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_qowbb"]
atlas = ExtResource("1_vkxol")
region = Rect2(136, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_sslkw"]
atlas = ExtResource("1_vkxol")
region = Rect2(204, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_peyvc"]
atlas = ExtResource("1_vkxol")
region = Rect2(272, 68, 68, 68)

[sub_resource type="AtlasTexture" id="AtlasTexture_q7gvx"]
atlas = ExtResource("1_vkxol")
region = Rect2(340, 68, 68, 68)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_344q8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p124i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o43qv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6uwyl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6alwm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fe1lf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bqcp4")
}],
"loop": false,
"name": &"die",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_m61qg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_colso")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_paqqj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ri6e6")
}],
"loop": false,
"name": &"hurt",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fpn3m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qxu58")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_85ugm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xuohn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xfp75")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uh56v")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_scc74")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_74hl4")
}],
"loop": true,
"name": &"jump_end",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_bo50c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mve3r")
}],
"loop": true,
"name": &"jump_start",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_cgllh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5fqep")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_eb7bd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cb1fh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w1rg1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_17wts")
}],
"loop": true,
"name": &"running",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_712q4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8m3bl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_58obp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pnnwl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o6rsg")
}],
"loop": false,
"name": &"thrust_1",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ax00")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gcxuh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b6si1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j3cx4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kuxx6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0ugev")
}],
"loop": false,
"name": &"thrust_2",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_0dm5s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qnh1r")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hxykc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6kp8v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q660d")
}],
"loop": false,
"name": &"thrust_3",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_iemdl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pd673")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n82xw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8gq7w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_va872")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cqly2")
}],
"loop": false,
"name": &"thrust_4",
"speed": 15.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_07ssf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xsik6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qowbb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sslkw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_peyvc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q7gvx")
}],
"loop": true,
"name": &"walking",
"speed": 10.0
}]
