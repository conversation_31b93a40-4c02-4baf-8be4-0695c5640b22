[gd_scene load_steps=8 format=3 uid="uid://7ohdh0u4q5rd"]

[ext_resource type="Script" path="res://allies/scripts/ally.gd" id="1_6ecqm"]
[ext_resource type="SpriteFrames" uid="uid://71qhu1bl14gi" path="res://allies/skins/spear_ally.tres" id="1_c6ssp"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_e0iq1"]
radius = 7.0
height = 18.0

[sub_resource type="CircleShape2D" id="CircleShape2D_p3jtc"]
radius = 86.0233

[sub_resource type="CircleShape2D" id="CircleShape2D_25jno"]
radius = 42.0119

[sub_resource type="CircleShape2D" id="CircleShape2D_wmf45"]
radius = 200.0

[sub_resource type="CircleShape2D" id="CircleShape2D_tajrn"]
radius = 12.0

[node name="Ally" type="CharacterBody2D" groups=["ally_group"]]
collision_layer = 128
collision_mask = 147
script = ExtResource("1_6ecqm")
health = null
peak_duration = null
damage = null
speed = null
acceleration = null
follow_distance = null
attack_range = null
attack_cooldown = null
sprites = null

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
sprite_frames = ExtResource("1_c6ssp")
animation = &"slash_1"
autoplay = "idle"
frame = 2
frame_progress = 0.625434

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0, 1)
shape = SubResource("CapsuleShape2D_e0iq1")

[node name="TrackingZone" type="Area2D" parent="."]
collision_layer = 128

[node name="CollisionShape2D" type="CollisionShape2D" parent="TrackingZone"]
shape = SubResource("CircleShape2D_p3jtc")

[node name="StopTrackingZone" type="Area2D" parent="."]
collision_layer = 128

[node name="CollisionShape2D" type="CollisionShape2D" parent="StopTrackingZone"]
shape = SubResource("CircleShape2D_25jno")

[node name="DetectZone" type="Area2D" parent="."]
collision_layer = 128
collision_mask = 2

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectZone"]
shape = SubResource("CircleShape2D_wmf45")

[node name="Hitbox" type="Area2D" parent="."]
collision_layer = 512
collision_mask = 2

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
position = Vector2(15, 4)
shape = SubResource("CircleShape2D_tajrn")
disabled = true
