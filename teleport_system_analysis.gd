# Comprehensive Teleport System Analysis
extends Node

var teleport_issues = {}
var route_analysis = {}

func _ready():
	print("🔍 COMPREHENSIVE TELEPORT SYSTEM ANALYSIS")
	print("=" * 50)
	call_deferred("analyze_all_issues")

func analyze_all_issues():
	print("\n📊 ANALYZING ALL TELEPORT ROUTES AND ISSUES...")
	
	# Analyze discrepancies between systems
	analyze_system_discrepancies()
	
	# Check specific route issues
	analyze_route_issues()
	
	# Check map controller consistency
	analyze_map_controller_issues()
	
	# Generate comprehensive report
	generate_comprehensive_report()

func analyze_system_discrepancies():
	print("\n🔍 ANALYZING SYSTEM DISCREPANCIES...")
	
	# Gate scene file positions vs Position Mapping System
	var gate_positions = {
		"rung_nuong_to_dong_dau": Vector2(5200, -350),      # From TeleportGate_RungNuong.tscn
		"dong_dau_to_rung_nuong": Vector2(-200, -1300),     # From TeleportGate_DongDau.tscn
		"rung_nuong_to_lang_van_lang": Vector2(2000, -1900), # From TeleportGate_RungNuong_LangVanLang.tscn
		"rung_nuong_to_hang_an": Vector2(300, -1900),       # From TeleportGate_RungNuong_HangAn.tscn
		"rung_nuong_to_suoi_thieng": Vector2(-2000, 200),   # From TeleportGate_RungNuong_SuoiThieng.tscn
		"hang_an_to_rung_nuong": Vector2(2000, -1200),      # From TeleportGate_HangAn.tscn
		"hang_an_to_lang_van_lang": Vector2(3700, -1900),   # From TeleportGate_HangAn_LangVanLang.tscn
		"dong_dau_to_lang_van_lang": Vector2(300, -1900),   # From TeleportGate_DongDau_LangVanLang.tscn
		"dong_dau_to_doi_tre": Vector2(-2292, -538),        # From TeleportGate_DongDau_DoiTre.tscn
		"doi_tre_to_dong_dau": Vector2(-1400, -400),        # From TeleportGate_DoiTre.tscn
		"lang_van_lang_to_doi_tre": Vector2(-2292, -538),   # From TeleportGate_LangVanLang_DoiTre.tscn
		"lang_van_lang_to_hang_an": Vector2(300, -1900),    # From TeleportGate_LangVanLang_HangAn.tscn
		"lang_van_lang_to_rung_nuong": Vector2(750, -1200)  # From TeleportGate_LangVanLang_RungNuong.tscn
	}
	
	# Position Mapping System positions
	var mapping_positions = {
		"lang_van_lang_to_rung_nuong": Vector2(753, -1225),
		"lang_van_lang_to_dong_dau": Vector2(-2444, -299),
		"lang_van_lang_to_hang_an": Vector2(-2069, 484),
		"lang_van_lang_to_suoi_thieng": Vector2(-2069, 484),
		"lang_van_lang_to_doi_tre": Vector2(-2818, -219),
		"rung_nuong_to_lang_van_lang": Vector2(2000, -1900),
		"rung_nuong_to_dong_dau": Vector2(5328, -320),
		"rung_nuong_to_hang_an": Vector2(300, -1900),
		"rung_nuong_to_suoi_thieng": Vector2(-2000, 200),
		"rung_nuong_to_doi_tre": Vector2(-2292, -538),
		"dong_dau_to_lang_van_lang": Vector2(-2444, -299),
		"dong_dau_to_rung_nuong": Vector2(753, -1225),
		"dong_dau_to_hang_an": Vector2(-2069, 484),
		"dong_dau_to_suoi_thieng": Vector2(-2069, 484),
		"dong_dau_to_doi_tre": Vector2(-2292, -538),
		"hang_an_to_lang_van_lang": Vector2(3700, -1900),
		"hang_an_to_rung_nuong": Vector2(753, -1225),
		"hang_an_to_dong_dau": Vector2(-1421, -429),
		"hang_an_to_suoi_thieng": Vector2(-2069, 484),
		"hang_an_to_doi_tre": Vector2(-2292, -538),
		"suoi_thieng_to_lang_van_lang": Vector2(300, -1900),
		"suoi_thieng_to_rung_nuong": Vector2(753, -1225),
		"suoi_thieng_to_dong_dau": Vector2(-1421, -429),
		"suoi_thieng_to_hang_an": Vector2(-2069, 484),
		"suoi_thieng_to_doi_tre": Vector2(-2292, -538),
		"doi_tre_to_lang_van_lang": Vector2(1100, -1900),
		"doi_tre_to_rung_nuong": Vector2(753, -1225),
		"doi_tre_to_dong_dau": Vector2(-2500, -435),
		"doi_tre_to_hang_an": Vector2(-2069, 484),
		"doi_tre_to_suoi_thieng": Vector2(-2069, 484)
	}
	
	print("🔍 Comparing Gate Scene Files vs Position Mapping System:")
	
	for route in gate_positions:
		var gate_pos = gate_positions[route]
		var mapping_pos = mapping_positions.get(route, Vector2.ZERO)
		
		if mapping_pos != Vector2.ZERO:
			var matches = gate_pos == mapping_pos
			if not matches:
				teleport_issues[route] = {
					"type": "position_mismatch",
					"gate_position": gate_pos,
					"mapping_position": mapping_pos,
					"difference": gate_pos.distance_to(mapping_pos)
				}
				print("❌ %s: Gate=%s vs Mapping=%s (diff: %.1f)" % [route, gate_pos, mapping_pos, gate_pos.distance_to(mapping_pos)])
			else:
				print("✅ %s: Positions match" % route)
		else:
			teleport_issues[route] = {
				"type": "missing_mapping",
				"gate_position": gate_pos
			}
			print("⚠️ %s: No mapping found for gate position %s" % [route, gate_pos])

func analyze_route_issues():
	print("\n🗺️ ANALYZING SPECIFIC ROUTE ISSUES...")
	
	# Check if spawn positions are near actual teleport gates in destination maps
	var map_gate_positions = {
		"dong_dau": {
			"rung_nuong": Vector2(5328, -320),      # Position of rung_nuong gate in dong_dau
			"doi_tre": Vector2(-2500, -435),        # Position of doi_tre gate in dong_dau
			"lang_van_lang": Vector2(-2122, -296)   # Position of lang_van_lang gate in dong_dau
		},
		"rung_nuong": {
			"dong_dau": Vector2(-584, -1350),       # Position of dong_dau gate in rung_nuong
			"hang_an": Vector2(2003, -1343),        # Position of hang_an gate in rung_nuong
			"lang_van_lang": Vector2(1500, -1300),  # Estimated position
			"suoi_thieng": Vector2(800, -1300)      # Estimated position
		},
		"lang_van_lang": {
			"dong_dau": Vector2(300, -1900),        # Left gate position
			"hang_an": Vector2(3700, -1900),        # Right gate position
			"rung_nuong": Vector2(2000, -1900),     # Center position
			"doi_tre": Vector2(1100, -1900),        # Estimated position
			"suoi_thieng": Vector2(2500, -1900)     # Estimated position
		},
		"hang_an": {
			"rung_nuong": Vector2(-3800, 400),      # Position of rung_nuong gate in hang_an
			"lang_van_lang": Vector2(-3000, 400)    # Estimated position
		}
	}
	
	print("🎯 Checking if spawn positions are near destination gates:")
	
	# Check major routes
	var critical_routes = [
		{"route": "rung_nuong_to_dong_dau", "spawn": Vector2(5200, -350), "map": "dong_dau", "gate": "rung_nuong"},
		{"route": "dong_dau_to_rung_nuong", "spawn": Vector2(-200, -1300), "map": "rung_nuong", "gate": "dong_dau"},
		{"route": "lang_van_lang_to_dong_dau", "spawn": Vector2(-2444, -299), "map": "dong_dau", "gate": "lang_van_lang"},
		{"route": "dong_dau_to_lang_van_lang", "spawn": Vector2(300, -1900), "map": "lang_van_lang", "gate": "dong_dau"}
	]
	
	for route_data in critical_routes:
		var route = route_data.route
		var spawn_pos = route_data.spawn
		var dest_map = route_data.map
		var gate_name = route_data.gate
		
		if map_gate_positions.has(dest_map) and map_gate_positions[dest_map].has(gate_name):
			var gate_pos = map_gate_positions[dest_map][gate_name]
			var distance = spawn_pos.distance_to(gate_pos)
			
			if distance > 200:  # If spawn is more than 200 units from gate
				route_analysis[route] = {
					"issue": "spawn_too_far_from_gate",
					"spawn_position": spawn_pos,
					"gate_position": gate_pos,
					"distance": distance
				}
				print("❌ %s: Spawn too far from gate (%.1f units)" % [route, distance])
			else:
				print("✅ %s: Spawn near gate (%.1f units)" % [route, distance])

func analyze_map_controller_issues():
	print("\n🎮 ANALYZING MAP CONTROLLER ISSUES...")
	
	# Check if all map controllers have consistent spawn handling
	var controller_issues = []
	
	# Check for validation logic that might reset positions
	print("🔍 Checking for problematic validation logic:")
	print("✅ dong_dau: Fixed bounds-based validation")
	print("⚠️ Other maps: Need to check for similar distance-based validation issues")
	
	# Check default positions
	var default_positions = {
		"lang_van_lang": Vector2(300, -1900),
		"rung_nuong": Vector2(753, -1225),
		"dong_dau": Vector2(-1421, -429),
		"hang_an": Vector2(-2069, 484),
		"suoi_thieng": Vector2(-2069, 484),
		"doi_tre": Vector2(-2292, -538)
	}
	
	print("📍 Default positions in map controllers:")
	for map_name in default_positions:
		print("   %s: %s" % [map_name, default_positions[map_name]])

func generate_comprehensive_report():
	print("\n📊 COMPREHENSIVE TELEPORT SYSTEM REPORT")
	print("=" * 50)
	
	print("\n🚨 CRITICAL ISSUES FOUND:")
	print("1. Position Mismatches: %d routes" % teleport_issues.size())
	print("2. Route Issues: %d routes" % route_analysis.size())
	
	print("\n❌ POSITION MISMATCHES:")
	for route in teleport_issues:
		var issue = teleport_issues[route]
		if issue.type == "position_mismatch":
			print("   %s: Gate=%s vs Mapping=%s (diff: %.1f)" % [
				route, issue.gate_position, issue.mapping_position, issue.difference
			])
		elif issue.type == "missing_mapping":
			print("   %s: Missing mapping for gate position %s" % [route, issue.gate_position])
	
	print("\n🎯 SPAWN ACCURACY ISSUES:")
	for route in route_analysis:
		var issue = route_analysis[route]
		print("   %s: %s (distance: %.1f)" % [route, issue.issue, issue.distance])
	
	print("\n🔧 RECOMMENDED FIXES:")
	print("1. Unify position sources - use gate scene files as single source of truth")
	print("2. Update teleport_position_mapping.gd to match gate scene files")
	print("3. Fix spawn positions that are too far from destination gates")
	print("4. Check all map controllers for validation logic issues")
	print("5. Ensure SceneManager integration works consistently")
	
	print("\n📋 NEXT STEPS:")
	print("F1 - Re-run analysis")
	print("F2 - Generate position fixes")
	print("F3 - Test specific routes")

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				analyze_all_issues()
			KEY_F2:
				generate_position_fixes()
			KEY_F3:
				test_specific_routes()

func generate_position_fixes():
	print("\n🔧 GENERATING POSITION FIXES...")
	print("This will be implemented in the comprehensive fix phase.")

func test_specific_routes():
	print("\n🧪 TESTING SPECIFIC ROUTES...")
	print("This will be implemented in the testing phase.")
