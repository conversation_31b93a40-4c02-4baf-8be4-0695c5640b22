[gd_scene load_steps=5 format=3 uid="uid://cf8kby3gw31o1"]

[ext_resource type="Script" path="res://npcs/scripts/vua_hung_npc.gd" id="1_mp1jl"]
[ext_resource type="SpriteFrames" uid="uid://d1r8rbljif8hn" path="res://allies/skins/vua_hung.tres" id="2_bs2gb"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_bl32f"]
radius = 28.0
height = 74.0

[sub_resource type="LabelSettings" id="LabelSettings_yvnqt"]
font_size = 24
outline_size = 4
outline_color = Color(0, 0, 0, 1)

[node name="VuaHung" type="CharacterBody2D"]
scale = Vector2(0.25, 0.25)
collision_layer = 64
collision_mask = 16
script = ExtResource("1_mp1jl")
enemy_spawner_path = NodePath("../EnemyWaveSpawner")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
position = Vector2(4, -24)
scale = Vector2(1.43856, 1.43856)
sprite_frames = ExtResource("2_bs2gb")
animation = &"idle"
autoplay = "idle"
frame_progress = 0.966919
flip_h = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_bl32f")

[node name="ClickableButton" type="Button" parent="."]
offset_left = -30.0
offset_top = -80.0
offset_right = 30.0
offset_bottom = 40.0
flat = true
icon_alignment = 1

[node name="NameLabel" type="Label" parent="."]
offset_left = -100.0
offset_top = -120.0
offset_right = 100.0
offset_bottom = -80.0
text = "Vua Hùng"
label_settings = SubResource("LabelSettings_yvnqt")
horizontal_alignment = 1
vertical_alignment = 1
