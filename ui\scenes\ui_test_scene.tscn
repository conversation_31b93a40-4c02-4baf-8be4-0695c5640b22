[gd_scene load_steps=2 format=3 uid="uid://bqm8xvn7qkqxr"]

[ext_resource type="Script" path="res://ui/scripts/ui_improvements_test.gd" id="1_test"]

[node name="UITestScene" type="Node2D"]
script = ExtResource("1_test")

[node name="Background" type="ColorRect" parent="."]
offset_right = 1280.0
offset_bottom = 720.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="TestLabel" type="Label" parent="."]
offset_left = 50.0
offset_top = 50.0
offset_right = 1230.0
offset_bottom = 150.0
theme_override_font_sizes/font_size = 32
theme_override_colors/font_color = Color(1, 0.9, 0.3, 1)
text = "UI Improvements Test Suite
Press H for help, R to restart tests"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TestResults" type="RichTextLabel" parent="."]
offset_left = 50.0
offset_top = 200.0
offset_right = 1230.0
offset_bottom = 670.0
theme_override_font_sizes/normal_font_size = 14
theme_override_colors/default_color = Color(1, 1, 1, 1)
bbcode_enabled = true
text = "Test results will appear here..."
scroll_following = true
