extends Node

var player: Player = null
var mission_order = ["<PERSON>easant", "Swordman", "Spear<PERSON>", "<PERSON>"]
var available_mission: Dictionary = {
	"Peasant": preload("res://classes/resources/peasant_resource.tres"),
	"Swordman": preload("res://classes/resources/swordman_resource.tres"),
	"Spearman": preload("res://classes/resources/spearman_resource.tres"),
	"Bowman": preload("res://classes/resources/bowman_resource.tres")
}

func set_up_player(player_custom: Player) -> void:
	player = player_custom

	var mission = get_mission_information(player.devine_mission)

	# Kiểm tra nếu không tìm thấy thông tin mission
	if mission == null:
		# Sử dụng mission mặc định (Peasant)
		mission = get_mission_information("Peasant")

		# Nếu vẫn không tìm thấy, thoát
		if mission == null:
			return

	player.adapt_to_mission(mission)

func switch_mission():
	var next_mission_title = get_next_mission(player.devine_mission)

	# <PERSON><PERSON><PERSON> tra nếu không có mission tiếp theo
	if next_mission_title == null:
		return

	var next_mission = get_mission_information(next_mission_title)

	# Ki<PERSON><PERSON> tra nếu không tìm thấy thông tin mission
	if next_mission == null:
		return

	player.adapt_to_mission(next_mission)

func get_next_mission(current_mission: String):
	var index = mission_order.find(current_mission)
	if index == -1:
		return null  # no next rank
	elif index == mission_order.size() - 1:
		return mission_order[0]
	else:
		return mission_order[index + 1]

func get_mission_information(mission_name: String) -> ClassDefinition:
	return available_mission.get(mission_name)
