[gd_resource type="SpriteFrames" load_steps=10 format=3 uid="uid://cqffh68kyx6by"]

[ext_resource type="Texture2D" uid="uid://cg1l53ayfh73x" path="res://assets/images/characters/npcs/tho_ren.png" id="1_fmap4"]

[sub_resource type="AtlasTexture" id="AtlasTexture_1mii2"]
atlas = ExtResource("1_fmap4")
region = Rect2(0, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_mdksx"]
atlas = ExtResource("1_fmap4")
region = Rect2(129, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_qw1kj"]
atlas = ExtResource("1_fmap4")
region = Rect2(258, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_che0a"]
atlas = ExtResource("1_fmap4")
region = Rect2(387, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_f5rno"]
atlas = ExtResource("1_fmap4")
region = Rect2(0, 130, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_xi8d3"]
atlas = ExtResource("1_fmap4")
region = Rect2(129, 130, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_uudhq"]
atlas = ExtResource("1_fmap4")
region = Rect2(258, 130, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_j5hio"]
atlas = ExtResource("1_fmap4")
region = Rect2(387, 130, 129, 130)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1mii2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mdksx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qw1kj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_che0a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f5rno")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xi8d3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uudhq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j5hio")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}]
