# Loading Screen Test - Test the loading screen fixes
extends Node

func _ready():
	print("🧪 Loading Screen Test initialized")

func test_loading_screen_completion():
	"""Test loading screen completion functionality"""
	print("\n🧪 TESTING LOADING SCREEN COMPLETION")
	print("="*50)
	
	# Load the loading screen
	var loading_scene = preload("res://ui/scenes/loading_screen.tscn")
	if not loading_scene:
		print("❌ Loading screen scene not found")
		return
	
	var loading_instance = loading_scene.instantiate()
	get_tree().root.add_child(loading_instance)
	
	print("✅ Loading screen instantiated")
	
	# Wait a moment for initialization
	await get_tree().create_timer(0.5).timeout
	
	# Configure the loading screen
	if loading_instance.has_method("set_target_scene"):
		loading_instance.set_target_scene("res://Home/scenes/Startmenu.tscn")
		print("✅ Target scene set")
	
	if loading_instance.has_method("set_auto_transition"):
		loading_instance.set_auto_transition(true)
		print("✅ Auto-transition enabled")
	
	# Debug the state
	if loading_instance.has_method("debug_loading_state"):
		loading_instance.debug_loading_state()
	
	# Simulate progress updates
	print("📊 Simulating progress updates...")
	for i in range(0, 101, 25):
		if loading_instance.has_method("update_progress_enhanced"):
			loading_instance.update_progress_enhanced(i, "Testing %d%%" % i)
		await get_tree().create_timer(0.3).timeout
	
	print("✅ Progress simulation completed")
	print("⏳ Waiting for auto-transition...")
	
	# Wait up to 5 seconds for transition
	var start_time = Time.get_ticks_msec()
	var timeout = 5000  # 5 seconds
	
	while Time.get_ticks_msec() - start_time < timeout:
		if not is_instance_valid(loading_instance) or not loading_instance.get_parent():
			print("✅ Loading screen transitioned successfully!")
			return
		await get_tree().create_timer(0.1).timeout
	
	print("❌ Loading screen did not transition within timeout")
	
	# Force completion as fallback
	if is_instance_valid(loading_instance) and loading_instance.has_method("force_completion"):
		print("🚨 Forcing completion...")
		loading_instance.force_completion()

func test_scene_manager_loading():
	"""Test SceneManager loading functionality"""
	print("\n🧪 TESTING SCENE MANAGER LOADING")
	print("="*50)
	
	if not SceneManager:
		print("❌ SceneManager not available")
		return
	
	print("✅ SceneManager available")
	
	# Test going to a simple scene
	var test_scene = "res://Home/scenes/Startmenu.tscn"
	if ResourceLoader.exists(test_scene):
		print("🎯 Testing scene transition to: %s" % test_scene)
		SceneManager.goto_scene(test_scene)
		print("✅ Scene transition initiated")
	else:
		print("❌ Test scene not found: %s" % test_scene)

func test_emergency_fix():
	"""Test emergency loading fix functionality"""
	print("\n🧪 TESTING EMERGENCY LOADING FIX")
	print("="*50)
	
	# Load emergency fix
	var emergency_fix_script = preload("res://utils/scripts/emergency_loading_fix.gd")
	if not emergency_fix_script:
		print("❌ Emergency fix script not found")
		return
	
	var emergency_fix = emergency_fix_script.new()
	add_child(emergency_fix)
	
	print("✅ Emergency fix loaded")
	
	# Test debug function
	emergency_fix.debug_loading_screens()
	
	# Test manual fix
	emergency_fix.force_fix_all_loading_screens()
	
	print("✅ Emergency fix test completed")

func run_all_tests():
	"""Run all loading screen tests"""
	print("\n🚀 RUNNING ALL LOADING SCREEN TESTS")
	print("="*60)
	
	await test_loading_screen_completion()
	await get_tree().create_timer(1.0).timeout
	
	await test_scene_manager_loading()
	await get_tree().create_timer(1.0).timeout
	
	await test_emergency_fix()
	
	print("\n🎉 ALL TESTS COMPLETED")

# Quick test functions for console use
func quick_test():
	"""Quick test of loading screen"""
	print("🚀 Quick loading screen test...")
	test_loading_screen_completion()

func quick_fix():
	"""Quick fix for stuck loading screens"""
	print("🚨 Quick fix for loading screens...")
	var emergency_fix_script = preload("res://utils/scripts/emergency_loading_fix.gd")
	if emergency_fix_script:
		var emergency_fix = emergency_fix_script.new()
		add_child(emergency_fix)
		emergency_fix.force_fix_all_loading_screens()
	else:
		print("❌ Emergency fix not available")

func goto_main_menu():
	"""Go to main menu directly"""
	print("🏠 Going to main menu...")
	get_tree().change_scene_to_file("res://Home/scenes/Startmenu.tscn")

# Input handling for quick access
func _input(event):
	if event is InputEventKey and event.pressed:
		# F1 = Quick test
		if event.keycode == KEY_F1:
			quick_test()
		
		# F2 = Quick fix
		elif event.keycode == KEY_F2:
			quick_fix()
		
		# F3 = Go to main menu
		elif event.keycode == KEY_F3:
			goto_main_menu()
		
		# F4 = Run all tests
		elif event.keycode == KEY_F4:
			run_all_tests()
