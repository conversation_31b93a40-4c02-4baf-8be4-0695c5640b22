[gd_resource type="SpriteFrames" load_steps=8 format=3 uid="uid://f5epyyes21yh"]

[ext_resource type="Texture2D" uid="uid://b0l3a7wpvw2hj" path="res://assets/images/characters/npcs/lang_bam.png" id="1_4js4o"]

[sub_resource type="AtlasTexture" id="AtlasTexture_g7xoy"]
atlas = ExtResource("1_4js4o")
region = Rect2(0, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_22ul4"]
atlas = ExtResource("1_4js4o")
region = Rect2(129, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_y75yq"]
atlas = ExtResource("1_4js4o")
region = Rect2(258, 0, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_crvch"]
atlas = ExtResource("1_4js4o")
region = Rect2(0, 130, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_mp4uc"]
atlas = ExtResource("1_4js4o")
region = Rect2(129, 130, 129, 130)

[sub_resource type="AtlasTexture" id="AtlasTexture_sdqoa"]
atlas = ExtResource("1_4js4o")
region = Rect2(258, 130, 129, 130)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_g7xoy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_22ul4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y75yq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_crvch")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mp4uc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sdqoa")
}],
"loop": true,
"name": &"idle",
"speed": 10.0
}]
