extends Node

# Singleton for handling damage effects
# This script provides utility functions for creating damage effects

# Preload the damage number scene
var damage_number_manager = preload("res://ui/scripts/damage_number.gd").new()

# Preload particle effects
var hit_effect_scene = preload("res://effects/scenes/hit_effect.tscn") if ResourceLoader.exists("res://effects/scenes/hit_effect.tscn") else null

# Create damage number at position
func create_damage_number(position: Vector2, value: float, is_critical: bool = false, color: Color = Color.WHITE) -> void:
	# Check if we have a valid current scene
	if not get_tree() or not get_tree().current_scene:
		print("WARNING: Cannot create damage number - current_scene is null")
		return

	# Use the damage number manager to create a damage number
	damage_number_manager.create_damage_number(position, value, is_critical, color)

# Create hit effect at position
func create_hit_effect(position: Vector2) -> void:
	# Check if we have a valid current scene
	var current_scene = get_tree().current_scene
	if not current_scene:
		print("WARNING: Cannot create hit effect - current_scene is null")
		return

	if hit_effect_scene:
		var effect = hit_effect_scene.instantiate()
		effect.position = position
		current_scene.add_child(effect)
	else:
		# If hit effect scene doesn't exist, create a simple particle effect
		var particles = CPUParticles2D.new()
		particles.position = position
		particles.amount = 10
		particles.lifetime = 0.5
		particles.explosiveness = 1.0
		particles.direction = Vector2(0, -1)
		particles.spread = 45.0
		particles.initial_velocity_min = 50.0
		particles.initial_velocity_max = 100.0
		particles.scale_amount = 2.0
		particles.color = Color(1, 0.5, 0.5)
		particles.emitting = true
		particles.one_shot = true

		# Auto-free after particles are done
		particles.finished.connect(func(): particles.queue_free())

		# Add to current scene if it's valid
		current_scene.add_child(particles)

# Apply screen shake
func apply_screen_shake(intensity: float = 5.0, duration: float = 0.2) -> void:
	# Check if we have a valid viewport
	var viewport = get_viewport()
	if not viewport:
		print("WARNING: Cannot apply screen shake - viewport is null")
		return

	# Find camera
	var camera = viewport.get_camera_2d()
	if not camera:
		print("WARNING: Cannot apply screen shake - camera is null")
		return

	if camera.has_method("shake"):
		camera.shake(intensity, duration)
	else:
		# Simple implementation if camera doesn't have shake method
		var original_position = camera.position
		var tween = camera.create_tween()

		# Shake effect
		for i in range(5):
			var random_offset = Vector2(
				randf_range(-intensity, intensity),
				randf_range(-intensity, intensity)
			)
			tween.tween_property(camera, "position", original_position + random_offset, duration / 5.0)

		# Return to original position
		tween.tween_property(camera, "position", original_position, duration / 5.0)
